# 部门管理系统API测试示例

## 前置条件

1. 确保数据库已执行升级脚本：`db/dept_management_upgrade.sql`
2. 系统已启动并可正常访问
3. 已获取有效的认证token

## 测试流程

### 1. 创建部门结构

#### 1.1 创建一级部门 - 技术中心
```bash
curl -X POST "http://localhost:8080/sys/dept/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "技术中心",
    "deptCode": "TECH_CENTER",
    "parentId": 1,
    "deptType": "NORMAL",
    "description": "负责公司技术研发工作",
    "status": 1,
    "sortOrder": 1,
    "agentIds": [1, 2],
    "defaultAgentId": 2
  }'
```

#### 1.2 创建二级部门 - 前端开发部
```bash
curl -X POST "http://localhost:8080/sys/dept/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "前端开发部",
    "deptCode": "FRONTEND",
    "parentId": TECH_CENTER_ID,
    "deptType": "NORMAL",
    "description": "负责前端技术开发",
    "status": 1,
    "sortOrder": 1,
    "agentIds": [2],
    "defaultAgentId": 2
  }'
```

#### 1.3 创建二级部门 - 后端开发部
```bash
curl -X POST "http://localhost:8080/sys/dept/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "后端开发部",
    "deptCode": "BACKEND",
    "parentId": TECH_CENTER_ID,
    "deptType": "NORMAL",
    "description": "负责后端技术开发",
    "status": 1,
    "sortOrder": 2,
    "agentIds": [1, 2],
    "defaultAgentId": 1
  }'
```

### 2. 用户管理

#### 2.1 创建用户
```bash
curl -X POST "http://localhost:8080/sys/user" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "username": "zhangsan",
    "name": "张三",
    "email": "<EMAIL>",
    "mobile": "13800138001",
    "employeeId": "EMP001",
    "deptId": FRONTEND_DEPT_ID,
    "enable": true
  }'
```

#### 2.2 设置用户部门
```bash
curl -X POST "http://localhost:8080/sys/user/dept/set" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "userId": USER_ID,
    "deptId": FRONTEND_DEPT_ID,
    "isDeptManager": false
  }'
```

#### 2.3 设置部门主管
```bash
curl -X POST "http://localhost:8080/sys/user/dept/manager/set" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "deptId": FRONTEND_DEPT_ID,
    "userId": USER_ID,
    "operationType": "SET"
  }'
```

### 3. 查询操作

#### 3.1 获取部门树结构
```bash
curl -X GET "http://localhost:8080/sys/dept/tree-with-agents?includeAgents=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3.2 获取部门详细信息
```bash
curl -X GET "http://localhost:8080/sys/dept/detail/DEPT_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3.3 查询部门用户
```bash
curl -X GET "http://localhost:8080/sys/user/dept/DEPT_ID?includeChildren=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3.4 分页查询部门用户
```bash
curl -X GET "http://localhost:8080/sys/user/dept/DEPT_ID/page?pageNo=1&pageSize=10&includeChildren=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3.5 获取部门AI智能体
```bash
curl -X GET "http://localhost:8080/sys/dept/DEPT_ID/agents" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3.6 获取部门默认AI智能体
```bash
curl -X GET "http://localhost:8080/sys/dept/DEPT_ID/default-agent" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 批量操作

#### 4.1 批量设置用户部门
```bash
curl -X POST "http://localhost:8080/sys/user/dept/batch-set" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "userIds": [USER_ID1, USER_ID2, USER_ID3],
    "deptId": TARGET_DEPT_ID,
    "operationType": "ADD"
  }'
```

#### 4.2 转移用户到其他部门
```bash
curl -X POST "http://localhost:8080/sys/user/dept/transfer" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "userIds": [USER_ID1, USER_ID2],
    "fromDeptId": SOURCE_DEPT_ID,
    "toDeptId": TARGET_DEPT_ID
  }'
```

### 5. AI智能体管理

#### 5.1 设置部门AI智能体关联
```bash
curl -X POST "http://localhost:8080/sys/dept/DEPT_ID/agents" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "agentIds": [1, 2, 3],
    "defaultAgentId": 2
  }'
```

### 6. 更新操作

#### 6.1 更新部门信息
```bash
curl -X PUT "http://localhost:8080/sys/dept/update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "deptId": DEPT_ID,
    "name": "前端技术部",
    "description": "负责前端技术研发和创新",
    "agentIds": [2, 3],
    "defaultAgentId": 3
  }'
```

### 7. 删除操作

#### 7.1 移除部门主管
```bash
curl -X DELETE "http://localhost:8080/sys/dept/DEPT_ID/manager" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 7.2 从部门移除用户
```bash
curl -X POST "http://localhost:8080/sys/user/dept/remove" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "userIds": [USER_ID1, USER_ID2],
    "deptId": DEPT_ID
  }'
```

## 测试验证

### 1. 验证部门层级结构
- 检查部门树形结构是否正确
- 验证父子关系是否正确建立

### 2. 验证用户部门关系
- 检查用户是否正确归属到指定部门
- 验证部门主管标识是否正确设置

### 3. 验证AI智能体关联
- 检查部门是否正确关联了指定的AI智能体
- 验证默认AI智能体设置是否生效

### 4. 验证权限控制
- 测试不同角色用户的操作权限
- 验证数据访问范围是否正确

## 常见问题排查

### 1. 创建部门失败
- 检查父部门ID是否存在
- 验证部门编码是否重复
- 确认必填字段是否完整

### 2. 设置部门主管失败
- 检查用户是否存在
- 验证用户是否属于该部门
- 确认用户状态是否正常

### 3. AI智能体关联失败
- 检查智能体ID是否存在
- 验证智能体是否启用
- 确认默认智能体是否在关联列表中

### 4. 查询结果异常
- 检查数据库索引是否正确创建
- 验证查询条件是否正确
- 确认数据权限是否正确配置
