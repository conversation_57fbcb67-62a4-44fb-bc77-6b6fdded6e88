package com.coocare.ai.config.i18n;

import com.coocare.ai.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;


/**
 * 多语言国际化消息工具类
 *
 * <AUTHOR>
 * @since 2023-01-01
 */
@Slf4j
@Component
public class I18nMessage {

    /**
     * 多语言字符串转换
     * @param message 多语言表中对应的code
     * @param args 参数
     * @return 转换后的文字
     */
    public static String getMessage(String message, Object... args) {
        MessageSource messageSource = SpringContextUtil.getBean(MessageSource.class);
        try {
            return messageSource.getMessage(message, args, LocaleContextHolder.getLocale());
        } catch (NoSuchMessageException ex) {
            log.error(message + "在多语言文件中不存在", ex);
            return message;
        }
    }

    /**
     * 多语言字符串转换
     * @param message
     * @return 转换后的文字
     */
    public static String getMessage(String message) {
        return getMessage(message, null);
    }

}