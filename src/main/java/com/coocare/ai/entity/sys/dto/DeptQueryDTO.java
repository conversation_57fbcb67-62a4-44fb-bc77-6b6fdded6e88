package com.coocare.ai.entity.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 部门查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "部门查询DTO")
public class DeptQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "部门名称（模糊查询）")
    private String name;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "父部门ID")
    private Long parentId;

    @Schema(description = "部门主管用户ID")
    private Long managerUserId;

    @Schema(description = "是否包含子部门")
    private Boolean includeChildren = false;

    @Schema(description = "页码", example = "1")
    private Integer pageNo = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
}
