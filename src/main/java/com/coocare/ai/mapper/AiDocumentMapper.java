package com.coocare.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocare.ai.entity.AiDocument;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * AI文档 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface AiDocumentMapper extends BaseMapper<AiDocument> {

    /**
     * 根据产品系列ID查询所有文档（包含分类信息）
     *
     * @param productSeriesId 产品系列ID
     * @return 文档列表
     */
    List<AiDocument> selectDocumentsByProductSeriesId(@Param("productSeriesId") Long productSeriesId);

    /**
     * 根据产品系列ID和分类编码查询文档
     *
     * @param productSeriesId 产品系列ID
     * @param categoryCode    分类编码
     * @return 文档列表
     */
    List<AiDocument> selectDocumentsByProductSeriesAndCategory(@Param("productSeriesId") Long productSeriesId,
                                                               @Param("categoryCode") String categoryCode);

    /**
     * 根据分类ID查询文档
     *
     * @param categoryId 分类ID
     * @return 文档列表
     */
    List<AiDocument> selectDocumentsByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 根据产品型号ID查询所有文档
     *
     * @param productModelId 产品型号ID
     * @return 文档列表
     */
    List<AiDocument> selectDocumentsByProductModelId(@Param("productModelId") Long productModelId);

    /**
     * 查询文档详情（包含分类信息）
     *
     * @param documentId 文档ID
     * @return 文档详情
     */
    AiDocument selectDocumentDetailById(@Param("documentId") Long documentId);

    /**
     * 根据文件名查询文档
     *
     * @param documentName 文档名称
     * @param categoryId   分类ID
     * @return 文档信息
     */
    AiDocument selectByNameAndCategoryId(@Param("documentName") String documentName,
                                         @Param("categoryId") Long categoryId);

    /**
     * 统计分类下的文档数量
     *
     * @param categoryId 分类ID
     * @return 文档数量
     */
    int countDocumentsByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 统计产品系列下的文档数量
     *
     * @param productSeriesId 产品系列ID
     * @return 文档数量
     */
    int countDocumentsByProductSeriesId(@Param("productSeriesId") Long productSeriesId);

    /**
     * 更新文档下载次数
     *
     * @param documentId 文档ID
     * @return 更新结果
     */
    int incrementDownloadCount(@Param("documentId") Long documentId);

    /**
     * 更新文档查看次数
     *
     * @param documentId 文档ID
     * @return 更新结果
     */
    int incrementViewCount(@Param("documentId") Long documentId);

    /**
     * 批量更新文档状态
     *
     * @param documentIds 文档ID列表
     * @param status      状态
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("documentIds") List<Long> documentIds, @Param("status") String status);

    /**
     * 根据产品系列ID分组查询文档统计信息
     *
     * @param productSeriesId 产品系列ID
     * @return 分组统计结果
     */
    List<AiDocument> selectDocumentGroupStatsByProductSeriesId(@Param("productSeriesId") Long productSeriesId);
}
