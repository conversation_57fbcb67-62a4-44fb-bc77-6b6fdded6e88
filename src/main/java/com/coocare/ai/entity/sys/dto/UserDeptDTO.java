package com.coocare.ai.entity.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 用户部门管理DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "用户部门管理DTO")
public class UserDeptDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "部门ID", required = true)
    @NotNull(message = "部门ID不能为空")
    private Long deptId;

    @Schema(description = "是否设为部门主管")
    private Boolean isDeptManager = false;

    @Schema(description = "批量用户ID列表")
    private List<Long> userIds;

    @Schema(description = "操作类型：ADD-添加，REMOVE-移除，TRANSFER-转移")
    private String operationType;
}
