package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.AiAgent;

import java.util.List;

/**
 * AI智能体 服务类
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface AiAgentService extends IService<AiAgent>{

    /**
     * 获取所有可用的智能体列表
     * @return 智能体列表
     */
    List<AiAgent> getAllAvailableAgents();

    /**
     * 获取已启用的智能体列表
     * @return 已启用的智能体ID列表
     */
    List<Long> getEnabledAgents();

    /**
     * 获取默认智能体
     * @return 默认智能体名称
     */
    String getDefaultAgent();

    /**
     * 获取已启用的智能体详细信息列表
     * @return 已启用的智能体详细信息列表
     */
    List<AiAgent> getEnabledAgentDetails();

    /**
     * 设置部门AI智能体关联
     * @param deptId 部门ID
     * @param agentIds AI智能体ID列表
     * @param defaultAgentId 默认AI智能体ID
     * @return 设置结果
     */
    boolean setDeptAgents(Long deptId, List<Long> agentIds, Long defaultAgentId);

    /**
     * 获取部门关联的AI智能体列表
     * @param deptId 部门ID
     * @return AI智能体列表
     */
    List<AiAgent> getDeptAgents(Long deptId);
}
