//package com.coocare.ai.controller;
//
//import com.coocare.ai.config.domain.AjaxResult;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.util.LinkedMultiValueMap;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.Map;
//
///**
// * OAuth2 认证控制器
// *
// * <AUTHOR>
// */
//@Tag(name = "OAuth2 认证")
//@RestController
//@RequestMapping("/oauth22222")
//@RequiredArgsConstructor
//@Slf4j
//public class OAuth2Controller {
//
//    private final RestTemplate restTemplate = new RestTemplate();
//
//    @Operation(summary = "密码登录获取 Token")
//    @PostMapping("/token/password")
//    public AjaxResult<Map<String, Object>> passwordLogin(
//            @Parameter(description = "用户名") @RequestParam String username,
//            @Parameter(description = "密码") @RequestParam String password,
//            @Parameter(description = "客户端ID") @RequestParam(defaultValue = "genius") String clientId,
//            @Parameter(description = "客户端密钥") @RequestParam(defaultValue = "genius") String clientSecret) {
//
//        try {
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
//            headers.setBasicAuth(clientId, clientSecret);
//
//            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
//            params.add("grant_type", "password");
//            params.add("username", username);
//            params.add("password", password);
//
//            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
//
//            ResponseEntity<Map> response = restTemplate.exchange(
//                    "http://localhost:8070/oauth2/token",
//                    HttpMethod.POST,
//                    request,
//                    Map.class
//            );
//
//            log.info("密码登录成功，用户：{}", username);
//            return AjaxResult.ok(response.getBody());
//
//        } catch (Exception e) {
//            log.error("密码登录失败，用户：{}，错误：{}", username, e.getMessage());
//            return AjaxResult.failed("登录失败：" + e.getMessage());
//        }
//    }
//
//    @Operation(summary = "邮箱验证码登录获取 Token")
//    @PostMapping("/token/email")
//    public AjaxResult<Map<String, Object>> emailLogin(
//            @Parameter(description = "邮箱") @RequestParam String email,
//            @Parameter(description = "验证码") @RequestParam String emailCode,
//            @Parameter(description = "客户端ID") @RequestParam(defaultValue = "genius") String clientId,
//            @Parameter(description = "客户端密钥") @RequestParam(defaultValue = "genius") String clientSecret) {
//
//        try {
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
//            headers.setBasicAuth(clientId, clientSecret);
//
//            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
//            params.add("grant_type", "urn:ietf:params:oauth:grant-type:email_code");
//            params.add("email", email);
//            params.add("email_captcha", emailCode);
//
//            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
//
//            ResponseEntity<Map> response = restTemplate.exchange(
//                    "http://localhost:8070/oauth2/token",
//                    HttpMethod.POST,
//                    request,
//                    Map.class
//            );
//
//            log.info("邮箱验证码登录成功，邮箱：{}", email);
//            return AjaxResult.ok(response.getBody());
//
//        } catch (Exception e) {
//            log.error("邮箱验证码登录失败，邮箱：{}，错误：{}", email, e.getMessage());
//            return AjaxResult.failed("登录失败：" + e.getMessage());
//        }
//    }
//
//    @Operation(summary = "短信验证码登录获取 Token")
//    @PostMapping("/token/sms")
//    public AjaxResult<Map<String, Object>> smsLogin(
//            @Parameter(description = "手机号") @RequestParam String mobile,
//            @Parameter(description = "验证码") @RequestParam String smsCode,
//            @Parameter(description = "客户端ID") @RequestParam(defaultValue = "genius") String clientId,
//            @Parameter(description = "客户端密钥") @RequestParam(defaultValue = "genius") String clientSecret) {
//
//        try {
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
//            headers.setBasicAuth(clientId, clientSecret);
//
//            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
//            params.add("grant_type", "mobile");
//            params.add("mobile", mobile);
//            params.add("code", smsCode);
//
//            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
//
//            ResponseEntity<Map> response = restTemplate.exchange(
//                    "http://localhost:8070/oauth2/token",
//                    HttpMethod.POST,
//                    request,
//                    Map.class
//            );
//
//            log.info("短信验证码登录成功，手机号：{}", mobile);
//            return AjaxResult.ok(response.getBody());
//
//        } catch (Exception e) {
//            log.error("短信验证码登录失败，手机号：{}，错误：{}", mobile, e.getMessage());
//            return AjaxResult.failed("登录失败：" + e.getMessage());
//        }
//    }
//
//    @Operation(summary = "刷新 Token")
//    @PostMapping("/token/refresh")
//    public AjaxResult<Map<String, Object>> refreshToken(
//            @Parameter(description = "刷新令牌") @RequestParam String refreshToken,
//            @Parameter(description = "客户端ID") @RequestParam(defaultValue = "genius") String clientId,
//            @Parameter(description = "客户端密钥") @RequestParam(defaultValue = "genius") String clientSecret) {
//
//        try {
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
//            headers.setBasicAuth(clientId, clientSecret);
//
//            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
//            params.add("grant_type", "refresh_token");
//            params.add("refresh_token", refreshToken);
//
//            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
//
//            ResponseEntity<Map> response = restTemplate.exchange(
//                    "http://localhost:8070/oauth2/token",
//                    HttpMethod.POST,
//                    request,
//                    Map.class
//            );
//
//            log.info("Token 刷新成功");
//            return AjaxResult.ok(response.getBody());
//
//        } catch (Exception e) {
//            log.error("Token 刷新失败，错误：{}", e.getMessage());
//            return AjaxResult.failed("刷新失败：" + e.getMessage());
//        }
//    }
//}
