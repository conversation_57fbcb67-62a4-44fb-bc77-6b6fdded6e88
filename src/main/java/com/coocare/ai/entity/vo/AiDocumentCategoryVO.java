package com.coocare.ai.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * AI文档分类响应VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "AI文档分类响应VO")
public class AiDocumentCategoryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "分类编码")
    private String categoryCode;

    @Schema(description = "父级分类ID")
    private Long parentId;

    @Schema(description = "父级分类名称")
    private String parentName;

    @Schema(description = "分类层级：1-产品型号，2-产品系列，3-文档类型")
    private Integer level;

    @Schema(description = "分类层级名称")
    private String levelName;

    @Schema(description = "排序号")
    private Integer sortOrder;

    @Schema(description = "分类描述")
    private String description;

    @Schema(description = "分类图标")
    private String icon;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "是否系统分类：0-否，1-是")
    private Integer isSystem;

    @Schema(description = "是否系统分类名称")
    private String isSystemName;

    @Schema(description = "分类完整路径")
    private String categoryPath;

    @Schema(description = "是否有子分类")
    private Boolean hasChildren;

    @Schema(description = "子分类数量")
    private Integer childrenCount;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "子分类列表")
    private List<AiDocumentCategoryVO> children;

    /**
     * 获取层级名称
     */
    public String getLevelName() {
        if (level == null) {
            return "";
        }
        switch (level) {
            case 1:
                return "产品型号";
            case 2:
                return "产品系列";
            case 3:
                return "文档类型";
            default:
                return "未知";
        }
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "";
        }
        return status == 1 ? "启用" : "禁用";
    }

    /**
     * 获取是否系统分类名称
     */
    public String getIsSystemName() {
        if (isSystem == null) {
            return "";
        }
        return isSystem == 1 ? "是" : "否";
    }
}
