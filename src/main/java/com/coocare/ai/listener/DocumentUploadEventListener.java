package com.coocare.ai.listener;

import com.coocare.ai.dify.DatasetService;
import com.coocare.ai.event.DocumentUploadEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 文档上传事件监听器
 * 监听文档上传完成事件，触发异步回调通知
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DocumentUploadEventListener {

    //    private final DataSetUtils dataSetUtils;
    private final DatasetService datasetService;

    /**
     * 监听文档上传事件
     *
     * @param event 文档上传事件
     */
    @EventListener(classes = DocumentUploadEvent.class)
    @Async("taskExecutor")
    public void handleDocumentUploadEvent(DocumentUploadEvent event) {
        log.info("接收到文档上传事件: {}", event.getOperationDescription());
        try {
            // 委托给回调服务处理
            datasetService.handleFileToDataSet(event);
        } catch (Exception e) {
            log.error("处理文档上传事件失败", e);
        }
    }
}