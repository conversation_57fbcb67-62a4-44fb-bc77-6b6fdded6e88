package com.coocare.ai.config.doc;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.configuration.SpringDocConfiguration;
import org.springdoc.core.customizers.GlobalOpenApiCustomizer;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;

/**
 * OpenAPI/Swagger 文档配置
 * 配置全局鉴权头、接口分组与 operationId 标准化
 *
 * <AUTHOR>
 * @since 2023-03-14
 */

@Configuration
@AutoConfigureBefore(SpringDocConfiguration.class)
public class SwaggerConfiguration {

    @Value("${spring.application.name}")
    private String applicationName;

    @Bean
    public OpenAPI springShopOpenAPI() {
        return new OpenAPI()
                .info(new Info().title(applicationName)
                        .description(applicationName + " API文档")
                        .version("v1")
                        .license(new License().name("Apache 2.0")
                                .url("")))
                .components(new Components()
                        .addSecuritySchemes(HttpHeaders.AUTHORIZATION,
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.APIKEY)
                                        .scheme("Bearer")
                                        .bearerFormat("JWT")
                        ).addParameters(HttpHeaders.AUTHORIZATION,
                                new Parameter()
                                        .in("header")
                                        .schema(new StringSchema())
                                        .name(HttpHeaders.AUTHORIZATION)
                        )
                );
    }

    @Bean
    public GlobalOpenApiCustomizer orderGlobalOpenApiCustomizer() {
        return openApi -> {
            // 全局添加鉴权参数，并为缺失 operationId 的接口自动生成唯一 ID
            if (openApi.getPaths() != null) {
                openApi.getPaths().forEach((s, pathItem) -> {
                    // 规范化路径一次
                    String normalizedPath = s.startsWith("/") ? s.substring(1) : s;
                    normalizedPath = normalizedPath.replace("/", "_")
                                                   .replace("{", "")
                                                   .replace("}", "");

                    // 逐个 HTTP 方法处理，强制覆盖/设置 operationId
                    if (pathItem.getGet() != null) {
                        pathItem.getGet().addSecurityItem(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION));
                        pathItem.getGet().setOperationId(("get_" + normalizedPath).toLowerCase());
                    }
                    if (pathItem.getPost() != null) {
                        pathItem.getPost().addSecurityItem(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION));
                        pathItem.getPost().setOperationId(("post_" + normalizedPath).toLowerCase());
                    }
                    if (pathItem.getPut() != null) {
                        pathItem.getPut().addSecurityItem(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION));
                        pathItem.getPut().setOperationId(("put_" + normalizedPath).toLowerCase());
                    }
                    if (pathItem.getDelete() != null) {
                        pathItem.getDelete().addSecurityItem(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION));
                        pathItem.getDelete().setOperationId(("delete_" + normalizedPath).toLowerCase());
                    }
                    if (pathItem.getPatch() != null) {
                        pathItem.getPatch().addSecurityItem(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION));
                        pathItem.getPatch().setOperationId(("patch_" + normalizedPath).toLowerCase());
                    }
                    if (pathItem.getHead() != null) {
                        pathItem.getHead().addSecurityItem(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION));
                        pathItem.getHead().setOperationId(("head_" + normalizedPath).toLowerCase());
                    }
                    if (pathItem.getOptions() != null) {
                        pathItem.getOptions().addSecurityItem(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION));
                        pathItem.getOptions().setOperationId(("options_" + normalizedPath).toLowerCase());
                    }
                    if (pathItem.getTrace() != null) {
                        pathItem.getTrace().addSecurityItem(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION));
                        pathItem.getTrace().setOperationId(("trace_" + normalizedPath).toLowerCase());
                    }
                });
            }

        };
    }

    @Bean
    public GroupedOpenApi defaultApi() {
        return GroupedOpenApi.builder()
                .group("全部接口")
                .pathsToMatch("/**")
                .build();
    }

    @Bean
    public GroupedOpenApi systemApi() {
        return GroupedOpenApi.builder()
                .group("系统管理接口")
                .pathsToMatch("/sys/**")
                .build();
    }

    @Bean
    public GroupedOpenApi aiAgentApi() {
        return GroupedOpenApi.builder()
                .group("智能体管理接口")
                .pathsToMatch("/ai/**")
                .build();
    }

}
