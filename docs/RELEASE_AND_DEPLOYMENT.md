# 发布与部署流程

## 流程
1. 分支合并与版本打标
2. 构建：mvn -DskipTests package
3. 静态检查与单元测试
4. 镜像构建与签名
5. 部署到测试环境 -> 冒烟 -> 预发 -> 生产

## 发布策略
- 灰度/金丝雀：按比例将流量导向新版本
- 回滚：保留上一个稳定镜像与回滚脚本
- 配置变更：优先使用动态配置与灰度开关

## 健康检查
- /actuator/health、/actuator/info
- 启动就绪探针：延迟 readiness，避免冷启动失败

## 变更管理
- 变更评审、发布窗口与冻结期
- 变更记录（Changelog）与回滚说明


## CI/CD 示例

### GitHub Actions（.github/workflows/ci.yml）

```yaml
name: CI
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 17
      - name: <PERSON><PERSON> Maven
        uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
      - name: Build
        run: mvn -B -DskipTests package
      - name: Unit Tests
        run: mvn -B test
      - name: Docker Login
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.DOCKER_REGISTRY }}
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      - name: Build and Push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile
          push: true
          tags: ${{ secrets.DOCKER_REGISTRY }}/zhan-ai-enterprise:${{ github.sha }}
```

### Jenkins Declarative Pipeline（Jenkinsfile）

```groovy
pipeline {
  agent any
  environment {
    REGISTRY = 'registry.example.com'
    IMAGE = "${env.REGISTRY}/zhan-ai-enterprise:${env.GIT_COMMIT}"
  }
  stages {
    stage('Checkout') { steps { checkout scm } }
    stage('Build') { steps { sh 'mvn -DskipTests package' } }
    stage('Test') { steps { sh 'mvn test' } }
    stage('Docker Build') { steps { sh 'docker build -t ${IMAGE} .' } }
    stage('Docker Push') { steps { sh 'docker push ${IMAGE}' } }
    stage('Deploy Staging') { steps { sh 'kubectl set image deploy/zhan-ai zhan-ai=${IMAGE} -n staging' } }
    stage('Smoke Test') { steps { sh 'curl -fsS http://staging.example.com/actuator/health' } }
    stage('Promote Prod?') { steps { input message: 'Promote to production?' } }
    stage('Deploy Prod') { steps { sh 'kubectl set image deploy/zhan-ai zhan-ai=${IMAGE} -n prod' } }
  }
}
```
