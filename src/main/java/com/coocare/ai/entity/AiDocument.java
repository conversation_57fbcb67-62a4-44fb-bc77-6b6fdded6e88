package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * AI文档实体类
 * 用于存储上传到第三层分类的文档信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_document")
@Schema(description = "AI文档管理")
public class AiDocument implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "文档ID")
    @TableId(value = "document_id", type = IdType.ASSIGN_ID)
    private Long documentId;

    @Schema(description = "后端对应ID")
    @TableField(value = "backend_id")
    private String backendId;

    @Schema(description = "数据集对应ID")
    @TableField(value = "dataset_id")
    private String datasetId;

    @Schema(description = "文档名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文档名称不能为空")
    @Size(max = 200, message = "文档名称长度不能超过200个字符")
    @TableField(value = "document_name")
    private String documentName;

    @Schema(description = "原始文件名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "原始文件名不能为空")
    @Size(max = 200, message = "原始文件名长度不能超过200个字符")
    @TableField(value = "original_name")
    private String originalName;

    @Schema(description = "文件存储路径")
    @NotBlank(message = "文件存储路径不能为空")
    @Size(max = 500, message = "文件存储路径长度不能超过500个字符")
    @TableField(value = "file_path")
    private String filePath;

    @Schema(description = "文件访问URL")
    @Size(max = 500, message = "文件访问URL长度不能超过500个字符")
    @TableField(value = "file_url")
    private String fileUrl;

    @Schema(description = "文件大小（字节）")
    @TableField(value = "file_size")
    private Long fileSize;

    @Schema(description = "文件类型/扩展名")
    @Size(max = 50, message = "文件类型长度不能超过50个字符")
    @TableField(value = "file_type")
    private String fileType;

    @Schema(description = "MIME类型")
    @Size(max = 100, message = "MIME类型长度不能超过100个字符")
    @TableField(value = "mime_type")
    private String mimeType;

    @Schema(description = "所属分类ID（第三层分类）")
    @NotNull(message = "所属分类ID不能为空")
    @TableField(value = "category_id")
    private Long categoryId;

    @Schema(description = "所属产品系列ID（第二层分类）")
    @NotNull(message = "所属产品系列ID不能为空")
    @TableField(value = "product_series_id")
    private Long productSeriesId;

    @Schema(description = "所属产品型号ID（第一层分类）")
    @NotNull(message = "所属产品型号ID不能为空")
    @TableField(value = "product_model_id")
    private Long productModelId;

    @Schema(description = "分类编码（冗余字段，便于查询）")
    @NotBlank(message = "分类编码不能为空")
    @Size(max = 50, message = "分类编码长度不能超过50个字符")
    @TableField(value = "category_code")
    private String categoryCode;

    @Schema(description = "文档描述")
    @Size(max = 1000, message = "文档描述长度不能超过1000个字符")
    @TableField(value = "description")
    private String description;

    @Schema(description = "文档标签（JSON格式）")
    @Size(max = 500, message = "文档标签长度不能超过500个字符")
    @TableField(value = "tags")
    private String tags;

    @Schema(description = "文档版本")
    @Size(max = 20, message = "文档版本长度不能超过20个字符")
    @TableField(value = "version")
    private String version;

    @Schema(description = "下载次数")
    @TableField(value = "download_count")
    private Integer downloadCount;

    @Schema(description = "查看次数")
    @TableField(value = "view_count")
    private Integer viewCount;

    @Schema(description = "状态：enable=启用 disable=禁用 archive=归档 un_archive=取消归档")
    @TableField(value = "status")
    private String status;

    @Schema(description = "索引状态")
    @TableField(value = "indexing_status")
    private String indexingStatus;

    @Schema(description = "排序号")
    @TableField(value = "sort_order")
    private Integer sortOrder;

    @Schema(description = "上传人")
    @TableField(value = "upload_by", fill = FieldFill.INSERT)
    private String uploadBy;

    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.UPDATE)
    private String updateBy;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "删除标志：0-正常，1-删除")
    @TableField(value = "del_flag")
    @TableLogic
    private String delFlag;

    // 非数据库字段
    @Schema(description = "分类名称")
    @TableField(exist = false)
    private String categoryName;
    @Schema(description = "产品系列名称")
    @TableField(exist = false)
    private String productSeriesName;
    @Schema(description = "产品型号名称")
    @TableField(exist = false)
    private String productModelName;
    @Schema(description = "文件大小（格式化显示）")
    @TableField(exist = false)
    private String fileSizeFormatted;

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "";
        }
        return switch (status) {
            case "disable" -> "禁用";
            case "enable" -> "启用";
            case "archive" -> "归档文档";
            case "un_archive" -> "取消归档";
            default -> "未知";
        };
    }

    /**
     * 获取索引状态名称
     */
    public String getIndexingStatus() {
        switch (indexingStatus) {
            case "waiting" -> {
                return  "等待中";
            }
            case "indexing" -> {
                return  "索引中";
            }
            case "success" -> {
                return  "索引成功";
            }
            case "failed" -> {
                return  "索引失败";
            }
            default -> {
                return  "未知";
            }
        }
    }

    /**
     * 格式化文件大小
     */
    public String getFileSizeFormatted() {
        if (fileSize == null || fileSize == 0) {
            return "0 B";
        }

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.2f %s", size, units[unitIndex]);
    }
}
