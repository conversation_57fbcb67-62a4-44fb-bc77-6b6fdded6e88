package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI 数据集表
 */
@Schema(description = "AI 数据集表")
@Data
@TableName(value = "ai_dataset")
public class AiDataset implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 数据集ID
     */
    @TableId(value = "dataset_id", type = IdType.ASSIGN_ID)
    @Schema(description = "数据集ID")
    private Long datasetId;

    /**
     * 数据集标题
     */
    @TableField(value = "title")
    @Schema(description = "数据集标题")
    private String title;

    /**
     * 索引方式
     */
    @TableField(value = "indexing_technique")
    @Schema(description = "索引方式")
    private String indexingTechnique;

    /**
     * 处理规则
     */
    @TableField(value = "process_rule")
    @Schema(description = "处理规则")
    private String processRule;

    /**
     * 索引内容形式
     */
    @TableField(value = "doc_form")
    @Schema(description = "索引内容形式")
    private String docForm;

    /**
     * 检索模式
     */
    @TableField(value = "retrieval_model")
    @Schema(description = "检索模式")
    private Integer retrievalModel;

    /**
     * 后端实际ID
     */
    @TableField(value = "backend_id")
    @Schema(description = "后端实际ID")
    private String backendId;

    /**
     * 数据集类型：DOCUMENT-文档类，QA-问答类
     */
    @TableField(value = "dataset_type")
    @Schema(description = "数据集类型：DOCUMENT-文档类，QA-问答类")
    private String datasetType;

    /**
     * 可选关联分类ID（非硬性关联）
     */
    @TableField(value = "category_id")
    @Schema(description = "可选关联分类ID（非硬性关联）")
    private Long categoryId;

    /**
     * 是否启用：0-禁用，1-启用
     */
    @TableField(value = "`enable`")
    @Schema(description = "是否启用：0-禁用，1-启用")
    private Byte enable;

    /**
     * 关联的智能体ID
     */
    @TableField(value = "agent_id")
    @Schema(description = "关联的智能体ID")
    private Long agentId;

    /**
     * 排序号
     */
    @TableField(value = "sort_order")
    @Schema(description = "排序号")
    private Integer sortOrder;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标志：0-正常，1-删除
     */
    @TableField(value = "del_flag")
    @Schema(description = "删除标志：0-正常，1-删除")
    private String delFlag;
}