package com.coocare.ai.entity.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 部门主管设置DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "部门主管设置DTO")
public class DeptManagerDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "部门ID", required = true)
    @NotNull(message = "部门ID不能为空")
    private Long deptId;

    @Schema(description = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "操作类型：SET-设置，REMOVE-移除")
    private String operationType = "SET";
}
