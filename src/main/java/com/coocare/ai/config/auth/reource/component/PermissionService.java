
/*
 * Copyright (c) 2020 housekeeper Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.coocare.ai.config.auth.reource.component;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthentication;
import org.springframework.util.PatternMatchUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
/**
 * 接口权限判断工具
 * 提供 hasPermission 与 hasScope 两类权限判定方法
 *
 * <AUTHOR>
 * @since 2019-02-01
 */

public class PermissionService {

	/**
	 * 判断接口是否有任意xxx，xxx权限
	 * @param permissions 权限
	 * @return {boolean}
	 */
	public boolean hasPermission(String... permissions) {
		if (ArrayUtil.isEmpty(permissions)) {
			return false;
		}
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		if (authentication == null) {
			return false;
		}
		Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
		return authorities.stream()
			.map(GrantedAuthority::getAuthority)
			.filter(StringUtils::hasText)
			.anyMatch(x -> PatternMatchUtils.simpleMatch(permissions, x));
	}

	/**
	 * 客户端模式是否有该接口权限
	 *
	 * @param scopes scope 列表
	 * @return {boolean}
	 * @PreAuthorize("@pms.hasScope('server')")
	 */
	public boolean hasScope(String... scopes) {
		if (ArrayUtil.isEmpty(scopes)) {
			return false;
		}

		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

		if (authentication == null) {
			return false;
		}


		if (authentication instanceof BearerTokenAuthentication) {
			BearerTokenAuthentication bearerTokenAuthentication = (BearerTokenAuthentication) authentication;
			List<String> scopeList = MapUtil.get(bearerTokenAuthentication.getTokenAttributes()
					, OAuth2ParameterNames.SCOPE, List.class, new ArrayList<>());
			return scopeList.stream().anyMatch(x -> PatternMatchUtils.simpleMatch(scopes, x));
		}
		return false;
	}

}
