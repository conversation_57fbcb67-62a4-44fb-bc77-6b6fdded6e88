# CI 质量门槛（Quality Gates）

## 覆盖率（JaCoCo）
- 语句覆盖（LINE）：>= 70%
- 分支覆盖（BRANCH）：>= 60%
- 关键包（service、utils）应优先满足

Maven 配置示例（pom.xml）：
```xml
<plugin>
  <groupId>org.jacoco</groupId>
  <artifactId>jacoco-maven-plugin</artifactId>
  <version>0.8.11</version>
  <executions>
    <execution>
      <goals>
        <goal>prepare-agent</goal>
      </goals>
    </execution>
    <execution>
      <id>report</id>
      <phase>test</phase>
      <goals>
        <goal>report</goal>
      </goals>
    </execution>
    <execution>
      <id>check</id>
      <phase>verify</phase>
      <goals>
        <goal>check</goal>
      </goals>
      <configuration>
        <rules>
          <rule>
            <element>BUNDLE</element>
            <limits>
              <limit>
                <counter>LINE</counter>
                <value>COVEREDRATIO</value>
                <minimum>0.70</minimum>
              </limit>
            </limits>
          </rule>
        </rules>
      </configuration>
    </execution>
  </executions>
</plugin>
```

## 静态检查
- SpotBugs、Checkstyle、PMD（可选）
- 基线：CI 必须无 Blocker/Critical 级别问题

## GitHub Actions 执行门槛示例
- 在 CI 流程中加入 `mvn verify` 并以 JaCoCo check 作为门槛

```yaml
- name: Test & Verify
  run: mvn -B test verify
```

## Jenkins 门槛
- 在 Jenkinsfile 中增加 `mvn verify` 阶段，失败即阻断流水

```groovy
stage('Verify') { steps { sh 'mvn -B test verify' } }
```

## 建议
- 将关键业务（初始化、鉴权、上传）的单元/集成测试纳入必测范围
- 对外接口使用 Spring MVC Test/RestAssured 做冒烟回归

