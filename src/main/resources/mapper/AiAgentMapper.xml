<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AiAgentMapper">
  <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AiAgent">
    <!--@mbg.generated-->
    <!--@Table ai_agent-->
    <id column="agent_id" jdbcType="BIGINT" property="agentId" />
    <result column="agent_name" jdbcType="VARCHAR" property="agentName" />
    <result column="agent_intro" jdbcType="VARCHAR" property="agentIntro" />
    <result column="agent_description" jdbcType="LONGVARCHAR" property="agentDescription" />
    <result column="agent_type" jdbcType="VARCHAR" property="agentType" />
    <result column="agent_config" jdbcType="LONGVARCHAR" property="agentConfig" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    agent_id, agent_name, agent_intro, agent_description, agent_type, agent_config, `enable`, 
    sort_order, create_by, create_time, update_by, update_time, del_flag
  </sql>
</mapper>