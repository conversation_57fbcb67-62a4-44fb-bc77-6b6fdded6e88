package com.coocare.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocare.ai.entity.AiChatStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * AI对话统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Mapper
public interface AiChatStatisticsMapper extends BaseMapper<AiChatStatistics> {

    /**
     * 根据时间范围统计token使用量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return token统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as conversation_count, " +
            "SUM(total_tokens) as total_tokens, " +
            "SUM(input_tokens) as total_input_tokens, " +
            "SUM(output_tokens) as total_output_tokens, " +
            "AVG(response_time) as avg_response_time " +
            "FROM ai_chat_statistics " +
            "WHERE create_time BETWEEN #{startTime} AND #{endTime} " +
            "AND del_flag = '0'")
    Map<String, Object> getTokenStatistics(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 根据智能体ID统计使用情况
     *
     * @param agentId   智能体ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计信息
     */
    @Select("SELECT " +
            "agent_id, " +
            "agent_name, " +
            "COUNT(*) as conversation_count, " +
            "SUM(total_tokens) as total_tokens, " +
            "COUNT(DISTINCT user_id) as unique_users " +
            "FROM ai_chat_statistics " +
            "WHERE agent_id = #{agentId} " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "AND del_flag = '0' " +
            "GROUP BY agent_id, agent_name")
    Map<String, Object> getAgentStatistics(@Param("agentId") Long agentId,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户ID统计使用情况
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计信息
     */
    @Select("SELECT " +
            "user_id, " +
            "username, " +
            "user_name, " +
            "COUNT(*) as conversation_count, " +
            "SUM(total_tokens) as total_tokens, " +
            "COUNT(DISTINCT agent_id) as used_agents " +
            "FROM ai_chat_statistics " +
            "WHERE user_id = #{userId} " +
            "AND create_time BETWEEN #{startTime} AND #{endTime} " +
            "AND del_flag = '0' " +
            "GROUP BY user_id, username, user_name")
    Map<String, Object> getUserStatistics(@Param("userId") Long userId,
                                          @Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 获取热门智能体排行榜
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 智能体排行榜
     */
    @Select("SELECT " +
            "agent_id, " +
            "agent_name, " +
            "COUNT(*) as conversation_count, " +
            "SUM(total_tokens) as total_tokens, " +
            "COUNT(DISTINCT user_id) as unique_users " +
            "FROM ai_chat_statistics " +
            "WHERE create_time BETWEEN #{startTime} AND #{endTime} " +
            "AND del_flag = '0' " +
            "GROUP BY agent_id, agent_name " +
            "ORDER BY conversation_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getPopularAgents(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime,
                                               @Param("limit") Integer limit);

    /**
     * 获取活跃用户排行榜
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 用户排行榜
     */
    @Select("SELECT " +
            "user_id, " +
            "username, " +
            "user_name, " +
            "COUNT(*) as conversation_count, " +
            "SUM(total_tokens) as total_tokens, " +
            "COUNT(DISTINCT agent_id) as used_agents " +
            "FROM ai_chat_statistics " +
            "WHERE create_time BETWEEN #{startTime} AND #{endTime} " +
            "AND del_flag = '0' " +
            "GROUP BY user_id, username, user_name " +
            "ORDER BY conversation_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getActiveUsers(@Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime,
                                             @Param("limit") Integer limit);
}
