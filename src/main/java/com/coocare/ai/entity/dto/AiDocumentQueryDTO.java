package com.coocare.ai.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * AI文档查询请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "AI文档查询请求DTO")
public class AiDocumentQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "文档名称（模糊查询）")
    private String documentName;

    @Schema(description = "所属分类ID（第三层分类）")
    private Long categoryId;

    @Schema(description = "所属产品系列ID（第二层分类）")
    private Long productSeriesId;

    @Schema(description = "所属产品型号ID（第一层分类）")
    private Long productModelId;

    @Schema(description = "分类编码")
    private String categoryCode;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "状态：enable=启用 disable=禁用 archive=归档 un_archive=取消归档")
    private String status;

    @Schema(description = "索引状态")
    private String indexingStatus;

    @Schema(description = "上传人")
    private String uploadBy;

    @Schema(description = "页码", example = "1")
    private Integer pageNo = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
}
