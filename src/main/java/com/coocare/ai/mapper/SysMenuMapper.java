package com.coocare.ai.mapper;

import com.coocare.ai.entity.sys.SysMenu;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 菜单权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Mapper
public interface SysMenuMapper extends MPJBaseMapper<SysMenu> {

    List<SysMenu> findMenuByRoleId(@Param("roleId") Long roleId);
}
