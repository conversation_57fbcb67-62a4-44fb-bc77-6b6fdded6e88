package com.coocare.ai.config.auth.reource.service;

import com.coocare.ai.config.constants.SecurityConstants;
import com.coocare.ai.config.constants.UserDetail;
import com.coocare.ai.config.exception.RRException;
import com.coocare.ai.entity.sys.dto.UserInfo;
import com.coocare.ai.service.SysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

/**
 * 邮箱验证码登录用户详情服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EmailUserDetailsServiceImpl implements CustomUserDetailsService {

    private final SysUserService userService;

    /**
     * 邮箱验证码登录
     *
     * @param username 用户名（格式：email@EMAIL）
     * @return UserDetails
     * @throws UsernameNotFoundException
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        if (!username.contains("@EMAIL")) {
            throw new RRException("非法参数");
        }

        // 提取邮箱地址
        String email = username.replace("@EMAIL", "");

        UserInfo userInfo = userService.loadUserByEmail(email);
        return getUserDetails(userInfo);
    }

    /**
     * 通过用户实体查询
     *
     * @param user user
     * @return UserDetails
     */
    @Override
    public UserDetails loadUserByUser(UserDetail user) {
        return loadUserByUsername(user.getUsername());
    }

    /**
     * 是否支持此客户端校验
     *
     * @param clientId  请求客户端
     * @param grantType 授权类型
     * @return true/false
     */
    @Override
    public boolean support(String clientId, String grantType) {
        return SecurityConstants.GRANT_TYPE_EMAIL_CODE.equals(grantType);
    }

    @Override
    public int getOrder() {
        return 100;
    }
}
