-- OAuth2 客户端配置
-- 支持密码登录、短信验证码登录、邮箱验证码登录

-- 创建 OAuth 客户端详情表（如果不存在）
CREATE TABLE IF NOT EXISTS `sys_oauth_client_details` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `client_id` varchar(32) NOT NULL COMMENT '客户端ID',
  `client_secret` varchar(256) NOT NULL COMMENT '客户端密钥',
  `resource_ids` varchar(256) DEFAULT NULL COMMENT '资源ID列表',
  `scope` varchar(256) NOT NULL COMMENT '作用域',
  `authorized_grant_types` varchar(256) NOT NULL COMMENT '授权方式',
  `web_server_redirect_uri` varchar(256) DEFAULT NULL COMMENT '回调地址',
  `authorities` varchar(256) DEFAULT NULL COMMENT '权限列表',
  `access_token_validity` int(11) DEFAULT NULL COMMENT '请求令牌有效时间',
  `refresh_token_validity` int(11) DEFAULT NULL COMMENT '刷新令牌有效时间',
  `additional_information` varchar(4096) DEFAULT NULL COMMENT '扩展信息',
  `autoapprove` varchar(256) DEFAULT NULL COMMENT '是否自动放行',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标记',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_client_id` (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OAuth客户端详情表';

-- 插入默认的 OAuth 客户端配置
INSERT INTO `sys_oauth_client_details` (
  `client_id`, 
  `client_secret`, 
  `resource_ids`, 
  `scope`, 
  `authorized_grant_types`, 
  `web_server_redirect_uri`, 
  `authorities`, 
  `access_token_validity`, 
  `refresh_token_validity`, 
  `additional_information`, 
  `autoapprove`,
  `create_by`
) VALUES (
  'genius',
  'genius',
  NULL,
  'server',
  'password,refresh_token,mobile,urn:ietf:params:oauth:grant-type:email_code',
  NULL,
  NULL,
  43200,
  2592000,
  '{"captcha_flag":"off"}',
  'true',
  'system'
) ON DUPLICATE KEY UPDATE 
  `authorized_grant_types` = 'password,refresh_token,mobile,urn:ietf:params:oauth:grant-type:email_code',
  `update_time` = CURRENT_TIMESTAMP;

-- 插入 Web 客户端配置
INSERT INTO `sys_oauth_client_details` (
  `client_id`, 
  `client_secret`, 
  `resource_ids`, 
  `scope`, 
  `authorized_grant_types`, 
  `web_server_redirect_uri`, 
  `authorities`, 
  `access_token_validity`, 
  `refresh_token_validity`, 
  `additional_information`, 
  `autoapprove`,
  `create_by`
) VALUES (
  'web',
  'web',
  NULL,
  'server',
  'password,refresh_token,authorization_code,urn:ietf:params:oauth:grant-type:email_code',
  'http://localhost:3000/callback',
  NULL,
  43200,
  2592000,
  '{"captcha_flag":"off"}',
  'false',
  'system'
) ON DUPLICATE KEY UPDATE 
  `authorized_grant_types` = 'password,refresh_token,authorization_code,urn:ietf:params:oauth:grant-type:email_code',
  `update_time` = CURRENT_TIMESTAMP;

-- 插入移动端客户端配置
INSERT INTO `sys_oauth_client_details` (
  `client_id`, 
  `client_secret`, 
  `resource_ids`, 
  `scope`, 
  `authorized_grant_types`, 
  `web_server_redirect_uri`, 
  `authorities`, 
  `access_token_validity`, 
  `refresh_token_validity`, 
  `additional_information`, 
  `autoapprove`,
  `create_by`
) VALUES (
  'mobile',
  'mobile',
  NULL,
  'server',
  'password,refresh_token,mobile,urn:ietf:params:oauth:grant-type:email_code',
  NULL,
  NULL,
  43200,
  2592000,
  '{"captcha_flag":"on"}',
  'true',
  'system'
) ON DUPLICATE KEY UPDATE 
  `authorized_grant_types` = 'password,refresh_token,mobile,urn:ietf:params:oauth:grant-type:email_code',
  `update_time` = CURRENT_TIMESTAMP;
