package com.coocare.ai.config.auth.support.filter;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 安全配置属性
 * 提供登录前端密码解密秘钥等安全参数
 *
 * <AUTHOR>
 * @since 2020-10-04
 */
@Data
@Component
@ConfigurationProperties("security")
public class AuthSecurityConfigProperties {

    /**
     * 网关解密登录前端密码 秘钥
     */
    private String encodeKey;

}
