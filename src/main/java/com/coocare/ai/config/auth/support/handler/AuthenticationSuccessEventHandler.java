package com.coocare.ai.config.auth.support.handler;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.ContentType;
import com.coocare.ai.config.constants.CacheConstants;
import com.coocare.ai.config.constants.CommonConstants;
import com.coocare.ai.config.constants.SecurityConstants;
import com.coocare.ai.config.domain.AjaxResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.OAuth2RefreshToken;
import org.springframework.security.oauth2.core.endpoint.OAuth2AccessTokenResponse;
import org.springframework.security.oauth2.core.http.converter.OAuth2AccessTokenResponseHttpMessageConverter;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AccessTokenAuthenticationToken;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.PrintWriter;
import java.time.temporal.ChronoUnit;
import java.util.Map;

/**
 * 认证成功处理器
 * 将 OAuth2 的 AccessTokenResponse 包装为统一的 AjaxResult 返回
 *
 * <AUTHOR>
 * @since 2022-06-02
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthenticationSuccessEventHandler implements AuthenticationSuccessHandler {

    private static final HttpMessageConverter<OAuth2AccessTokenResponse> accessTokenHttpResponseConverter = new OAuth2AccessTokenResponseHttpMessageConverter();

    @Resource
    private RedisTemplate redisTemplate;


    /**
     * Called when a user has been successfully authenticated.
     *
     * @param request        the request which caused the successful authentication
     * @param response       the response
     * @param authentication the <tt>Authentication</tt> object which was created during
     *                       the authentication process.
     */
    @SneakyThrows
    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) {

        // 写入登录成功的日志
        OAuth2AccessTokenAuthenticationToken accessTokenAuthentication = (OAuth2AccessTokenAuthenticationToken) authentication;
        Map<String, Object> map = accessTokenAuthentication.getAdditionalParameters();
        if (MapUtil.isNotEmpty(map)) {
            sendSuccessEventLog(request, accessTokenAuthentication, map);
        }
        // 清除账号历史锁定次数
        clearLoginFailureTimes(map);

        // 输出token
        sendAccessTokenResponse(response, authentication);
    }

    /**
     * 清空登录失败的记录
     *
     * @param map
     */
    private void clearLoginFailureTimes(Map<String, Object> map) {
        String key = String.format("%s:%s:%s", CacheConstants.LOGIN_ERROR_TIMES, MapUtil.getStr(map, SecurityConstants.CLIENT_ID), MapUtil.getStr(map, SecurityConstants.DETAILS_USERNAME));
        redisTemplate.delete(key);
    }

    /**
     * 记录登录成功事件
     *
     * @param request                   HttpServletRequest
     * @param accessTokenAuthentication Authentication
     * @param map                       请求参数
     */
    private void sendSuccessEventLog(HttpServletRequest request,
                                     OAuth2AccessTokenAuthenticationToken accessTokenAuthentication, Map<String, Object> map) {
        // 发送异步日志事件

        SecurityContext context = SecurityContextHolder.createEmptyContext();
        context.setAuthentication(accessTokenAuthentication);
        SecurityContextHolder.setContext(context);
    }

    private void sendAccessTokenResponse(HttpServletResponse response, Authentication authentication)
            throws IOException {

        OAuth2AccessTokenAuthenticationToken accessTokenAuthentication = (OAuth2AccessTokenAuthenticationToken) authentication;

        OAuth2AccessToken accessToken = accessTokenAuthentication.getAccessToken();
        OAuth2RefreshToken refreshToken = accessTokenAuthentication.getRefreshToken();
        Map<String, Object> additionalParameters = accessTokenAuthentication.getAdditionalParameters();

        OAuth2AccessTokenResponse.Builder builder = OAuth2AccessTokenResponse.withToken(accessToken.getTokenValue())
                .tokenType(accessToken.getTokenType())
                .scopes(accessToken.getScopes());
        if (accessToken.getIssuedAt() != null && accessToken.getExpiresAt() != null) {
            builder.expiresIn(ChronoUnit.SECONDS.between(accessToken.getIssuedAt(), accessToken.getExpiresAt()));
        }
        if (refreshToken != null) {
            builder.refreshToken(refreshToken.getTokenValue());
        }
        if (!CollectionUtils.isEmpty(additionalParameters)) {
            builder.additionalParameters(additionalParameters);
        }
        OAuth2AccessTokenResponse accessTokenResponse = builder.build();
//        ServletServerHttpResponse httpResponse = new ServletServerHttpResponse(response);

        // 无状态 注意删除 context 上下文的信息
        SecurityContextHolder.clearContext();
        AjaxResult<OAuth2AccessTokenResponse> result = new AjaxResult<>();
        result.setCode(CommonConstants.SUCCESS);
        result.setData(accessTokenResponse);
        response.setCharacterEncoding(CommonConstants.UTF8);
        response.setContentType(ContentType.JSON.getValue());
        response.setStatus(HttpStatus.OK.value());
        ObjectMapper objectMapper = new ObjectMapper();
        // 注册 JavaTimeModule 以支持 Java 8 日期时间类型
        objectMapper.registerModule(new JavaTimeModule());
        try (PrintWriter printWriter = response.getWriter()) {
            printWriter.write(objectMapper.writeValueAsString(result));
            printWriter.flush();
        }
//        this.accessTokenHttpResponseConverter.write(accessTokenResponse, null, httpResponse);
    }

}
