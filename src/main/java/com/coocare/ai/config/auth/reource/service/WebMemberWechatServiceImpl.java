//package com.coocare.ai.config.auth.reource.service;
/**
 * WeChat 登录用户详情服务（示例/占位）
 * 若启用请取消注释并接入 WechatService
 *
 * <AUTHOR>
 * @since 2022-06-02
 */
//
//import com.coocare.ai.config.constants.SecurityConstants;
//import com.coocare.ai.constants.ClientEnum;
//import com.coocare.ai.constants.UserDetail;
//import com.coocare.ai.constants.UserInfo;
//import com.coocare.ai.entity.ums.enums.SourceEnum;
//import com.coocare.ai.service.common.WechatService;
//import lombok.RequiredArgsConstructor;
//import org.springframework.security.core.userdetails.UserDetails;
//import org.springframework.security.core.userdetails.UsernameNotFoundException;
//import org.springframework.stereotype.Component;
//
///**
// * 微信登录模式
// *
// * <AUTHOR>
// */
//@Component
//@RequiredArgsConstructor
//public class WebMemberWechatServiceImpl implements CustomUserDetailsService {
//
//    private final WechatService wechatService;
//
//    @Override
//    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
//        UserInfo userInfo = wechatService.info(username, SourceEnum.PORTAL, ClientEnum.PORTAL);
//        return getUserDetails(userInfo);
//    }
//
//    @Override
//    public UserDetails loadUserByUser(UserDetail userDetail) {
//        return loadUserByUsername(userDetail.getUsername());
//    }
//
//    @Override
//    public boolean support(String clientId, String grantType) {
//        return SecurityConstants.WECHAT.equals(grantType) && ClientEnum.PORTAL.getCode().equals(clientId);
//    }
//}
