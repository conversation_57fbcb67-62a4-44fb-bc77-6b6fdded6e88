-- AI文档表建表脚本
-- 用于存储上传到第三层分类的文档信息

DROP TABLE IF EXISTS `ai_document`;

CREATE TABLE `ai_document` (
  `document_id` bigint NOT NULL COMMENT '文档ID',
  `document_name` varchar(200) NOT NULL COMMENT '文档名称',
  `original_name` varchar(200) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件存储路径',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件访问URL',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型/扩展名',
  `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
  `category_id` bigint NOT NULL COMMENT '所属分类ID（第三层分类）',
  `product_series_id` bigint NOT NULL COMMENT '所属产品系列ID（第二层分类）',
  `product_model_id` bigint NOT NULL COMMENT '所属产品型号ID（第一层分类）',
  `category_code` varchar(50) NOT NULL COMMENT '分类编码（冗余字段，便于查询）',
  `description` varchar(1000) DEFAULT NULL COMMENT '文档描述',
  `tags` varchar(500) DEFAULT NULL COMMENT '文档标签（JSON格式）',
  `version` varchar(20) DEFAULT '1.0' COMMENT '文档版本',
  `download_count` int DEFAULT '0' COMMENT '下载次数',
  `view_count` int DEFAULT '0' COMMENT '查看次数',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用，2-审核中',
  `is_public` tinyint DEFAULT '1' COMMENT '是否公开：0-私有，1-公开',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `upload_by` varchar(50) DEFAULT NULL COMMENT '上传人',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志：0-正常，1-删除',
  PRIMARY KEY (`document_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_product_series_id` (`product_series_id`),
  KEY `idx_product_model_id` (`product_model_id`),
  KEY `idx_category_code` (`category_code`),
  KEY `idx_document_name` (`document_name`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_status` (`status`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_del_flag` (`del_flag`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `fk_document_category` FOREIGN KEY (`category_id`) REFERENCES `ai_document_category` (`category_id`),
  CONSTRAINT `fk_document_product_series` FOREIGN KEY (`product_series_id`) REFERENCES `ai_document_category` (`category_id`),
  CONSTRAINT `fk_document_product_model` FOREIGN KEY (`product_model_id`) REFERENCES `ai_document_category` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI文档表';

-- 创建索引以优化查询性能
CREATE INDEX `idx_category_series_composite` ON `ai_document` (`product_series_id`, `category_code`, `status`, `del_flag`);
CREATE INDEX `idx_upload_time` ON `ai_document` (`create_time` DESC);

-- 插入示例数据
-- 为HP EliteBook 6 G1i 13添加一些示例文档
INSERT INTO `ai_document` (`document_id`, `document_name`, `original_name`, `file_path`, `file_url`, `file_size`, `file_type`, `mime_type`, `category_id`, `product_series_id`, `product_model_id`, `category_code`, `description`, `version`, `status`, `is_public`, `sort_order`, `upload_by`, `create_by`, `del_flag`) VALUES
-- 产品资料
(4001, 'HP EliteBook 6 G1i 13 技术规格书', 'HP_EliteBook_6_G1i_13_Specs.pdf', '/documents/product_data/HP_EliteBook_6_G1i_13_Specs.pdf', 'https://example.com/documents/product_data/HP_EliteBook_6_G1i_13_Specs.pdf', 2048576, 'pdf', 'application/pdf', 3001, 2001, 1001, 'PRODUCT_DATA', 'HP EliteBook 6 G1i 13英寸笔记本详细技术规格说明', '1.0', 1, 1, 1, 'admin', 'system', '0'),
(4002, 'HP EliteBook 6 G1i 13 用户手册', 'HP_EliteBook_6_G1i_13_Manual.pdf', '/documents/product_data/HP_EliteBook_6_G1i_13_Manual.pdf', 'https://example.com/documents/product_data/HP_EliteBook_6_G1i_13_Manual.pdf', 5242880, 'pdf', 'application/pdf', 3001, 2001, 1001, 'PRODUCT_DATA', 'HP EliteBook 6 G1i 13英寸笔记本用户使用手册', '1.0', 1, 1, 2, 'admin', 'system', '0'),

-- 产品图库
(4003, 'HP EliteBook 6 G1i 13 正面图', 'HP_EliteBook_6_G1i_13_Front.jpg', '/documents/product_gallery/HP_EliteBook_6_G1i_13_Front.jpg', 'https://example.com/documents/product_gallery/HP_EliteBook_6_G1i_13_Front.jpg', 1024000, 'jpg', 'image/jpeg', 3002, 2001, 1001, 'PRODUCT_GALLERY', 'HP EliteBook 6 G1i 13英寸笔记本正面高清图片', '1.0', 1, 1, 1, 'admin', 'system', '0'),
(4004, 'HP EliteBook 6 G1i 13 侧面图', 'HP_EliteBook_6_G1i_13_Side.jpg', '/documents/product_gallery/HP_EliteBook_6_G1i_13_Side.jpg', 'https://example.com/documents/product_gallery/HP_EliteBook_6_G1i_13_Side.jpg', 896000, 'jpg', 'image/jpeg', 3002, 2001, 1001, 'PRODUCT_GALLERY', 'HP EliteBook 6 G1i 13英寸笔记本侧面高清图片', '1.0', 1, 1, 2, 'admin', 'system', '0'),

-- 产品彩页
(4005, 'HP EliteBook 6 G1i 13 产品宣传册', 'HP_EliteBook_6_G1i_13_Brochure.pdf', '/documents/product_brochure/HP_EliteBook_6_G1i_13_Brochure.pdf', 'https://example.com/documents/product_brochure/HP_EliteBook_6_G1i_13_Brochure.pdf', 3145728, 'pdf', 'application/pdf', 3003, 2001, 1001, 'PRODUCT_BROCHURE', 'HP EliteBook 6 G1i 13英寸笔记本产品宣传彩页', '1.0', 1, 1, 1, 'admin', 'system', '0'),

-- 产品证书
(4006, 'HP EliteBook 6 G1i 13 CE认证证书', 'HP_EliteBook_6_G1i_13_CE_Certificate.pdf', '/documents/product_certificate/HP_EliteBook_6_G1i_13_CE_Certificate.pdf', 'https://example.com/documents/product_certificate/HP_EliteBook_6_G1i_13_CE_Certificate.pdf', 512000, 'pdf', 'application/pdf', 3004, 2001, 1001, 'PRODUCT_CERTIFICATE', 'HP EliteBook 6 G1i 13英寸笔记本CE认证证书', '1.0', 1, 1, 1, 'admin', 'system', '0'),
(4007, 'HP EliteBook 6 G1i 13 FCC认证证书', 'HP_EliteBook_6_G1i_13_FCC_Certificate.pdf', '/documents/product_certificate/HP_EliteBook_6_G1i_13_FCC_Certificate.pdf', 'https://example.com/documents/product_certificate/HP_EliteBook_6_G1i_13_FCC_Certificate.pdf', 480000, 'pdf', 'application/pdf', 3004, 2001, 1001, 'PRODUCT_CERTIFICATE', 'HP EliteBook 6 G1i 13英寸笔记本FCC认证证书', '1.0', 1, 1, 2, 'admin', 'system', '0'),

-- Quick Specs
(4008, 'HP EliteBook 6 G1i 13 快速规格表', 'HP_EliteBook_6_G1i_13_QuickSpecs.pdf', '/documents/quick_specs/HP_EliteBook_6_G1i_13_QuickSpecs.pdf', 'https://example.com/documents/quick_specs/HP_EliteBook_6_G1i_13_QuickSpecs.pdf', 256000, 'pdf', 'application/pdf', 3005, 2001, 1001, 'QUICK_SPECS', 'HP EliteBook 6 G1i 13英寸笔记本快速规格参数表', '1.0', 1, 1, 1, 'admin', 'system', '0'),

-- 图说产品
(4009, 'HP EliteBook 6 G1i 13 产品图解', 'HP_EliteBook_6_G1i_13_Illustration.pdf', '/documents/product_illustration/HP_EliteBook_6_G1i_13_Illustration.pdf', 'https://example.com/documents/product_illustration/HP_EliteBook_6_G1i_13_Illustration.pdf', 4194304, 'pdf', 'application/pdf', 3006, 2001, 1001, 'PRODUCT_ILLUSTRATION', 'HP EliteBook 6 G1i 13英寸笔记本产品功能图解说明', '1.0', 1, 1, 1, 'admin', 'system', '0');

-- 为HP EliteBook 6 G1i 14添加一些示例文档
INSERT INTO `ai_document` (`document_id`, `document_name`, `original_name`, `file_path`, `file_url`, `file_size`, `file_type`, `mime_type`, `category_id`, `product_series_id`, `product_model_id`, `category_code`, `description`, `version`, `status`, `is_public`, `sort_order`, `upload_by`, `create_by`, `del_flag`) VALUES
-- 产品资料
(4010, 'HP EliteBook 6 G1i 14 技术规格书', 'HP_EliteBook_6_G1i_14_Specs.pdf', '/documents/product_data/HP_EliteBook_6_G1i_14_Specs.pdf', 'https://example.com/documents/product_data/HP_EliteBook_6_G1i_14_Specs.pdf', 2097152, 'pdf', 'application/pdf', 3007, 2002, 1001, 'PRODUCT_DATA', 'HP EliteBook 6 G1i 14英寸笔记本详细技术规格说明', '1.0', 1, 1, 1, 'admin', 'system', '0'),

-- 产品图库
(4011, 'HP EliteBook 6 G1i 14 正面图', 'HP_EliteBook_6_G1i_14_Front.jpg', '/documents/product_gallery/HP_EliteBook_6_G1i_14_Front.jpg', 'https://example.com/documents/product_gallery/HP_EliteBook_6_G1i_14_Front.jpg', 1048576, 'jpg', 'image/jpeg', 3008, 2002, 1001, 'PRODUCT_GALLERY', 'HP EliteBook 6 G1i 14英寸笔记本正面高清图片', '1.0', 1, 1, 1, 'admin', 'system', '0'),

-- Quick Specs
(4012, 'HP EliteBook 6 G1i 14 快速规格表', 'HP_EliteBook_6_G1i_14_QuickSpecs.pdf', '/documents/quick_specs/HP_EliteBook_6_G1i_14_QuickSpecs.pdf', 'https://example.com/documents/quick_specs/HP_EliteBook_6_G1i_14_QuickSpecs.pdf', 280000, 'pdf', 'application/pdf', 3011, 2002, 1001, 'QUICK_SPECS', 'HP EliteBook 6 G1i 14英寸笔记本快速规格参数表', '1.0', 1, 1, 1, 'admin', 'system', '0');
