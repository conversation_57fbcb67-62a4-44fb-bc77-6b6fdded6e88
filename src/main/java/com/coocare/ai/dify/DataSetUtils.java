//package com.coocare.ai.dify;
//
//import cn.hutool.core.io.FileUtil;
//import cn.hutool.core.util.StrUtil;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.coocare.ai.config.GlobalConfigManager;
//import com.coocare.ai.entity.AiDataset;
//import com.coocare.ai.entity.AiDocument;
//import com.coocare.ai.service.AiDatasetService;
//import com.coocare.ai.service.IAiDocumentService;
//import io.github.imfangs.dify.client.DifyClientFactory;
//import io.github.imfangs.dify.client.DifyDatasetsClient;
//import io.github.imfangs.dify.client.model.datasets.CreateDocumentByFileRequest;
//import io.github.imfangs.dify.client.model.datasets.DocumentResponse;
//import io.github.imfangs.dify.client.model.datasets.ProcessRule;
//import io.github.imfangs.dify.client.model.datasets.UpdateDocumentStatusRequest;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import java.io.File;
//import java.util.Collections;
//
//import static org.wildfly.common.Assert.assertNotNull;
//
///**
// * <p>
// * 数据集工具类
// * 提供与Dify数据集相关的操作功能
// * 使用全局配置管理器获取服务配置
// * </p>
// *
// * <AUTHOR>
// * @since 2025-08-12
// */
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class DataSetUtils {
//
//    private final IAiDocumentService aiDocumentService;
//    private final AiDatasetService aiDatasetService;
//    private final GlobalConfigManager globalConfigManager;
//
//    public CreateDocumentByFileRequest getDocumentRequest(String docForm) {
//        return CreateDocumentByFileRequest.builder()
//                .indexingTechnique("high_quality")
//                .docForm(docForm)
//                .docLanguage("Chinese")
//                .processRule(ProcessRule.builder()
//                        .mode("automatic")  // 使用自动处理模式
//                        .build())
//                .build();
//    }
//
//    /**
//     * 添加文件到数据集
//     * @param datasetId
//     * @param documentId
//     * @param documentUrl
//     */
//    public void addFileToDataSet(Long datasetId, Long documentId, String documentUrl) {
//        try {
//            AiDataset dataset = aiDatasetService.getById(datasetId);
//            String difyServiceUrl = globalConfigManager.getDifyServiceUrl();
//            if (StrUtil.isBlank(difyServiceUrl)) {
//                log.error("Dify服务URL未配置，请检查系统配置");
//                return;
//            }
//            File file = FileUtil.file(documentUrl);
//            DifyDatasetsClient difyDatasetsClient = DifyClientFactory.createDatasetsClient(difyServiceUrl, globalConfigManager.getDatasetApiKey());
//            DocumentResponse response = difyDatasetsClient.createDocumentByFile(dataset.getBackendId(), getDocumentRequest("text_model"), file);
//            // 验证响应
//            assertNotNull(response);
//            assertNotNull(response.getDocument());
//            assertNotNull(response.getDocument().getId());
//            aiDocumentService.update(Wrappers.lambdaUpdate(AiDocument.class)
//                    .eq(AiDocument::getDocumentId, documentId)
//                    .set(AiDocument::getBackendId, response.getDocument().getId()));
//        } catch (Exception e) {
//            log.error("添加文件到数据集失败: datasetId={}, filePath={}", datasetId, documentUrl, e);
//        }
//    }
//
//    /**
//     * 删除文件到数据集
//     * @param datasetId
//     * @param documentId
//     */
//    public void removeFileToDataSet(Long datasetId, Long documentId) {
//        try {
//            AiDataset dataset = aiDatasetService.getById(datasetId);
//            AiDocument document = aiDocumentService.getById(documentId);
//            String difyServiceUrl = globalConfigManager.getDifyServiceUrl();
//            if (StrUtil.isBlank(difyServiceUrl)) {
//                log.error("Dify服务URL未配置，请检查系统配置");
//                return;
//            }
//            // 检查文件是否存在
//            DifyDatasetsClient difyDatasetsClient = DifyClientFactory.createDatasetsClient(difyServiceUrl, globalConfigManager.getDatasetApiKey());
//            difyDatasetsClient.deleteDocument(dataset.getBackendId(), document.getBackendId());
//            aiDocumentService.removeById(documentId);
//        } catch (Exception e) {
//            log.error("添加文件到数据集失败: datasetId={}, documentId={}", datasetId, documentId, e);
//        }
//    }
//
//    /**
//     * 更新数据集文件状态
//     * @param datasetId
//     * @param documentId
//     */
//    public void changeFileStatus(Long datasetId, Long documentId, String status) {
//        try {
//            AiDataset dataset = aiDatasetService.getById(datasetId);
//            AiDocument document = aiDocumentService.getById(documentId);
//            String difyServiceUrl = globalConfigManager.getDifyServiceUrl();
//            if (StrUtil.isBlank(difyServiceUrl)) {
//                log.error("Dify服务URL未配置，请检查系统配置");
//                return;
//            }
//            // 检查文件是否存在
//            DifyDatasetsClient difyDatasetsClient = DifyClientFactory.createDatasetsClient(difyServiceUrl, globalConfigManager.getDatasetApiKey());
//            UpdateDocumentStatusRequest request = UpdateDocumentStatusRequest.builder().documentIds(Collections.singletonList(document.getBackendId())).build();
//            difyDatasetsClient.updateDocumentStatus(dataset.getBackendId(), status, request);
//            aiDocumentService.removeById(documentId);
//        } catch (Exception e) {
//            log.error("更新数据集文件状态失败: datasetId={}, documentId={}", datasetId, documentId, e);
//        }
//    }
//
//    /**
//     * 更新数据集文件
//     * @param datasetId
//     * @param documentId
//     */
//    public void updateFileToDataSet(Long datasetId, Long documentId, String documentUrl) {
//        try {
//            AiDataset dataset = aiDatasetService.getById(datasetId);
//            AiDocument document = aiDocumentService.getById(documentId);
//            String difyServiceUrl = globalConfigManager.getDifyServiceUrl();
//            if (StrUtil.isBlank(difyServiceUrl)) {
//                log.error("Dify服务URL未配置，请检查系统配置");
//                return;
//            }
//            File file = FileUtil.file(documentUrl);
//            DifyDatasetsClient difyDatasetsClient = DifyClientFactory.createDatasetsClient(difyServiceUrl, globalConfigManager.getDatasetApiKey());
//            DocumentResponse response = difyDatasetsClient.updateDocumentByFile(dataset.getBackendId(), document.getBackendId(), null, file);
//            // 验证响应
//            assertNotNull(response);
//            assertNotNull(response.getDocument());
//            assertNotNull(response.getDocument().getId());
//            aiDocumentService.update(Wrappers.lambdaUpdate(AiDocument.class)
//                    .eq(AiDocument::getDocumentId, documentId)
//                    .set(AiDocument::getBackendId, response.getDocument().getId()));
//        } catch (Exception e) {
//            log.error("更新数据集文件失败: datasetId={}, documentId={}", datasetId, documentId, e);
//        }
//    }
//
//}
