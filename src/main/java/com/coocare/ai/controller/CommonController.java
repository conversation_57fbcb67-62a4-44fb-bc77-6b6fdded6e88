package com.coocare.ai.controller;

import cn.hutool.core.util.RandomUtil;
import com.coocare.ai.config.GlobalConfigManager;
import com.coocare.ai.config.auth.reource.annotation.Inner;
import com.coocare.ai.config.constants.ConfigConstants;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.oss.OssProperties;
import com.coocare.ai.config.oss.OssTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * 通用控制器
 * 提供公共能力（邮件验证码等）
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Inner(value = false)
@Tag(name = "通用接口")
@RestController
@RequestMapping("/common")
@RequiredArgsConstructor
@Slf4j
public class CommonController {

    private final OssProperties ossProperties;
    private final OssTemplate ossTemplate;
    private final JavaMailSender mailSender;
    private final GlobalConfigManager globalConfigManager;
    private final RedisTemplate<String, Object> redisTemplate;

    @Operation(summary = "发送邮件验证码")
    @PostMapping("/email/send")
    public AjaxResult<?> sendEmailCode(@Parameter(description = "邮箱地址") @RequestParam String email) {
        if (!StringUtils.hasText(email) || !isValidEmail(email)) {
            return AjaxResult.failed("邮箱格式不正确");
        }

        String cacheKey = "email_code:" + email;

        // 检查是否在冷却时间内
        if (redisTemplate.hasKey(cacheKey)) {
            return AjaxResult.failed("验证码发送过于频繁，请稍后再试");
        }

        try {
            // 生成6位数字验证码
            String code = RandomUtil.randomNumbers(6);

            // 发送邮件
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(globalConfigManager.getConfig(ConfigConstants.MAIL_USERNAME));
            message.setTo(email);
            message.setSubject("邮箱验证码");
            message.setText("您的验证码是：" + code + "，有效期为5分钟，请勿泄露给他人。");
            mailSender.send(message);

            // 存储验证码到Redis，有效期5分钟
            redisTemplate.opsForValue().set(cacheKey, code, 5, TimeUnit.MINUTES);

            log.info("邮件验证码发送成功，邮箱：{}", email);
            return AjaxResult.ok("验证码发送成功");

        } catch (Exception e) {
            log.error("邮件发送失败，邮箱：{}，错误：{}", email, e.getMessage());
            return AjaxResult.failed("邮件发送失败，请稍后重试");
        }
    }

    @Operation(summary = "验证邮件验证码")
    @PostMapping("/email/verify")
    public AjaxResult<?> verifyEmailCode(@Parameter(description = "邮箱地址") @RequestParam String email,
                                        @Parameter(description = "验证码") @RequestParam String code) {
        if (!StringUtils.hasText(email) || !StringUtils.hasText(code)) {
            return AjaxResult.failed("邮箱和验证码不能为空");
        }

        String cacheKey = "email_code:" + email;
        String cachedCode = (String) redisTemplate.opsForValue().get(cacheKey);

        if (cachedCode == null) {
            return AjaxResult.failed("验证码已过期或不存在");
        }

        if (!code.equals(cachedCode)) {
            return AjaxResult.failed("验证码错误");
        }

        // 验证成功后删除验证码
        redisTemplate.delete(cacheKey);

        log.info("邮件验证码验证成功，邮箱：{}", email);
        return AjaxResult.ok("验证成功");
    }

    /**
     * 简单的邮箱格式验证
     */
    private boolean isValidEmail(String email) {
        return email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }

//    @Operation(summary = "获取上传参数")
//    @GetMapping("/upload")
//    public AjaxResult<?> upload(@Parameter(name = "filename", description = "上传内容的名称。<br>接口返回真实的上传地址，客户端再通过真实上传地址通过PUT方式上传。<br>上传后URL访问形式为：https://xxxx/文件名，客户端可自己组合URL后使用", required = true)
//                                @RequestParam(value = "filename") String filename) {
//        return AjaxResult.success(ossTemplate.preUploadUrl(ossProperties.getBucketName(), filename));
//    }
}
