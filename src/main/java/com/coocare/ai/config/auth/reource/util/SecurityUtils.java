package com.coocare.ai.config.auth.reource.util;

import cn.hutool.core.util.StrUtil;
import com.coocare.ai.config.constants.SecurityConstants;
import com.coocare.ai.config.constants.UserDetail;
import lombok.experimental.UtilityClass;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthentication;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 安全工具类
 * 提供当前认证信息、Token、Claims、用户与角色的便捷访问
 *
 * <AUTHOR>
 * @since 2022-06-04
 */

@UtilityClass
public class SecurityUtils {

    /**
     * 获取Authentication
     */
    public Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 获取Authentication Token
     *
     * @return
     */
    public String getToken() {
        Authentication authentication = SecurityUtils.getAuthentication();
        if (authentication instanceof BearerTokenAuthentication bearerTokenAuthentication) {
            return bearerTokenAuthentication.getToken().getTokenValue();
        }
        return null;
    }

    /**
     * 获取扩展参数
     *
     * @param claimName 扩展参数的名称
     * @return 扩展参数值
     */
    public static Object getClaim(String claimName) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof BearerTokenAuthentication bearerTokenAuthentication) {
            return bearerTokenAuthentication.getTokenAttributes().get(claimName).toString();
        }
        return null;
    }

    /**
     * 获取用户
     *
     * @param authentication
     * @return UserDetail
     * <p>
     */
    public UserDetail getUser(Authentication authentication) {
        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetail) {
            return (UserDetail) principal;
        }
        return null;
    }

    /**
     * 获取用户
     */
    public UserDetail getUser() {
        Authentication authentication = getAuthentication();
        return getUser(authentication);
    }

    /**
     * 获取用户角色信息
     *
     * @return 角色集合
     */
    public List<Long> getRoleIds() {
        Authentication authentication = getAuthentication();
        Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();

        List<Long> roleIds = new ArrayList<>();
        authorities.stream()
                .filter(granted -> StrUtil.startWith(granted.getAuthority(), SecurityConstants.ROLE))
                .forEach(granted -> {
                    String id = StrUtil.removePrefix(granted.getAuthority(), SecurityConstants.ROLE);
                    roleIds.add(Long.parseLong(id));
                });
        return roleIds;
    }

}
