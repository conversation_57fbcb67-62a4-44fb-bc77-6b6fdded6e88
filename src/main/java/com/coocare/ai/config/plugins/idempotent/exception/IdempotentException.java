package com.coocare.ai.config.plugins.idempotent.exception;

/**
 * 幂等异常
 * 若存在全局异常基类，可继承其统一处理
 *
 * <AUTHOR>
 * @since 2023-01-01
 */
public class IdempotentException extends RuntimeException {

	public IdempotentException() {
		super();
	}

	public IdempotentException(String message) {
		super(message);
	}

	public IdempotentException(String message, Throwable cause) {
		super(message, cause);
	}

	public IdempotentException(Throwable cause) {
		super(cause);
	}

	protected IdempotentException(String message, Throwable cause, boolean enableSuppression,
			boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}

}
