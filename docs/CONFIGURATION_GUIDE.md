# 配置与环境说明

## application.yml 关键项
- server.port
- spring.cache/redis
- spring.datasource（MySQL）
- spring.mail（邮件服务）
- mybatis-plus / mybatis-plus-join
- springdoc 与 knife4j
- oss.*（S3 兼容存储）
- sa-token（如启用）
- dify.*（Dify 服务与数据集）

## 环境变量
- MYSQL_*、REDIS_*、MAIL_*、DIFY_*

## 环境差异建议
- dev：开启日志与 Swagger
- prod：白名单 CORS、关闭 Swagger、加强安全 Header、只读角色最小化


## application.yml 示例（参考）

```yaml
server:
  port: ${SERVER_PORT:8070}

spring:
  datasource:
    url: jdbc:mysql://${MYSQL_HOST:localhost}:${MYSQL_PORT:3306}/${MYSQL_DB:zhan_ai}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASS:root}
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASS:}
  mail:
    host: ${MAIL_HOST:}
    username: ${MAIL_USER:}
    password: ${MAIL_PASS:}

springdoc:
  api-docs:
    enabled: ${OPENAPI_ENABLED:true}
  swagger-ui:
    enabled: ${SWAGGER_UI_ENABLED:true}

oss:
  enable: ${OSS_ENABLE:true}
  endpoint: ${OSS_ENDPOINT:http://127.0.0.1:9000}
  access-key: ${OSS_ACCESS_KEY:admin}
  secret-key: ${OSS_SECRET_KEY:admin123456}
  bucket-name: ${OSS_BUCKET:zhan-ai}
  path-style-access: ${OSS_PATH_STYLE:false}
  custom-domain: ${OSS_DOMAIN:}

sa-token:
  token-name: Authorization

security:
  oauth2:
    server-url: ${AUTH_SERVER_URL:http://localhost:8070}

dify:
  url: ${DIFY_URL:}
  email: ${DIFY_EMAIL:}
  password: ${DIFY_PASSWORD:}
  datasetApiKey: ${DIFY_DATASET_API_KEY:}
```

> 说明：
- 使用 ${VAR:default} 方式覆盖默认值，生产环境务必通过环境变量提供机密配置
- 仅为示例，实际项以项目中的配置类与配置文件为准
