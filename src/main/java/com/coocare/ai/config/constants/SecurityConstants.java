package com.coocare.ai.config.constants;

/**
 * 安全相关常量
 * 统一定义 OAuth2、鉴权、加密、Header 等常量键名
 *
 * <AUTHOR>
 * @since 2022-06-02
 */
public interface SecurityConstants {

    /**
     * 启动时是否检查Inner注解安全性
     */
    boolean INNER_CHECK = true;

    /**
     * 刷新
     */
    String REFRESH_TOKEN = "refresh_token";

    /**
     * 验证码有效期
     */
    int CODE_TIME = 60;

    /**
     * 验证码长度
     */
    String CODE_SIZE = "4";

    /**
     * 角色前缀
     */
    String ROLE = "ROLE_";

    /**
     * 前缀
     */
    String COO_CARE_PREFIX = "coocare_";

    /**
     * token 相关前缀
     */
    String TOKEN_PREFIX = "token:";

    /**
     * oauth 相关前缀
     */
    String OAUTH_PREFIX = "oauth:";

    /**
     * 授权码模式code key 前缀
     */
    String OAUTH_CODE_PREFIX = "oauth:code:";

    /**
     * 项目的license
     */
    String COOCARE_LICENSE = "https://www.coocare.com";

    /**
     * OAUTH URL
     */
    String OAUTH_TOKEN_URL = "/oauth2/token";

    /**
     * 移动端授权
     */
    String GRANT_MOBILE = "mobile";

    /**
     * {bcrypt} 加密的特征码
     */
    String BCRYPT = "{bcrypt}";

    /**
     * 客户端模式
     */
    String CLIENT_CREDENTIALS = "client_credentials";

    /**
     * 客户端编号
     */
    String CLIENT_ID = "client_id";

    /**
     * 客户端唯一令牌
     */
    String CLIENT_RECREATE = "recreate_flag";

    /**
     * 用户ID字段
     */
    String DETAILS_USER_ID = "user_id";

    /**
     * 用户名
     */
    String DETAILS_USERNAME = "username";

    /**
     * 姓名
     */
    String NAME = "name";

    /**
     * 协议字段
     */
    String DETAILS_LICENSE = "license";

    /**
     * 激活字段 兼容外围系统接入
     */
    String ACTIVE = "active";

    /**
     * AES 加密
     */
    String AES = "aes";

    /**
     * 授权码模式confirm
     */
    String CUSTOM_CONSENT_PAGE_URI = "/token/confirm_access";

    /**
     * {noop} 加密的特征码
     */
    String NOOP = "{noop}";

    /**
     * 短信登录 参数名称
     */
    String SMS_PARAMETER_NAME = "mobile";

    /**
     * 手机号登录
     */
    String SMS = "mobile";

    /**
     * 邮箱登录
     */
    String MAIL = "mail";

    /**
     * 用户信息
     */
    String DETAILS_USER = "user_info";

    /**
     * 登录方式——短信验证码
     */
    String SMS_LOGIN_TYPE = "smsCaptcha";

    /**
     * 登录方式——账号密码登录
     */
    String PASSWORD_LOGIN_TYPE = "passwordLogin";

    /**
     * 权限在token中的key
     */
    String AUTHORITIES_KEY = "authorities";

    /**
     * 自定义 grant type —— 短信验证码
     */
    String GRANT_TYPE_SMS_CODE = "urn:ietf:params:oauth:grant-type:sms_code";

    /**
     * 自定义 grant type —— 短信验证码 —— 手机号的key
     */
    String OAUTH_PARAMETER_NAME_PHONE = "phone";

    /**
     * 自定义 grant type —— 短信验证码 —— 短信验证码的key
     */
    String OAUTH_PARAMETER_NAME_SMS_CAPTCHA = "sms_captcha";

    String SMS_CAPTCHA_PREFIX = "smsCaptcha:";

    /**
     * 自定义 grant type —— 邮箱验证码
     */
    String GRANT_TYPE_EMAIL_CODE = "urn:ietf:params:oauth:grant-type:email_code";

    /**
     * 自定义 grant type —— 邮箱验证码 —— 邮箱的key
     */
    String OAUTH_PARAMETER_NAME_EMAIL = "email";

    /**
     * 自定义 grant type —— 邮箱验证码 —— 邮箱验证码的key
     */
    String OAUTH_PARAMETER_NAME_EMAIL_CAPTCHA = "email_captcha";

    /**
     * 邮箱验证码前缀
     */
    String EMAIL_CAPTCHA_PREFIX = "email_code:";

    /**
     * 登录方式——邮箱验证码
     */
    String EMAIL_LOGIN_TYPE = "emailCaptcha";

    /**
     * 随机字符串请求头名字
     */
    String NONCE_HEADER_NAME = "nonceId";

    /**
     * 内部
     */
    String FROM_IN = "Y";

    /**
     * 标志
     */
    String FROM = "from";

}
