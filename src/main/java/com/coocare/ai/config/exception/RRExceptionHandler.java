package com.coocare.ai.config.exception;

import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.i18n.I18nMessage;
import com.coocare.ai.config.plugins.idempotent.exception.IdempotentException;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLSyntaxErrorException;
import java.util.List;


/**
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
@Order(999999)
public class RRExceptionHandler {

    /**
     * 处理校验异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public AjaxResult<Object> handleConstraintViolationException(ConstraintViolationException e) {
        return AjaxResult.failed(I18nMessage.getMessage(e.getMessage()));
    }

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(RRException.class)
    public AjaxResult<Object> handleRRException(RRException e) {
        return AjaxResult.failed(e.getCode(), I18nMessage.getMessage(e.getMessage(), e.getArgs()));
    }

    @ExceptionHandler(DuplicateKeyException.class)
    public AjaxResult<Object> handleDuplicateKeyException(DuplicateKeyException e) {
        log.error(e.getMessage(), e);
        return AjaxResult.failed(I18nMessage.getMessage("system.data.exist"));
    }

    @ExceptionHandler(BindException.class)
    public AjaxResult<Object> bindExceptionHandler(BindException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        String messages = StringUtils.join(fieldErrors.stream().map(DefaultMessageSourceResolvable::getDefaultMessage).toArray(), ";");
        return AjaxResult.failed(messages);
    }

    @ExceptionHandler(Exception.class)
    public AjaxResult<Object> handleException(Exception e) {
        log.error(e.getMessage(), e);
        return AjaxResult.failed("system.error");
    }

    @ExceptionHandler(SQLSyntaxErrorException.class)
    public AjaxResult<Object> handleSQLSyntaxErrorException(SQLSyntaxErrorException e) {
        log.error(e.getMessage(), e);
        return AjaxResult.failed();
    }

    @ExceptionHandler(IdempotentException.class)
    public AjaxResult<Object> handleIdempotentException(IdempotentException e) {
        log.error(e.getMessage(), e);
        return AjaxResult.failed(e.getMessage());
    }
}
