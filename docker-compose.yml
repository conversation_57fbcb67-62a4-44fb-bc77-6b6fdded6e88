version: '3.8'
services:
  mysql:
    image: mysql:8.0
    container_name: zai-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: zhan_ai
    ports:
      - "3306:3306"
    command: ["--default-authentication-plugin=mysql_native_password", "--character-set-server=utf8mb4", "--collation-server=utf8mb4_unicode_ci"]
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7
    container_name: zai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  minio:
    image: minio/minio:RELEASE.2024-09-22T00-33-43Z
    container_name: zai-minio
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: admin123456
    command: server /data --console-address ":9001"
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data

volumes:
  mysql_data:
  redis_data:
  minio_data:

