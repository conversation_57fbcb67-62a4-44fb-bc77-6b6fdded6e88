package com.coocare.ai.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocare.ai.config.constants.CommonConstants;
import com.coocare.ai.config.constants.ConfigConstants;
import com.coocare.ai.entity.AiAgent;
import com.coocare.ai.entity.AiDataset;
import com.coocare.ai.entity.config.CompanyInfo;
import com.coocare.ai.entity.sys.*;
import com.coocare.ai.entity.sys.dto.SystemInitDTO;
import com.coocare.ai.listener.ConfigUpdateListener;
import com.coocare.ai.service.*;
import io.github.guoshiqiufeng.dify.client.spring6.builder.DifyDatasetBuilder;
import io.github.guoshiqiufeng.dify.client.spring6.builder.DifyServerBuilder;
import io.github.guoshiqiufeng.dify.core.config.DifyProperties;
import io.github.guoshiqiufeng.dify.core.pojo.DifyPageResult;
import io.github.guoshiqiufeng.dify.dataset.DifyDataset;
import io.github.guoshiqiufeng.dify.dataset.dto.request.DatasetPageRequest;
import io.github.guoshiqiufeng.dify.dataset.dto.response.DatasetResponse;
import io.github.guoshiqiufeng.dify.server.DifyServer;
import io.github.guoshiqiufeng.dify.server.client.DifyServerTokenDefault;
import io.github.guoshiqiufeng.dify.server.dto.response.ApiKeyResponse;
import io.github.guoshiqiufeng.dify.server.dto.response.AppsResponse;
import io.github.guoshiqiufeng.dify.server.dto.response.DatasetApiKeyResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 系统初始化服务实现
 * 负责系统首次启动的初始化编排（角色/部门/管理员、企业与行业信息、智能体与数据集、系统参数、邮件配置等）
 * 完成后写入初始化标识并触发配置刷新事件
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemInitServiceImpl implements SystemInitService {

    // 使用ConfigConstants中定义的常量
    private final SysUserService userService;
    private final SysRoleService roleService;
    private final SysDeptService deptService;
    private final SysConfigService configService;
    private final SysUserRoleService userRoleService;
    private final SysMenuService menuService;
    private final SysRoleMenuService roleMenuService;
    private final AiAgentService aiAgentService;
    private final AiDatasetService aiDatasetService;
    private final ConfigUpdateListener configUpdateListener;

    @Override
    public boolean isSystemInitialized() {
        try {
            String initFlag = configService.getValue(ConfigConstants.SYSTEM_INIT_FLAG);
            return "true".equals(initFlag);
        } catch (Exception e) {
            log.warn("检查系统初始化状态失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initializeSystem(SystemInitDTO initDTO) {
        try {
            log.info("开始系统初始化...");

            // 1. 检查系统是否已经初始化
            if (isSystemInitialized()) {
                log.warn("系统已经初始化，跳过初始化流程");
                return false;
            }

            // 2. 创建默认角色和权限
            createDefaultRolesAndPermissions();

            // 3. 创建默认部门
            Long deptId = createDefaultDepartment(initDTO.getCompanyName());

            // 4. 创建管理员账号
            Long adminUserId = createAdminUser(initDTO);

            // 5. 将管理员分配到默认部门
            if (adminUserId != null && deptId != null) {
                SysUser adminUser = userService.getById(adminUserId);
                adminUser.setDeptId(deptId);
                userService.updateById(adminUser);
            }

            // 6. 初始化企业信息
            initCompanyInfo(initDTO);

            // 7. 初始化行业信息
            initIndustryInfo(initDTO);

            // 8. 初始化智能体配置
            initAgentConfig(initDTO);

            // 9. 初始化智能体服务配置
            initAgentServiceConfig(initDTO);

            // 10. 初始化系统配置
            initSystemConfig(initDTO);

            // 11. 设置系统初始化标识
            SysConfig initConfig = new SysConfig();
            initConfig.setParamKey(ConfigConstants.SYSTEM_INIT_FLAG);
            initConfig.setParamValue("true");
            initConfig.setRemark("系统初始化完成标识");
            configService.saveOrUpdateSysConfigByKey(initConfig);

            // 12. 触发系统初始化完成事件，重新加载全局配置
            configUpdateListener.handleSystemInitComplete();

            log.info("系统初始化完成");
            return true;

        } catch (Exception e) {
            log.error("系统初始化失败", e);
            throw new RuntimeException("系统初始化失败: " + e.getMessage());
        }
    }

    @Override
    public Long createAdminUser(SystemInitDTO initDTO) {
        try {
            // 检查管理员用户是否已存在
            SysUser existingUser = userService.queryUserByName(initDTO.getAdminUsername());
            if (existingUser != null) {
                log.warn("管理员用户已存在: {}", initDTO.getAdminUsername());
                return existingUser.getUserId();
            }

            // 创建管理员用户
            SysUser adminUser = new SysUser();
            adminUser.setUsername(initDTO.getAdminUsername());
            adminUser.setPassword(CommonConstants.ENCODER.encode(initDTO.getAdminPassword()));
            adminUser.setName(initDTO.getCompanyName() + "管理员");
            adminUser.setNickname(initDTO.getCompanyName() + "管理员");
            adminUser.setEmail(initDTO.getAdminEmail());
            adminUser.setPhone(initDTO.getAdminPhone());
            adminUser.setMobile(initDTO.getAdminPhone());
            adminUser.setEnable(true);
            adminUser.setLockFlag("0");
            adminUser.setCreateBy("system");
            adminUser.setCreateTime(LocalDateTime.now());

            userService.save(adminUser);

            // 分配超级管理员角色
            SysRole adminRole = roleService.getOne(
                Wrappers.<SysRole>lambdaQuery().eq(SysRole::getRoleCode, "ROLE_ADMIN")
            );

            if (adminRole != null) {
                SysUserRole userRole = new SysUserRole();
                userRole.setUserId(adminUser.getUserId());
                userRole.setRoleId(adminRole.getRoleId());
                userRoleService.save(userRole);
            }

            log.info("管理员用户创建成功: {}", initDTO.getAdminUsername());
            return adminUser.getUserId();

        } catch (Exception e) {
            log.error("创建管理员用户失败", e);
            throw new RuntimeException("创建管理员用户失败: " + e.getMessage());
        }
    }

    @Override
    public void initCompanyInfo(SystemInitDTO initDTO) {
        try {
            CompanyInfo companyInfo = new CompanyInfo();
            companyInfo.setCompanyName(initDTO.getCompanyName());
            companyInfo.setSocialCode(initDTO.getTaxNumber());
            companyInfo.setProvince(initDTO.getProvince());
            companyInfo.setCompanyAddress(initDTO.getCompanyAddress());
            companyInfo.setLogo(initDTO.getCompanyLogo());
            companyInfo.setTimeZone(initDTO.getTimeZone());

            SysConfig companyConfig = new SysConfig();
            companyConfig.setParamKey(ConfigConstants.COMPANY_INFO_KEY);
            companyConfig.setParamValue(JSONUtil.toJsonStr(companyInfo));
            companyConfig.setRemark("企业基本信息配置");
            configService.saveOrUpdateSysConfigByKey(companyConfig);

            // 保存企业联系信息
            saveConfigIfNotEmpty(ConfigConstants.COMPANY_PHONE, initDTO.getCompanyPhone(), "企业联系电话");
            saveConfigIfNotEmpty(ConfigConstants.COMPANY_EMAIL, initDTO.getCompanyEmail(), "企业邮箱");

            log.info("企业信息初始化完成");

        } catch (Exception e) {
            log.error("初始化企业信息失败", e);
            throw new RuntimeException("初始化企业信息失败: " + e.getMessage());
        }
    }

    @Override
    public void initIndustryInfo(SystemInitDTO initDTO) {
        try {
            saveConfigIfNotEmpty(ConfigConstants.COMPANY_INDUSTRY, initDTO.getIndustry(), "所属行业");
            saveConfigIfNotEmpty(ConfigConstants.COMPANY_INDUSTRY_SUB, initDTO.getIndustrySubcategory(), "行业细分");
            saveConfigIfNotEmpty(ConfigConstants.COMPANY_SCALE, initDTO.getCompanyScale(), "企业规模");

            log.info("行业信息初始化完成");

        } catch (Exception e) {
            log.error("初始化行业信息失败", e);
            throw new RuntimeException("初始化行业信息失败: " + e.getMessage());
        }
    }

    @Override
    public void initAgentConfig(SystemInitDTO initDTO) {
        try {
            if (ObjectUtil.isNotEmpty(initDTO.getEnabledAgents())) {
                SysConfig agentConfig = new SysConfig();
                agentConfig.setParamKey(ConfigConstants.ENABLED_AGENTS);
                agentConfig.setParamValue(JSONUtil.toJsonStr(initDTO.getEnabledAgents()));
                agentConfig.setRemark("启用的智能体列表");
                configService.saveOrUpdateSysConfigByKey(agentConfig);
            }
            log.info("智能体配置初始化完成");

        } catch (Exception e) {
            log.error("初始化智能体配置失败", e);
            throw new RuntimeException("初始化智能体配置失败: " + e.getMessage());
        }
    }

    @Override
    public void initAgentServiceConfig(SystemInitDTO initDTO) {
        try {
            // 保存Dify服务配置
            saveConfigIfNotEmpty(ConfigConstants.DIFY_SERVICE_URL, initDTO.getDifyServiceUrl(), "AI引擎的地址");
            saveConfigIfNotEmpty(ConfigConstants.DIFY_SERVICE_EMAIL, initDTO.getDifyServiceEmail(), "AI引擎的账号");
            saveConfigIfNotEmpty(ConfigConstants.DIFY_SERVICE_PASSWORD, initDTO.getDifyServicePassword(), "AI引擎的密码");

            DifyServer difyServer = DifyServerBuilder.create(DifyServerBuilder.DifyServerClientBuilder
                    .builder()
                    .baseUrl(initDTO.getDifyServiceUrl())
                    .serverProperties(new DifyProperties.Server(initDTO.getDifyServiceEmail(), initDTO.getDifyServicePassword()))
                    .serverToken(new DifyServerTokenDefault())
                    .clientConfig(new DifyProperties.ClientConfig())
                    .restClientBuilder(RestClient.builder())
                    .webClientBuilder(WebClient.builder())
                    .build());

            List<AppsResponse> appList = difyServer.apps(null, null);
            // 已有的智能体列表（来自数据库）
            List<AiAgent> existingAgents = aiAgentService.list();
            // 从已有智能体的配置中提取已存在的 backendId（如果有）以避免重复
            java.util.Set<String> existingBackendIds = new java.util.HashSet<>();
            for (AiAgent ag : existingAgents) {
                try {
                    if (StrUtil.isNotBlank(ag.getAgentConfig())) {
                        String bid = JSON.parseObject(ag.getAgentConfig()).getString("backendId");
                        if (StrUtil.isNotBlank(bid)) {
                            existingBackendIds.add(bid);
                        }
                    }
                } catch (Exception ignore) {
                    // 忽略单条解析异常，继续处理
                }
            }

            // 仅新增不存在的应用对应的智能体
            List<AiAgent> toSave = new ArrayList<>();
            for (AppsResponse app : appList) {
                log.debug("app: {}", app);
                String appId = app.getId();
                if (existingBackendIds.contains(appId)) {
                    log.debug("App {} 已存在于智能体列表(backendId 匹配)，跳过", appId);
                    continue;
                }
                List<ApiKeyResponse> apiKeys = difyServer.initAppApiKey(appId);
                AiAgent aiAgent = new AiAgent();
                aiAgent.setAgentName(app.getName());
                // 将 backendId 一并写入配置，便于后续判断去重
                aiAgent.setAgentConfig(JSON.toJSONString(Map.of(
                        "baseUrl", initDTO.getDifyServiceUrl() + "/v1",
                        "apiKey", apiKeys.get(0).getToken(),
                        "backendId", appId
                )));
                toSave.add(aiAgent);
            }
            if (!toSave.isEmpty()) {
                aiAgentService.saveBatch(toSave);
            }

            List<DatasetApiKeyResponse> datasetApiKeyResponses = difyServer.initDatasetApiKey();
            // 保存数据集API密钥
//            saveConfigIfNotEmpty(ConfigConstants.DATASET_API_KEY, initDTO.getDatasetApiKey(), "数据集API密钥");
            List<AiDataset> datasets = new ArrayList<>();
            DifyDataset difyDataset = DifyDatasetBuilder.create(DifyDatasetBuilder.DifyDatasetClientBuilder
                    .builder()
                    .baseUrl(initDTO.getDifyServiceUrl())
                    .clientConfig(new DifyProperties.ClientConfig())
                    .restClientBuilder(RestClient.builder().defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + datasetApiKeyResponses.get(0).getToken()))
                    .webClientBuilder(WebClient.builder().defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + datasetApiKeyResponses.get(0).getToken()))
                    .build());

            DifyPageResult<DatasetResponse> datasetResponse = difyDataset.page(new DatasetPageRequest());
            datasetResponse.getData().forEach(dataset -> {
                log.debug("dataset: {}", dataset);
                AiDataset aiDataset = new AiDataset();
                aiDataset.setBackendId(dataset.getId());
                aiDataset.setTitle(dataset.getName());
                datasets.add(aiDataset);
            });
            aiDatasetService.saveBatch(datasets);

            saveConfigIfNotEmpty(ConfigConstants.DATASET_API_KEY, datasetApiKeyResponses.get(0).getToken(), "数据集的apiKey");
            log.info("智能体服务配置初始化完成");

        } catch (Exception e) {
            log.error("初始化智能体服务配置失败", e);
            throw new RuntimeException("初始化智能体服务配置失败: " + e.getMessage());
        }
    }

    @Override
    public void initSystemConfig(SystemInitDTO initDTO) {
        try {
            saveConfigIfNotEmpty(ConfigConstants.SYSTEM_TIMEZONE, initDTO.getTimeZone(), "系统时区");
            saveConfigIfNotEmpty(ConfigConstants.SYSTEM_LANGUAGE, initDTO.getLanguage(), "系统语言");

            SysConfig emailNotifyConfig = new SysConfig();
            emailNotifyConfig.setParamKey(ConfigConstants.EMAIL_NOTIFICATION_ENABLED);
            emailNotifyConfig.setParamValue(initDTO.getEnableEmailNotification().toString());
            emailNotifyConfig.setRemark("是否启用邮件通知");
            configService.saveOrUpdateSysConfigByKey(emailNotifyConfig);

            if (initDTO.getEnableEmailNotification()) {
                initMailConfig(initDTO);
            }


            SysConfig smsNotifyConfig = new SysConfig();
            smsNotifyConfig.setParamKey(ConfigConstants.SMS_NOTIFICATION_ENABLED);
            smsNotifyConfig.setParamValue(initDTO.getEnableSmsNotification().toString());
            smsNotifyConfig.setRemark("是否启用短信通知");
            configService.saveOrUpdateSysConfigByKey(smsNotifyConfig);

            log.info("系统配置初始化完成");

        } catch (Exception e) {
            log.error("初始化系统配置失败", e);
            throw new RuntimeException("初始化系统配置失败: " + e.getMessage());
        }
    }

    @Override
    public void initMailConfig(SystemInitDTO initDTO) {
        try {
            saveConfigIfNotEmpty(ConfigConstants.MAIL_HOST, initDTO.getMailHost(), "邮箱服务器地址");
            saveConfigIfNotEmpty(ConfigConstants.MAIL_PORT, initDTO.getMailPort(), "邮箱服务器端口");
            saveConfigIfNotEmpty(ConfigConstants.MAIL_USERNAME, initDTO.getMailUsername(), "邮箱服务器用户名");
            saveConfigIfNotEmpty(ConfigConstants.MAIL_PASSWORD, initDTO.getMailPassword(), "邮箱服务器密码");
            saveConfigIfNotEmpty(ConfigConstants.MAIL_PROTOCOL, initDTO.getMailProtocol(), "邮箱服务器协议");
            saveConfigIfNotEmpty(ConfigConstants.MAIL_AUTH, initDTO.getMailAuth(), "邮箱服务器是否需要认证");
            saveConfigIfNotEmpty(ConfigConstants.MAIL_STARTTLS, initDTO.getMailStarttls(), "邮箱服务器是否启用STARTTLS");

            log.info("邮箱配置信息初始化完成");

        } catch (Exception e) {
            log.error("初始化邮箱配置信息失败", e);
            throw new RuntimeException("初始化邮箱配置信息失败: " + e.getMessage());
        }
    }

    @Override
    public void createDefaultRolesAndPermissions() {
        try {
            // 创建超级管理员角色
            createRoleIfNotExists("ROLE_ADMIN", "超级管理员", "系统超级管理员，拥有所有权限");

            // 创建普通管理员角色
            createRoleIfNotExists("ROLE_MANAGER", "管理员", "系统管理员，拥有大部分管理权限");

            // 创建普通用户角色
            createRoleIfNotExists("ROLE_USER", "普通用户", "系统普通用户，拥有基本操作权限");

            log.info("默认角色创建完成");

        } catch (Exception e) {
            log.error("创建默认角色失败", e);
            throw new RuntimeException("创建默认角色失败: " + e.getMessage());
        }
    }

    @Override
    public Long createDefaultDepartment(String companyName) {
        try {
            // 检查是否已存在根部门
            SysDept existingDept = deptService.getOne(
                    Wrappers.<SysDept>lambdaQuery()
                            .eq(SysDept::getParentId, 0L)
                            .eq(SysDept::getDelFlag, "0")
                            .last("LIMIT 1")
            );

            if (existingDept != null) {
                log.debug("根部门已存在: {}", existingDept.getName());
                return existingDept.getDeptId();
            }

            // 创建根部门
            SysDept rootDept = new SysDept();
            rootDept.setName(StrUtil.isNotBlank(companyName) ? companyName : "总公司");
            rootDept.setParentId(0L);
            rootDept.setSortOrder(0);
            rootDept.setDelFlag("0");
            rootDept.setCreateBy("system");
            rootDept.setCreateTime(LocalDateTime.now());

            deptService.save(rootDept);

            log.info("默认部门创建完成: {}", rootDept.getName());
            return rootDept.getDeptId();

        } catch (Exception e) {
            log.error("创建默认部门失败", e);
            throw new RuntimeException("创建默认部门失败: " + e.getMessage());
        }
    }

    /**
     * 创建角色（如果不存在）
     */
    private void createRoleIfNotExists(String roleCode, String roleName, String roleDesc) {
        SysRole existingRole = roleService.getOne(
                Wrappers.<SysRole>lambdaQuery().eq(SysRole::getRoleCode, roleCode)
        );

        if (existingRole == null) {
            SysRole role = new SysRole();
            role.setRoleCode(roleCode);
            role.setRoleName(roleName);
            role.setRoleDesc(roleDesc);
            role.setEnable(true);
            role.setDelFlag("0");
            role.setCreateBy("system");
            role.setCreateTime(LocalDateTime.now());
            roleService.save(role);
            log.info("角色创建成功: {}", roleName);
        }
    }

    /**
     * 保存配置（如果值不为空）
     */
    private void saveConfigIfNotEmpty(String key, String value, String remark) {
        if (StrUtil.isNotBlank(value)) {
            SysConfig config = new SysConfig();
            config.setParamKey(key);
            config.setParamValue(value);
            config.setRemark(remark);
            configService.saveOrUpdateSysConfigByKey(config);
        }
    }

}
