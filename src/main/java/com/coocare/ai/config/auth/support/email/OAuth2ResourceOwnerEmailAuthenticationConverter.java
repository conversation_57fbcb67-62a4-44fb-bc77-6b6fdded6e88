package com.coocare.ai.config.auth.support.email;

import com.coocare.ai.config.auth.reource.util.OAuth2EndpointUtils;
import com.coocare.ai.config.auth.support.base.OAuth2ResourceOwnerBaseAuthenticationConverter;
import com.coocare.ai.config.constants.SecurityConstants;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.OAuth2ErrorCodes;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Set;

/**
 * 邮箱验证码登录转换器
 *
 * <AUTHOR>
 */
public class OAuth2ResourceOwnerEmailAuthenticationConverter
        extends OAuth2ResourceOwnerBaseAuthenticationConverter<OAuth2ResourceOwnerEmailAuthenticationToken> {

    /**
     * 是否支持此convert
     * @param grantType 授权类型
     * @return
     */
    @Override
    public boolean support(String grantType) {
        return SecurityConstants.GRANT_TYPE_EMAIL_CODE.equals(grantType);
    }

    @Override
    public OAuth2ResourceOwnerEmailAuthenticationToken buildToken(Authentication clientPrincipal, Set requestedScopes,
                                                                  Map additionalParameters) {
        return new OAuth2ResourceOwnerEmailAuthenticationToken(
                new AuthorizationGrantType(SecurityConstants.GRANT_TYPE_EMAIL_CODE),
                clientPrincipal,
                requestedScopes,
                additionalParameters);
    }

    /**
     * 校验扩展参数 邮箱验证码模式邮箱和验证码必须不为空
     * @param request 参数列表
     */
    @Override
    public void checkParams(HttpServletRequest request) {
        MultiValueMap<String, String> parameters = OAuth2EndpointUtils.getParameters(request);

        // EMAIL (REQUIRED)
        String email = parameters.getFirst(SecurityConstants.OAUTH_PARAMETER_NAME_EMAIL);
        if (!StringUtils.hasText(email) || parameters.get(SecurityConstants.OAUTH_PARAMETER_NAME_EMAIL).size() != 1) {
            OAuth2EndpointUtils.throwError(OAuth2ErrorCodes.INVALID_REQUEST, SecurityConstants.OAUTH_PARAMETER_NAME_EMAIL,
                    OAuth2EndpointUtils.ACCESS_TOKEN_REQUEST_ERROR_URI);
        }

        // EMAIL_CAPTCHA (REQUIRED)
        String emailCaptcha = parameters.getFirst(SecurityConstants.OAUTH_PARAMETER_NAME_EMAIL_CAPTCHA);
        if (!StringUtils.hasText(emailCaptcha) || parameters.get(SecurityConstants.OAUTH_PARAMETER_NAME_EMAIL_CAPTCHA).size() != 1) {
            OAuth2EndpointUtils.throwError(OAuth2ErrorCodes.INVALID_REQUEST, SecurityConstants.OAUTH_PARAMETER_NAME_EMAIL_CAPTCHA,
                    OAuth2EndpointUtils.ACCESS_TOKEN_REQUEST_ERROR_URI);
        }
    }
}
