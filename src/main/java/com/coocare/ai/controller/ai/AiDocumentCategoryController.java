package com.coocare.ai.controller.ai;

import cn.hutool.core.bean.BeanUtil;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.AiDocumentCategory;
import com.coocare.ai.entity.dto.AiDocumentCategoryDTO;
import com.coocare.ai.entity.dto.AiDocumentCategoryQueryDTO;
import com.coocare.ai.entity.vo.AiDocumentCategoryVO;
import com.coocare.ai.service.AiDatasetService;
import com.coocare.ai.service.AiDocumentCategoryService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * AI 文档分类管理控制器
 * 提供分类分页、树结构、增删改查、状态切换与路径查询等接口
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Tag(name = "AI文档分类管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/document/category")
public class AiDocumentCategoryController {

    private final AiDocumentCategoryService categoryService;
    private final AiDatasetService datasetService;

    /**
     * 分页查询分类列表
     */
    @Operation(summary = "分页查询分类列表", description = "根据查询条件分页获取文档分类列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/page")
    public AjaxResult<PageUtils> page(Long datasetID, AiDocumentCategoryQueryDTO queryDTO) {
        try {



            PageDomain pageDomain = new PageDomain();
            pageDomain.setPageNo(queryDTO.getPageNo());
            pageDomain.setPageSize(queryDTO.getPageSize());

            PageUtils pageUtils = categoryService.pageInfo(pageDomain, queryDTO.getCategoryName(),
                    queryDTO.getLevel(), queryDTO.getParentId());
            
            // 转换为VO
            List<AiDocumentCategory> records = (List<AiDocumentCategory>) pageUtils.getList();
            List<AiDocumentCategoryVO> voList = records.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            pageUtils.setList(voList);
            
            return AjaxResult.ok(pageUtils);
        } catch (Exception e) {
            log.error("分页查询分类列表失败", e);
            return AjaxResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取分类树结构
     */
    @Operation(summary = "获取分类树结构", description = "获取完整的分类树结构或指定父级下的子树")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/tree")
    public AjaxResult<List<AiDocumentCategoryVO>> categoryTree(
            @Parameter(description = "父级分类ID，为空时获取完整树结构") @RequestParam(required = false) Long parentId) {
        try {
            List<AiDocumentCategory> categories = categoryService.getCategoryTree(parentId);
            List<AiDocumentCategoryVO> voList = categories.stream()
                    .map(this::convertToTreeVO)
                    .collect(Collectors.toList());
            return AjaxResult.ok(voList);
        } catch (Exception e) {
            log.error("获取分类树结构失败", e);
            return AjaxResult.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据父级ID获取子分类列表
     */
    @Operation(summary = "根据父级ID获取子分类列表", description = "获取指定父级分类下的直接子分类")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/children")
    public AjaxResult<List<AiDocumentCategoryVO>> getCategoryChildren(
            @Parameter(description = "父级分类ID，为空时获取顶级分类") @RequestParam(required = false) Long parentId) {
        try {
            List<AiDocumentCategory> categories = categoryService.getChildrenByParentId(parentId);
            List<AiDocumentCategoryVO> voList = categories.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            return AjaxResult.ok(voList);
        } catch (Exception e) {
            log.error("获取子分类列表失败", e);
            return AjaxResult.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询分类详情
     */
    @Operation(summary = "根据ID查询分类详情", description = "根据分类ID获取分类的详细信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "分类不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}")
    public AjaxResult<AiDocumentCategoryVO> getCategoryById(
            @Parameter(description = "分类ID", required = true) @PathVariable Long id) {
        try {
            AiDocumentCategory category = categoryService.getById(id);
            if (category == null) {
                return AjaxResult.failed("分类不存在");
            }
            AiDocumentCategoryVO vo = convertToVO(category);
            return AjaxResult.ok(vo);
        } catch (Exception e) {
            log.error("查询分类详情失败", e);
            return AjaxResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建产品型号分类（第一层）
     */
    @Operation(summary = "创建产品型号分类", description = "创建第一层分类（产品型号）")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/product-model")
    public AjaxResult<String> createProductModel(@Valid @RequestBody AiDocumentCategoryDTO categoryDTO) {
        try {
            AiDocumentCategory category = BeanUtil.copyProperties(categoryDTO, AiDocumentCategory.class);
            boolean result = categoryService.createProductModel(category);
            return result ? AjaxResult.ok("创建成功") : AjaxResult.failed("创建失败");
        } catch (IllegalArgumentException e) {
            return AjaxResult.failed(e.getMessage());
        } catch (Exception e) {
            log.error("创建产品型号分类失败", e);
            return AjaxResult.failed("创建失败: " + e.getMessage());
        }
    }

    /**
     * 创建产品系列分类（第二层）
     */
    @Operation(summary = "创建产品系列分类", description = "创建第二层分类（产品系列），会自动生成第三层文档类型分类")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/product-series")
    public AjaxResult<String> createProductSeries(@Valid @RequestBody AiDocumentCategoryDTO categoryDTO) {
        try {
            AiDocumentCategory category = BeanUtil.copyProperties(categoryDTO, AiDocumentCategory.class);
            boolean result = categoryService.createProductSeries(category);
            return result ? AjaxResult.ok("创建成功") : AjaxResult.failed("创建失败");
        } catch (IllegalArgumentException e) {
            return AjaxResult.failed(e.getMessage());
        } catch (Exception e) {
            log.error("创建产品系列分类失败", e);
            return AjaxResult.failed("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新分类信息
     */
    @Operation(summary = "更新分类信息", description = "更新分类的基本信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "404", description = "分类不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/{id}")
    public AjaxResult<String> updateCategoryById(
            @Parameter(description = "分类ID", required = true) @PathVariable Long id,
            @Valid @RequestBody AiDocumentCategoryDTO categoryDTO) {
        try {
            AiDocumentCategory category = BeanUtil.copyProperties(categoryDTO, AiDocumentCategory.class);
            category.setCategoryId(id);
            boolean result = categoryService.updateCategory(category);
            return result ? AjaxResult.ok("更新成功") : AjaxResult.failed("更新失败");
        } catch (IllegalArgumentException e) {
            return AjaxResult.failed(e.getMessage());
        } catch (Exception e) {
            log.error("更新分类信息失败", e);
            return AjaxResult.failed("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除分类
     */
    @Operation(summary = "删除分类", description = "删除指定分类（逻辑删除），如果存在子分类则不允许删除")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "400", description = "存在子分类，无法删除"),
        @ApiResponse(responseCode = "404", description = "分类不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("/{id}")
    public AjaxResult<String> deleteCategoryById(
            @Parameter(description = "分类ID", required = true) @PathVariable Long id) {
        try {
            boolean result = categoryService.deleteCategory(id);
            return result ? AjaxResult.ok("删除成功") : AjaxResult.failed("删除失败");
        } catch (IllegalArgumentException e) {
            return AjaxResult.failed(e.getMessage());
        } catch (Exception e) {
            log.error("删除分类失败", e);
            return AjaxResult.failed("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除分类
     */
    @Operation(summary = "批量删除分类", description = "批量删除指定的分类")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "400", description = "存在子分类，无法删除"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("/batch")
    public AjaxResult<String> deleteBatchCategoryById(@RequestBody List<Long> categoryIds) {
        try {
            boolean result = categoryService.deleteBatch(categoryIds);
            return result ? AjaxResult.ok("删除成功") : AjaxResult.failed("删除失败");
        } catch (IllegalArgumentException e) {
            return AjaxResult.failed(e.getMessage());
        } catch (Exception e) {
            log.error("批量删除分类失败", e);
            return AjaxResult.failed("删除失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用分类
     */
    @Operation(summary = "启用/禁用分类", description = "更新分类的启用状态")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "操作成功"),
        @ApiResponse(responseCode = "404", description = "分类不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/{id}/status/{status}")
    public AjaxResult<String> updateCategoryStatus(
            @Parameter(description = "分类ID", required = true) @PathVariable Long id,
            @Parameter(description = "状态：0-禁用，1-启用", required = true) @PathVariable Boolean status) {
        try {
            boolean result = categoryService.updateStatus(id, status);
            return result ? AjaxResult.ok("操作成功") : AjaxResult.failed("操作失败");
        } catch (Exception e) {
            log.error("更新分类状态失败", e);
            return AjaxResult.failed("操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取分类路径
     */
    @Operation(summary = "获取分类路径", description = "获取指定分类的完整路径")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "分类不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}/path")
    public AjaxResult<String> getCategoryPath(
            @Parameter(description = "分类ID", required = true) @PathVariable Long id) {
        try {
            String path = categoryService.getCategoryPath(id);
            return AjaxResult.ok(path);
        } catch (Exception e) {
            log.error("获取分类路径失败", e);
            return AjaxResult.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 转换为VO对象
     */
    private AiDocumentCategoryVO convertToVO(AiDocumentCategory category) {
        AiDocumentCategoryVO vo = BeanUtil.copyProperties(category, AiDocumentCategoryVO.class);
        vo.setCategoryPath(categoryService.getCategoryPath(category.getCategoryId()));
        vo.setHasChildren(categoryService.hasChildren(category.getCategoryId()));
        return vo;
    }

    /**
     * 转换为树形VO对象
     */
    private AiDocumentCategoryVO convertToTreeVO(AiDocumentCategory category) {
        AiDocumentCategoryVO vo = convertToVO(category);
        if (category.getChildren() != null && !category.getChildren().isEmpty()) {
            List<AiDocumentCategoryVO> childrenVO = category.getChildren().stream()
                    .map(this::convertToTreeVO)
                    .collect(Collectors.toList());
            vo.setChildren(childrenVO);
        }
        return vo;
    }
}
