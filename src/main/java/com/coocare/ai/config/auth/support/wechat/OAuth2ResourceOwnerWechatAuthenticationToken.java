//package com.coocare.ai.config.auth.support.wechat;
/**
 * 微信登录授权 Token（示例/占位）
 * 若启用请补全上下文结构
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
//
//import com.coocare.ai.config.auth.support.base.OAuth2ResourceOwnerBaseAuthenticationToken;
//import org.springframework.security.core.Authentication;
//import org.springframework.security.oauth2.core.AuthorizationGrantType;
//
//import java.util.Map;
//import java.util.Set;
//
///**
// * <AUTHOR>
// * @description 微信code登录token信息
// */
//public class OAuth2ResourceOwnerWechatAuthenticationToken extends OAuth2ResourceOwnerBaseAuthenticationToken {
//
//	public OAuth2ResourceOwnerWechatAuthenticationToken(AuthorizationGrantType authorizationGrantType,
//			Authentication clientPrincipal, Set<String> scopes, Map<String, Object> additionalParameters) {
//		super(authorizationGrantType, clientPrincipal, scopes, additionalParameters);
//	}
//
//}
