package com.coocare.ai.event;

import com.coocare.ai.entity.vo.AiDocumentVO;
import io.github.guoshiqiufeng.dify.dataset.enums.document.DocActionEnum;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;

/**
 * <p>
 * 文档操作事件
 * 在文档上传、删除、状态更改等操作完成后发布此事件，用于触发异步回调通知
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Getter
public class DocumentUploadEvent extends ApplicationEvent {

    private static final long serialVersionUID = 1L;

    /**
     * 上传的文档信息
     */
    private final AiDocumentVO document;

    /**
     * 操作类型
     */
    private final OperationType operationType;

    private final Long datasetId;

    /**
     * 操作时间
     */
    private final LocalDateTime operationTime;

    /**
     * 操作用户
     */
    private final String operationUser;

    /**
     * 错误信息（如果上传失败）
     */
    private final String errorMessage;

    /**
     * 额外的上下文信息（如状态变更的详细信息）
     */
    private final String contextInfo;

    /**
     * 操作动作
     */
    private final DocActionEnum action;

    private final MultipartFile file;

    /**
     * 完整构造函数
     *
     * @param source        事件源
     * @param document      文档信息
     * @param operationType 操作类型
     * @param operationUser 操作用户
     * @param errorMessage  错误信息
     * @param contextInfo   上下文信息
     */
    public DocumentUploadEvent(Object source, AiDocumentVO document, OperationType operationType, Long datasetId,
                               String operationUser, String errorMessage, String contextInfo, DocActionEnum action, MultipartFile file) {
        super(source);
        this.document = document;
        this.operationType = operationType;
        this.datasetId = datasetId;
        this.operationTime = LocalDateTime.now();
        this.operationUser = operationUser;
        this.errorMessage = errorMessage;
        this.contextInfo = contextInfo;
        this.action = action;
        this.file = file;
    }

    /**
     * 获取操作详细描述
     */
    public String getOperationDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append(String.format("用户[%s]", operationUser));

        if (document != null) {
            desc.append(String.format("针对文档[%s]", document.getDocumentName()));
        }

        desc.append(String.format("进行了[%s]操作", operationType.getDescription()));

        if (contextInfo != null && !contextInfo.trim().isEmpty()) {
            desc.append(String.format("，%s", contextInfo));
        }

        return desc.toString();
    }

    @Override
    public String toString() {
        return String.format("DocumentOperationEvent{document=%s, operationType=%s, operationTime=%s, operationUser='%s', errorMessage='%s'}",
                document != null ? document.getDocumentName() : "null",
                operationType, operationTime, operationUser, errorMessage);
    }


    /**
     * 操作类型枚举
     */
    @Getter
    public enum OperationType {
        /**
         * 文档上传
         */
        UPLOAD("upload", "上传"),

        /**
         * 文档删除
         */
        DELETE("delete", "删除"),

        /**
         * 状态更改
         */
        STATUS_CHANGE("status_change", "状态更改"),

        /**
         * 文档更新
         */
        UPDATE("update", "更新");

        private final String code;
        private final String description;

        OperationType(String code, String description) {
            this.code = code;
            this.description = description;
        }

    }
}
