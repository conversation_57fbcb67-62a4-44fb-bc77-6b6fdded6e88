package com.coocare.ai.config.auth.support.core;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.coocare.ai.config.auth.reource.service.CustomUserDetailsService;
import com.coocare.ai.config.constants.CacheConstants;
import com.coocare.ai.config.constants.SecurityConstants;
import com.coocare.ai.config.exception.RRException;
import com.coocare.ai.utils.WebUtils;
import lombok.Setter;
import lombok.SneakyThrows;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsPasswordService;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.web.authentication.www.BasicAuthenticationConverter;
import org.springframework.util.Assert;

import java.util.Comparator;
import java.util.Map;
import java.util.Optional;

/**
 * 自定义 DaoAuthenticationProvider
 * 支持多客户端多授权类型的用户加载与登录失败计数/锁定逻辑
 *
 * <AUTHOR>
 * @since 2022-06-04
 */

public class DaoAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {

    /**
     * The plaintext password used to perform PasswordEncoder#matches(CharSequence,
     * String) on when the user is not found to avoid SEC-2056.
     */
    private static final String USER_NOT_FOUND_PASSWORD = "userNotFoundPassword";

    private final static BasicAuthenticationConverter basicConvert = new BasicAuthenticationConverter();

    private PasswordEncoder passwordEncoder;

    /**
     * The password used to perform {@link PasswordEncoder#matches(CharSequence, String)}
     * on when the user is not found to avoid SEC-2056. This is necessary, because some
     * {@link PasswordEncoder} implementations will short circuit if the password is not
     * in a valid format.
     */
    private volatile String userNotFoundEncodedPassword;

    @Setter
    private UserDetailsService userDetailsService;

    private RedisTemplate redisTemplate;

    @Setter
    private UserDetailsPasswordService userDetailsPasswordService;

    public DaoAuthenticationProvider() {
        setMessageSource(SpringUtil.getBean("messageSource"));
        setPasswordEncoder(PasswordEncoderFactories.createDelegatingPasswordEncoder());
        this.redisTemplate = SpringUtil.getBean(RedisTemplate.class);
        this.setHideUserNotFoundExceptions(false);
    }

    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails,
                                                  UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {

        // 邮箱登录模式不用校验密码
        String grantType = WebUtils.getRequest().getParameter(OAuth2ParameterNames.GRANT_TYPE);
        if (StrUtil.equals(SecurityConstants.GRANT_TYPE_EMAIL_CODE, grantType)) {
            return;
        }
        if (authentication.getCredentials() == null) {
            this.logger.debug("Failed to authenticate since no credentials provided");
            throw new BadCredentialsException(this.messages
                    .getMessage("AbstractUserDetailsAuthenticationProvider.badCredentials"));
        }
        String presentedPassword = authentication.getCredentials().toString();
        if (!this.passwordEncoder.matches(presentedPassword, userDetails.getPassword())) {
            this.logger.debug("Failed to authenticate since password does not match stored value");
            String clientId = WebUtils.extractClientId(WebUtils.getRequest().getHeader(HttpHeaders.AUTHORIZATION)).orElse(null);
            // todo
            //if (ClientEnum.PORTAL.getCode().equals(clientId)
            //        || ClientEnum.STORE.getCode().equals(clientId)
            //        || ClientEnum.AGENT.getCode().equals(clientId)
            //        || ClientEnum.APP.getCode().equals(clientId)) {
            //    // 门户、商家、客服、app不考虑密码错误次数
            //    throw new GeniusException("密码错误");
            //}
            redisTemplate.setValueSerializer(new StringRedisSerializer());
            String key = String.format("%s:%s:%s", CacheConstants.LOGIN_ERROR_TIMES, clientId, authentication.getName());
            Long times = redisTemplate.opsForValue().increment(key);
            if (null != times && times == 1) {
                throw new RRException("user.password.error");
            }
            Long errorTimes = 6L;
            long leaveTimes = errorTimes - times;
            if (leaveTimes <= 0) {
                throw new LockedException("user.account.lock.pre" + "由于多次输入错误密码，请稍后再试或重置密码");
            }
            throw new RRException("user.password.check.remaining", leaveTimes);
        }
    }

    @SneakyThrows
    @Override
    protected final UserDetails retrieveUser(String username, UsernamePasswordAuthenticationToken authentication) {
        prepareTimingAttackProtection();
        String grantType = WebUtils.getRequest().getParameter(OAuth2ParameterNames.GRANT_TYPE);
        String clientId = WebUtils.getRequest().getParameter(OAuth2ParameterNames.CLIENT_ID);

        if (StrUtil.isBlank(clientId)) {
            clientId = basicConvert.convert(WebUtils.getRequest()).getName();
        }

        Map<String, CustomUserDetailsService> userDetailsServiceMap = SpringUtil
                .getBeansOfType(CustomUserDetailsService.class);

        String finalClientId = clientId;
        Optional<CustomUserDetailsService> optional = userDetailsServiceMap.values()
                .stream()
                .filter(service -> service.support(finalClientId, grantType))
                .max(Comparator.comparingInt(Ordered::getOrder));

        if (optional.isEmpty()) {
            throw new InternalAuthenticationServiceException("UserDetailsService error , not register");
        }

        try {
            UserDetails loadedUser = optional.get().loadUserByUsername(username);
            if (loadedUser == null) {
                throw new InternalAuthenticationServiceException(
                        "UserDetailsService returned null, which is an interface contract violation");
            }
            return loadedUser;
        } catch (UsernameNotFoundException ex) {
            mitigateAgainstTimingAttack(authentication);
            throw ex;
        } catch (Exception ex) {
            throw ex;
        }
    }

    @Override
    protected Authentication createSuccessAuthentication(Object principal, Authentication authentication,
                                                         UserDetails user) {
        boolean upgradeEncoding = this.userDetailsPasswordService != null
                && this.passwordEncoder.upgradeEncoding(user.getPassword());
        if (upgradeEncoding) {
            String presentedPassword = authentication.getCredentials().toString();
            String newPassword = this.passwordEncoder.encode(presentedPassword);
            user = this.userDetailsPasswordService.updatePassword(user, newPassword);
        }
        return super.createSuccessAuthentication(principal, authentication, user);
    }

    private void prepareTimingAttackProtection() {
        if (this.userNotFoundEncodedPassword == null) {
            this.userNotFoundEncodedPassword = this.passwordEncoder.encode(USER_NOT_FOUND_PASSWORD);
        }
    }

    private void mitigateAgainstTimingAttack(UsernamePasswordAuthenticationToken authentication) {
        if (authentication.getCredentials() != null) {
            String presentedPassword = authentication.getCredentials().toString();
            this.passwordEncoder.matches(presentedPassword, this.userNotFoundEncodedPassword);
        }
    }

    protected PasswordEncoder getPasswordEncoder() {
        return this.passwordEncoder;
    }

    /**
     * Sets the PasswordEncoder instance to be used to encode and validate passwords. If
     * not set, the password will be compared using
     * {@link PasswordEncoderFactories#createDelegatingPasswordEncoder()}
     *
     * @param passwordEncoder must be an instance of one of the {@code PasswordEncoder}
     *                        types.
     */
    public void setPasswordEncoder(PasswordEncoder passwordEncoder) {
        Assert.notNull(passwordEncoder, "passwordEncoder cannot be null");
        this.passwordEncoder = passwordEncoder;
        this.userNotFoundEncodedPassword = null;
    }

    protected UserDetailsService getUserDetailsService() {
        return this.userDetailsService;
    }

}
