# 智能体服务配置使用说明

## 概述

本文档说明如何在系统初始化阶段配置后端智能体服务URL和数据集API key，以及这些配置如何在系统中使用。

## 功能特性

1. **初始化阶段配置输入**：在系统首次初始化时可以配置智能体服务相关参数
2. **配置持久化存储**：配置信息保存到数据库config表中
3. **全局配置管理**：应用启动时自动加载配置到内存缓存
4. **动态配置更新**：支持配置的动态更新和缓存刷新
5. **自动重启加载**：系统重启后自动加载已保存的配置

## 配置项说明

### 智能体服务配置

| 配置项 | 字段名 | 描述 | 是否必填 |
|--------|--------|------|----------|
| 后端智能体服务URL | agentServiceUrl | 智能体服务的API地址 | 否 |
| 数据集API密钥 | datasetApiKey | 用于访问数据集的API密钥 | 否 |
| Dify服务URL | difyServiceUrl | Dify服务的API地址 | 否 |
| Dify服务邮箱 | difyServiceEmail | Dify服务登录邮箱 | 否 |
| Dify服务密码 | difyServicePassword | Dify服务登录密码 | 否 |

## 使用方式

### 1. 系统初始化配置

在系统首次初始化时，通过SystemInitDTO传入配置：

```json
{
  "adminUsername": "admin",
  "adminPassword": "password",
  "adminName": "管理员",
  "companyName": "示例公司",
  "taxNumber": "*********",
  "province": "北京市",
  "city": "北京市",
  "district": "朝阳区",
  "industry": "科技",
  
  // 智能体服务配置
  "agentServiceUrl": "http://localhost:8080/api/v1",
  "datasetApiKey": "dataset-ub2T9aQrSdtgqyGXbQUCNzes",
  "difyServiceUrl": "http://localhost:8080/v1",
  "difyServiceEmail": "<EMAIL>",
  "difyServicePassword": "password123"
}
```

### 2. 代码中使用配置

#### 通过GlobalConfigManager获取配置

```java
@Service
@RequiredArgsConstructor
public class YourService {
    
    private final GlobalConfigManager globalConfigManager;
    
    public void someMethod() {
        // 获取智能体服务URL
        String agentUrl = globalConfigManager.getAgentServiceUrl();
        
        // 获取数据集API密钥
        String apiKey = globalConfigManager.getDatasetApiKey();
        
        // 获取Dify服务URL
        String difyUrl = globalConfigManager.getDifyServiceUrl();
        
        // 检查系统是否已初始化
        boolean initialized = globalConfigManager.isSystemInitialized();
    }
}
```

#### 通过DataSetUtils使用配置

```java
@Service
@RequiredArgsConstructor
public class DocumentService {
    
    private final DataSetUtils dataSetUtils;
    
    public boolean uploadDocument(String filePath) {
        // DataSetUtils会自动使用全局配置的数据集API key和服务URL
        return dataSetUtils.addFileToDataSet(null, filePath);
    }
    
    public boolean checkConnection() {
        // 检查Dify服务连接状态
        return dataSetUtils.checkDifyServiceConnection();
    }
}
```

### 3. 配置管理API

系统提供了配置管理的REST API：

#### 获取智能体服务配置
```http
GET /sys/config/agent-service
```

#### 获取系统配置信息
```http
GET /sys/config/system
```

#### 刷新全局配置缓存
```http
POST /sys/config/refresh
```

#### 测试Dify服务连接
```http
GET /sys/config/test/dify-connection
```

#### 获取数据集信息
```http
GET /sys/config/test/dataset-info?datasetId=your-dataset-id
```

## 配置存储结构

配置信息存储在`sys_config`表中，使用以下键名：

| 配置键 | 描述 |
|--------|------|
| AGENT_SERVICE_URL | 后端智能体服务URL |
| DATASET_API_KEY | 数据集API密钥 |
| DIFY_SERVICE_URL | Dify服务URL |
| DIFY_SERVICE_EMAIL | Dify服务邮箱 |
| DIFY_SERVICE_PASSWORD | Dify服务密码 |

## 配置加载机制

1. **应用启动时**：GlobalConfigManager实现ApplicationRunner接口，在应用启动完成后自动加载所有配置到内存缓存
2. **配置更新时**：当通过SysConfigService更新配置时，会自动更新内存缓存
3. **系统初始化完成后**：会触发配置重新加载，确保所有新配置生效

## 线程安全

GlobalConfigManager使用读写锁（ReentrantReadWriteLock）确保配置读取和更新的线程安全：
- 读操作使用读锁，支持并发读取
- 写操作使用写锁，确保更新时的数据一致性

## 注意事项

1. **配置优先级**：DataSetUtils中，如果方法参数传入了datasetId，会优先使用参数值，否则使用全局配置
2. **配置验证**：在使用配置前建议先检查配置是否为空
3. **错误处理**：配置加载失败时会记录日志，但不会影响应用启动
4. **安全性**：密码等敏感配置在API返回时会被隐藏，只返回是否已配置的状态

## 扩展说明

如需添加新的配置项：

1. 在`ConfigConstants`中定义新的常量键
2. 在`SystemInitDTO`中添加对应字段
3. 在`SystemInitServiceImpl`中添加保存逻辑
4. 在`GlobalConfigManager`中添加获取方法
5. 根据需要在相关服务中使用新配置

## 示例场景

### 场景1：系统初始化
管理员首次部署系统时，通过初始化界面输入智能体服务配置，系统自动保存并在后续使用中生效。

### 场景2：配置更新
系统运行过程中需要更换智能体服务地址，管理员通过配置管理界面更新，系统自动刷新缓存，新配置立即生效。

### 场景3：服务重启
系统重启后，GlobalConfigManager自动从数据库加载之前保存的配置，无需重新配置。
