<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AiDocumentCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AiDocumentCategory">
        <id column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="category_code" property="categoryCode"/>
        <result column="parent_id" property="parentId"/>
        <result column="level" property="level"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="description" property="description"/>
        <result column="icon" property="icon"/>
        <result column="status" property="status"/>
        <result column="dataset_id" property="dataSetId"/>
        <result column="is_system" property="isSystem"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        category_id, category_name, category_code, parent_id, level, sort_order, 
        description, icon, status, dataset_id, is_system, create_by, create_time, update_by,
        update_time, del_flag
    </sql>

    <!-- 根据父级ID查询子分类列表 -->
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_document_category
        WHERE del_flag = '0'
        <choose>
            <when test="parentId == null">
                AND parent_id IS NULL
            </when>
            <otherwise>
                AND parent_id = #{parentId}
            </otherwise>
        </choose>
        ORDER BY sort_order ASC, create_time ASC
    </select>


    <!-- 查询分类树结构 -->
    <select id="selectCategoryTree" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_document_category
        WHERE del_flag = '0'
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        ORDER BY level ASC, sort_order ASC, create_time ASC
    </select>

    <!-- 根据分类名称和父级ID查询分类 -->
    <select id="selectByNameAndParentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_document_category
        WHERE del_flag = '0'
        AND category_name = #{categoryName}
        <choose>
            <when test="parentId == null">
                AND parent_id IS NULL
            </when>
            <otherwise>
                AND parent_id = #{parentId}
            </otherwise>
        </choose>
        LIMIT 1
    </select>

    <!-- 查询某个分类下是否存在子分类 -->
    <select id="countByParentId" resultType="int">
        SELECT COUNT(*)
        FROM ai_document_category
        WHERE del_flag = '0'
        AND parent_id = #{parentId}
    </select>

    <!-- 批量插入分类 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ai_document_category (
            category_id, category_name, category_code, parent_id, level, 
            sort_order, description, icon, status, is_system, 
            create_by, create_time, del_flag
        ) VALUES
        <foreach collection="categories" item="item" separator=",">
            (
                #{item.categoryId}, #{item.categoryName}, #{item.categoryCode}, 
                #{item.parentId}, #{item.level}, #{item.sortOrder}, 
                #{item.description}, #{item.icon}, #{item.status}, 
                #{item.isSystem}, #{item.createBy}, #{item.createTime}, 
                #{item.delFlag}
            )
        </foreach>
    </insert>

</mapper>
