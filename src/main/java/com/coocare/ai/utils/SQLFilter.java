package com.coocare.ai.utils;

import com.coocare.ai.config.exception.RRException;
import org.apache.commons.lang3.StringUtils;

/**
 * SQL 注入过滤工具
 * 对输入字符串进行基础的危险字符与关键字清理，降低 SQL 注入风险
 * <p>
 * 注意：该实现为通用弱过滤，复杂场景应结合参数化查询与白名单策略
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public class SQLFilter {

    /**
     * SQL注入过滤
     *
     * @param str 待验证的字符串
     */
    public static String sqlInject(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        //去掉'|"|;|\字符
        str = StringUtils.replace(str, "'", "");
        str = StringUtils.replace(str, "\"", "");
        str = StringUtils.replace(str, ";", "");
        str = StringUtils.replace(str, "\\", "");

        //转换成小写
        str = str.toLowerCase();

        //非法字符
        String[] keywords = {"master", "truncate", "insert", "select", "delete", "update", "declare", "alter", "drop"};

        //判断是否包含非法字符
        for (String keyword : keywords) {
            if (str.contains(keyword)) {
                throw new RRException("system.request.illegality");
            }
        }

        return str;
    }
}
