-- 部门管理系统测试脚本
-- 用于验证多级部门管理功能

-- 1. 查看部门表结构
DESCRIBE sys_dept;

-- 2. 查看用户表结构
DESCRIBE sys_user;

-- 3. 查看AI智能体表结构
DESCRIBE ai_agent;

-- 4. 查看部门智能体关联表结构
DESCRIBE ai_dept_agent;

-- 5. 测试数据查询

-- 查看所有部门及其层级关系
SELECT 
    d1.dept_id,
    d1.name AS dept_name,
    d1.dept_code,
    d1.parent_id,
    d2.name AS parent_name,
    d1.manager_user_id,
    u.name AS manager_name,
    d1.status
FROM sys_dept d1
LEFT JOIN sys_dept d2 ON d1.parent_id = d2.dept_id
LEFT JOIN sys_user u ON d1.manager_user_id = u.user_id
WHERE d1.del_flag = '0'
ORDER BY d1.parent_id, d1.sort_order;

-- 查看部门用户分布
SELECT 
    d.name AS dept_name,
    COUNT(u.user_id) AS user_count,
    SUM(CASE WHEN u.is_dept_manager = 1 THEN 1 ELSE 0 END) AS manager_count
FROM sys_dept d
LEFT JOIN sys_user u ON d.dept_id = u.dept_id AND u.del_flag = '0'
WHERE d.del_flag = '0'
GROUP BY d.dept_id, d.name
ORDER BY d.sort_order;

-- 查看部门AI智能体关联情况
SELECT 
    d.name AS dept_name,
    a.agent_name,
    da.is_default,
    da.sort_order
FROM sys_dept d
INNER JOIN ai_dept_agent da ON d.dept_id = da.dept_id
INNER JOIN ai_agent a ON da.agent_id = a.agent_id
WHERE d.del_flag = '0' 
  AND da.del_flag = '0' 
  AND a.del_flag = '0'
ORDER BY d.name, da.sort_order;

-- 查看部门主管信息
SELECT 
    d.name AS dept_name,
    u.username,
    u.name AS user_name,
    u.email,
    u.mobile
FROM sys_dept d
INNER JOIN sys_user u ON d.manager_user_id = u.user_id
WHERE d.del_flag = '0' 
  AND u.del_flag = '0'
  AND u.is_dept_manager = 1
ORDER BY d.name;

-- 6. 测试部门树形结构查询（递归查询示例）
WITH RECURSIVE dept_tree AS (
    -- 根部门
    SELECT 
        dept_id,
        name,
        dept_code,
        parent_id,
        0 as level,
        CAST(name AS CHAR(1000)) as path
    FROM sys_dept 
    WHERE parent_id = 0 AND del_flag = '0'
    
    UNION ALL
    
    -- 子部门
    SELECT 
        d.dept_id,
        d.name,
        d.dept_code,
        d.parent_id,
        dt.level + 1,
        CONCAT(dt.path, ' -> ', d.name)
    FROM sys_dept d
    INNER JOIN dept_tree dt ON d.parent_id = dt.dept_id
    WHERE d.del_flag = '0'
)
SELECT 
    dept_id,
    CONCAT(REPEAT('  ', level), name) AS dept_hierarchy,
    dept_code,
    level,
    path
FROM dept_tree
ORDER BY path;

-- 7. 验证数据完整性

-- 检查是否有孤儿部门（父部门不存在）
SELECT d1.dept_id, d1.name, d1.parent_id
FROM sys_dept d1
LEFT JOIN sys_dept d2 ON d1.parent_id = d2.dept_id
WHERE d1.parent_id > 0 
  AND d2.dept_id IS NULL 
  AND d1.del_flag = '0';

-- 检查是否有无效的部门主管（用户不存在或不属于该部门）
SELECT d.dept_id, d.name AS dept_name, d.manager_user_id
FROM sys_dept d
LEFT JOIN sys_user u ON d.manager_user_id = u.user_id
WHERE d.manager_user_id IS NOT NULL
  AND (u.user_id IS NULL OR u.dept_id != d.dept_id OR u.del_flag = '1')
  AND d.del_flag = '0';

-- 检查是否有无效的部门智能体关联
SELECT da.dept_agent_id, da.dept_id, da.agent_id
FROM ai_dept_agent da
LEFT JOIN sys_dept d ON da.dept_id = d.dept_id
LEFT JOIN ai_agent a ON da.agent_id = a.agent_id
WHERE (d.dept_id IS NULL OR d.del_flag = '1' OR a.agent_id IS NULL OR a.del_flag = '1')
  AND da.del_flag = '0';

-- 8. 性能测试查询

-- 测试部门用户统计查询性能
EXPLAIN SELECT 
    d.dept_id,
    d.name,
    COUNT(u.user_id) as user_count
FROM sys_dept d
LEFT JOIN sys_user u ON d.dept_id = u.dept_id AND u.del_flag = '0'
WHERE d.del_flag = '0'
GROUP BY d.dept_id, d.name;

-- 测试部门智能体关联查询性能
EXPLAIN SELECT 
    d.dept_id,
    d.name,
    GROUP_CONCAT(a.agent_name) as agents
FROM sys_dept d
LEFT JOIN ai_dept_agent da ON d.dept_id = da.dept_id AND da.del_flag = '0'
LEFT JOIN ai_agent a ON da.agent_id = a.agent_id AND a.del_flag = '0'
WHERE d.del_flag = '0'
GROUP BY d.dept_id, d.name;
