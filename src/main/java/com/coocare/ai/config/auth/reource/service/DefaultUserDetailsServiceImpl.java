package com.coocare.ai.config.auth.reource.service;

import com.coocare.ai.config.constants.UserDetail;
import com.coocare.ai.entity.sys.dto.UserInfo;
import com.coocare.ai.service.SysUserService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

/**
 * 默认用户详情服务
 * 根据用户名加载用户信息；并实现按 UserDetail 代理查询
 *
 * <AUTHOR>
 * @since 2022-06-02
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultUserDetailsServiceImpl implements CustomUserDetailsService {

    private final SysUserService userService;

    /**
     * 用户密码登录
     *
     * @param username 账户
     * @return
     * @throws UsernameNotFoundException
     */
    @Override
    @SneakyThrows
    public UserDetails loadUserByUsername(String username) {
        UserInfo userInfo = userService.loadUserByUsername(username);
        return getUserDetails(userInfo);
    }

    /**
     * 通过用户实体查询
     *
     * @param user user
     * @return
     */
    @Override
    public UserDetails loadUserByUser(UserDetail user) {
        return loadUserByUsername(user.getUsername());
    }

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE;
    }
}
