//package com.coocare.ai.config.auth.reource.component;
/**
 * Spring Security 消息源配置（示例/占位）
 * 若启用请注入自定义 messages 以覆盖默认国际化文案
 *
 * <AUTHOR>
 * @since 2022-06-04
 */
//
//import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
//import org.springframework.context.MessageSource;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.support.ReloadableResourceBundleMessageSource;
//import org.springframework.stereotype.Component;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import java.util.Locale;
//
//import static org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication.Type.SERVLET;
//
///**
// * <AUTHOR>
// * @date 2022-06-04
// * <p>
// * 注入自定义错误处理,覆盖 org/springframework/security/messages 内置异常
// */
//@Component
//@ConditionalOnWebApplication(type = SERVLET)
//public class SecurityMessageSourceConfiguration implements WebMvcConfigurer {
//
//	@Bean
//	public MessageSource securityMessageSource() {
//		ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
//		messageSource.setBasename("classpath:i18n/messages");
//		messageSource.setDefaultLocale(Locale.CHINA);
//		return messageSource;
//	}
//
//}
