package com.coocare.ai.entity.sys.vo;

import com.coocare.ai.entity.sys.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 部门信息VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "部门信息VO")
public class DeptVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "部门名称")
    private String name;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "父部门ID")
    private Long parentId;

    @Schema(description = "父部门名称")
    private String parentName;

    @Schema(description = "部门类型")
    private String deptType;

    @Schema(description = "部门主管用户ID")
    private Long managerUserId;

    @Schema(description = "部门主管信息")
    private SysUser managerUser;

    @Schema(description = "部门联系电话")
    private String phone;

    @Schema(description = "部门联系邮箱")
    private String email;

    @Schema(description = "部门群组邮箱")
    private String groupEmail;

    @Schema(description = "部门办公电话")
    private String officePhone;

    @Schema(description = "部门描述")
    private String description;

    @Schema(description = "部门状态")
    private Integer status;

    @Schema(description = "排序号")
    private Integer sortOrder;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    private String updateBy;

    @Schema(description = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    
    private LocalDateTime updateTime;

    @Schema(description = "子部门列表")
    private List<DeptVO> children;

    @Schema(description = "部门用户数量")
    private long userCount;

    @Schema(description = "是否有子部门")
    private Boolean hasChildren;
}
