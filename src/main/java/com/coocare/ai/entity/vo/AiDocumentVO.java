package com.coocare.ai.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * AI文档响应VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "AI文档响应VO")
public class AiDocumentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "文档ID")
    private Long documentId;

    @Schema(description = "后端对应ID")
    private String backendId;

    @Schema(description = "文档名称")
    private String documentName;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "文件访问URL")
    private String fileUrl;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "文件大小（格式化显示）")
    private String fileSizeFormatted;

    @Schema(description = "文件类型/扩展名")
    private String fileType;

    @Schema(description = "MIME类型")
    private String mimeType;

    @Schema(description = "所属分类ID（第三层分类）")
    private Long categoryId;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "所属产品系列ID（第二层分类）")
    private Long productSeriesId;

    @Schema(description = "产品系列名称")
    private String productSeriesName;

    @Schema(description = "所属产品型号ID（第一层分类）")
    private Long productModelId;

    @Schema(description = "产品型号名称")
    private String productModelName;

    @Schema(description = "分类编码")
    private String categoryCode;

    @Schema(description = "文档描述")
    private String description;

    @Schema(description = "文档标签")
    private String tags;

    @Schema(description = "文档版本")
    private String version;

    @Schema(description = "下载次数")
    private Integer downloadCount;

    @Schema(description = "查看次数")
    private Integer viewCount;

    @Schema(description = "状态：0-禁用，1-启用，2-审核中")
    private String status;

    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "索引状态")
    private String indexingStatus;

    @Schema(description = "索引状态")
    private String indexingStatusName;

    @Schema(description = "排序号")
    private Integer sortOrder;

    @Schema(description = "上传人")
    private String uploadBy;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

}
