package com.coocare.ai.config.auth.support.core;

import com.coocare.ai.config.constants.SecurityConstants;
import com.coocare.ai.config.constants.UserDetail;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenClaimsContext;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenClaimsSet;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;

/**
 * Token 自定义增强器
 * 在 AccessToken claims 中加入客户端与用户标识
 *
 * <AUTHOR>
 * @since 2022-06-03
 */

public class CustomeOAuth2TokenCustomizer implements OAuth2TokenCustomizer<OAuth2TokenClaimsContext> {

    /**
     * Customize the OAuth 2.0 Token attributes.
     *
     * @param context the context containing the OAuth 2.0 Token attributes
     */
    @Override
    public void customize(OAuth2TokenClaimsContext context) {
        OAuth2TokenClaimsSet.Builder claims = context.getClaims();
        claims.claim(SecurityConstants.DETAILS_LICENSE, SecurityConstants.COOCARE_LICENSE);
        String clientId = context.getAuthorizationGrant().getName();
        claims.claim(SecurityConstants.CLIENT_ID, clientId);
        claims.claim(SecurityConstants.ACTIVE, Boolean.TRUE);

        // 客户端模式不返回具体用户信息
        if (SecurityConstants.CLIENT_CREDENTIALS.equals(context.getAuthorizationGrantType().getValue())) {
            return;
        }

        UserDetail user = (UserDetail) context.getPrincipal().getPrincipal();
        claims.claim(SecurityConstants.DETAILS_USER_ID, user.getId());
        claims.claim(SecurityConstants.DETAILS_USERNAME, user.getUsername());
    }

}
