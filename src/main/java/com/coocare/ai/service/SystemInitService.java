package com.coocare.ai.service;

import com.coocare.ai.entity.sys.dto.SystemInitDTO;

/**
 * <p>
 * 系统初始化服务接口
 * 用于系统首次启动时的初始化配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface SystemInitService {

    /**
     * 检查系统是否已经初始化
     * @return true-已初始化，false-未初始化
     */
    boolean isSystemInitialized();

    /**
     * 执行系统初始化
     * @param initDTO 初始化配置信息
     * @return 初始化结果
     */
    boolean initializeSystem(SystemInitDTO initDTO);

    /**
     * 创建管理员账号
     * @param initDTO 初始化配置信息
     * @return 管理员用户ID
     */
    Long createAdminUser(SystemInitDTO initDTO);

    /**
     * 初始化企业信息
     * @param initDTO 初始化配置信息
     */
    void initCompanyInfo(SystemInitDTO initDTO);

    /**
     * 初始化行业信息
     * @param initDTO 初始化配置信息
     */
    void initIndustryInfo(SystemInitDTO initDTO);

    /**
     * 初始化智能体配置
     * @param initDTO 初始化配置信息
     */
    void initAgentConfig(SystemInitDTO initDTO);

    /**
     * 初始化智能体服务配置
     * @param initDTO 初始化配置信息
     */
    void initAgentServiceConfig(SystemInitDTO initDTO);

    /**
     * 初始化系统配置
     * @param initDTO 初始化配置信息
     */
    void initSystemConfig(SystemInitDTO initDTO);

    /**
     * 初始化邮箱配置
     * @param initDTO 初始化配置信息
     */
    void initMailConfig(SystemInitDTO initDTO);

    /**
     * 创建默认角色和权限
     */
    void createDefaultRolesAndPermissions();

    /**
     * 创建默认部门
     * @param companyName 企业名称
     * @return 部门ID
     */
    Long createDefaultDepartment(String companyName);


}
