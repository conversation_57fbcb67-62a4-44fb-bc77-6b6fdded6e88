package com.coocare.ai.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.config.constants.CacheConstants;
import com.coocare.ai.entity.sys.SysPublicParam;
import com.coocare.ai.mapper.SysPublicParamMapper;
import com.coocare.ai.service.SysPublicParamService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 公共参数配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Service
public class SysPublicParamServiceImpl extends ServiceImpl<SysPublicParamMapper, SysPublicParam> implements SysPublicParamService {

    @Override
    @Cacheable(value = CacheConstants.PARAMS_DETAILS, key = "#publicKey", unless = "#result == null ")
    public String getSysPublicParamKeyToValue(String publicKey) {
        SysPublicParam sysPublicParam = this.baseMapper
                .selectOne(Wrappers.<SysPublicParam>lambdaQuery().eq(SysPublicParam::getPublicKey, publicKey));

        if (sysPublicParam != null) {
            return sysPublicParam.getPublicValue();
        }
        return null;
    }

    /**
     * 更新参数
     *
     * @param sysPublicParam
     * @return
     */
    @Override
    @CacheEvict(value = CacheConstants.PARAMS_DETAILS, key = "#sysPublicParam.publicKey")
    public boolean updateParam(SysPublicParam sysPublicParam) {
        SysPublicParam param = this.getById(sysPublicParam.getPublicId());
        return this.updateById(sysPublicParam);
    }

    /**
     * 删除参数
     *
     * @param publicId
     * @return
     */
    @Override
    @CacheEvict(value = CacheConstants.PARAMS_DETAILS, allEntries = true)
    public boolean removeParam(Long publicId) {
        SysPublicParam param = this.getById(publicId);
        return removeById(publicId);
    }

    /**
     * 同步缓存
     *
     * @return R
     */
    @Override
    @CacheEvict(value = CacheConstants.PARAMS_DETAILS, allEntries = true)
    public boolean syncParamCache() {
        return Boolean.TRUE;
    }
}
