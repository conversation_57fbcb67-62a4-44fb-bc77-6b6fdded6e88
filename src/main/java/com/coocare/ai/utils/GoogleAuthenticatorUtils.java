package com.coocare.ai.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.apache.commons.codec.binary.Base32;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;

/**
 * Google 身份验证器工具
 * 实现基于 TOTP（时间同步一次性口令）的验证码生成与校验逻辑
 *
 * 注意：包含默认窗口偏移配置与二维码 URL 生成
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public class GoogleAuthenticatorUtils {

    public static final int SECRET_SIZE = 10;
    public static final String SEED = "g8GjEvTbW5oVSV7avLBdwIHqGlUYNzKFI7izOF8GwLDVKs2m0QN7vxRs2im5MDaNCWGmcD2rvcZx";
    public static final String RANDOM_NUMBER_ALGORITHM = "SHA1PRNG";
    
    /**
     * default 3 - max 17 (from google docs)最多可偏移的时间
     */
    private int window_size = 3;

    /**
     * 验证身份验证码是否正确
     *
     * @param codes       输入的身份验证码
     * @param savedSecret 密钥
     * @return 验证结果
     */
    public static Boolean authCode(String codes, String savedSecret) {
        long code = 0;
        try {
            code = Long.parseLong(codes);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        long t = System.currentTimeMillis();
        GoogleAuthenticatorUtils ga = new GoogleAuthenticatorUtils();
        ga.setWindowSize(ga.window_size);
        return ga.checkCode(savedSecret, code, t);
    }

    /**
     * 生成密钥和二维码URL
     *
     * @param user 用户
     * @param host 域
     * @return 包含密钥和二维码URL的结果
     */
    public static TOTPResult genSecret(String user, String host) {
        String secret = generateSecretKey();
        String qrCodeUrl = getQRBarcodeURL(user, host, secret);
        return new TOTPResult(secret, qrCodeUrl);
    }

    /**
     * 生成密钥
     *
     * @return 生成的密钥
     */
    public static String generateSecretKey() {
        SecureRandom sr = null;
        try {
            sr = SecureRandom.getInstance(RANDOM_NUMBER_ALGORITHM);
            sr.setSeed(Base64.decodeBase64(SEED));
            byte[] buffer = sr.generateSeed(SECRET_SIZE);
            Base32 codec = new Base32();
            byte[] bEncodedKey = codec.encode(buffer);
            return new String(bEncodedKey);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Failed to generate secret key", e);
        }
    }

    /**
     * 获取二维码内容URL
     *
     * @param user   用户
     * @param host   域
     * @param secret 密钥
     * @return 二维码URL
     */
    public static String getQRBarcodeURL(String user, String host, String secret) {
        String format = "otpauth://totp/%s@%s?secret=%s&issuer=%s";
        return String.format(format, user, host, secret, host);
    }

    /**
     * 生成二维码图片
     *
     * @param text   二维码内容
     * @param width  图片宽度
     * @param height 图片高度
     * @return 二维码图片的字节数组
     * @throws WriterException 写入异常
     * @throws IOException     IO异常
     */
    public static byte[] generateQRCodeImage(String text, int width, int height) 
            throws WriterException, IOException {
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, 1);
        
        BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height, hints);
        
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        image.createGraphics();
        
        Graphics2D graphics = (Graphics2D) image.getGraphics();
        graphics.setColor(Color.WHITE);
        graphics.fillRect(0, 0, width, height);
        graphics.setColor(Color.BLACK);
        
        for (int i = 0; i < width; i++) {
            for (int j = 0; j < height; j++) {
                if (bitMatrix.get(i, j)) {
                    graphics.fillRect(i, j, 1, 1);
                }
            }
        }
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        return baos.toByteArray();
    }

    /**
     * 根据当前时间生成TOTP验证码
     *
     * @param secret 密钥
     * @return 6位验证码
     */
    public static String generateCurrentTOTP(String secret) {
        long timeStamp = System.currentTimeMillis() / 1000L / 30L;
        return generateTOTP(secret, timeStamp);
    }

    /**
     * 根据指定时间戳生成TOTP验证码
     *
     * @param secret    密钥
     * @param timeStamp 时间戳
     * @return 6位验证码
     */
    public static String generateTOTP(String secret, long timeStamp) {
        try {
            Base32 codec = new Base32();
            byte[] decodedKey = codec.decode(secret);
            int code = verifyCode(decodedKey, timeStamp);
            return String.format("%06d", code);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate TOTP", e);
        }
    }

    /**
     * 时间校验密钥与code是否匹配
     *
     * @param key 解密后的密钥
     * @param t   时间
     * @return 验证码
     * @throws NoSuchAlgorithmException 算法异常
     * @throws InvalidKeyException      密钥异常
     */
    private static int verifyCode(byte[] key, long t)
            throws NoSuchAlgorithmException, InvalidKeyException {
        byte[] data = new byte[8];
        long value = t;
        for (int i = 8; i-- > 0; value >>>= 8) {
            data[i] = (byte) value;
        }
        SecretKeySpec signKey = new SecretKeySpec(key, "HmacSHA1");
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(signKey);
        byte[] hash = mac.doFinal(data);
        int offset = hash[20 - 1] & 0xF;
        long truncatedHash = 0;
        for (int i = 0; i < 4; ++i) {
            truncatedHash <<= 8;
            truncatedHash |= (hash[offset + i] & 0xFF);
        }
        truncatedHash &= 0x7FFFFFFF;
        truncatedHash %= 1000000;
        return (int) truncatedHash;
    }

    public void setWindowSize(int s) {
        if (s >= 1 && s <= 17) {
            window_size = s;
        }
    }

    /**
     * 校验code是否正确
     *
     * @param secret   密钥
     * @param code     动态code
     * @param timeMsec 时间
     * @return 校验结果
     */
    private boolean checkCode(String secret, long code, long timeMsec) {
        Base32 codec = new Base32();
        byte[] decodedKey = codec.decode(secret);
        long t = (timeMsec / 1000L) / 30L;
        for (int i = -window_size; i <= window_size; ++i) {
            long hash;
            try {
                hash = verifyCode(decodedKey, t + i);
            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException(e.getMessage());
            }
            if (hash == code) {
                return true;
            }
        }
        return false;
    }

    /**
     * TOTP结果类
     */
    public static class TOTPResult {
        private final String secret;
        private final String qrCodeUrl;

        public TOTPResult(String secret, String qrCodeUrl) {
            this.secret = secret;
            this.qrCodeUrl = qrCodeUrl;
        }

        public String getSecret() {
            return secret;
        }

        public String getQrCodeUrl() {
            return qrCodeUrl;
        }

        @Override
        public String toString() {
            return "TOTPResult{" +
                    "secret='" + secret + '\'' +
                    ", qrCodeUrl='" + qrCodeUrl + '\'' +
                    '}';
        }
    }
}
