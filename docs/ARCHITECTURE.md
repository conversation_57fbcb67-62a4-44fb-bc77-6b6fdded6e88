# 战AI-企业版 架构设计

## 背景与目标
- 面向企业提供 AI 能力（智能体、知识库/文档、账号与权限）的一站式后端服务
- 强调可维护、可扩展与工程化实践

## 技术栈
- Spring Boot 3.4, Spring Security OAuth2 Authorization Server
- MyBatis-Plus, MyBatis-Plus-Join, MySQL, Redis
- OpenAPI (springdoc), Knife4j（可选）
- 对象存储：S3 兼容（MinIO/OSS/COS/七牛等）
- Dify 集成（Agent、Dataset）

## 分层与包结构
- config：全局配置（安全、缓存、国际化、OpenAPI、OSS 等）
- controller：REST 接口层
- service：领域服务层（接口与实现）
- mapper：数据访问层
- entity：实体/DTO/VO
- utils：通用工具

## 关键流程
- 鉴权：OAuth2 授权服务器签发 Token，资源服务器校验；Redis 存储 Token/Consent
- 系统初始化：SystemInitController -> SystemInitServiceImpl -> 写入配置 -> 触发刷新
- 文档上传：Controller -> Service(校验/OSS 上传/DB 写入/事件) -> 统计与索引状态
- 智能体/Dify：服务初始化阶段拉取应用/数据集，保存映射，按需生成 API Key

## 重要约束
- 逻辑删除统一使用 del_flag
- 审计字段统一使用 create_by/create_time/update_by/update_time
- 分页统一 PageDomain + PageUtils
- 开放接口使用 OpenAPI 注解并生成 operationId

## 后续可演进
- 观察性（审计日志、Tracing）
- 幂等与重试的统一抽象
- 统一异常码与错误响应规范


## 架构图

> 上下文图（Context Diagram）

```mermaid
flowchart LR
  subgraph Client
    FE[Web/Client]
  end
  subgraph Server[Zhan AI Backend]
    API[REST Controllers]
    SRV[Domain Services]
    MP[MyBatis-Plus]
    EVT[Spring Events]
  end
  subgraph Infra[Infrastructure]
    DB[(MySQL)]
    REDIS[(Redis)]
    OSS[(S3-Compatible OSS)]
    DIFY[(Dify API)]
    AUTH[OAuth2 Auth Server]
  end

  FE -->|HTTP/JSON| API
  API -->|AuthZ| AUTH
  API --> SRV
  SRV --> MP
  MP --> DB
  SRV -->|Cache| REDIS
  SRV -->|Upload/Presign| OSS
  SRV -->|Agents/Datasets| DIFY
  SRV -->|Domain Events| EVT
```

> 组件图（Component Diagram）

```mermaid
flowchart TB
  subgraph Controllers
    C1[SystemInitController]
    C2[SysUserController]
    C3[AiDocumentController]
    C4[AiAgentController]
  end
  subgraph Services
    S1[SystemInitService]
    S2[SysUserService]
    S3[AiDocumentService]
    S4[AiAgentService]
  end
  subgraph DataAccess
    M1[(SysUserMapper)]
    M2[(AiDocumentMapper)]
    M3[(SysConfigMapper)]
  end
  subgraph Config
    CONF1[AuthorizationServerConfiguration]
    CONF2[ResourceServerConfiguration]
    CONF3[OssTemplate]
    CONF4[GlobalConfigManager]
  end
  subgraph Stores
    DB[(MySQL)]
    REDIS[(Redis)]
    OSS[(S3/OSS)]
  end

  C1 --> S1
  C2 --> S2
  C3 --> S3
  C4 --> S4

  S2 --> M1 --> DB
  S3 --> M2 --> DB
  S1 --> M3 --> DB

  S3 -->|upload/presign| CONF3 --> OSS
  S1 -->|load configs| CONF4 --> REDIS

  CONF1 -.-> Controllers
  CONF2 -.-> Controllers
```

> 部署图（Deployment Diagram）

```mermaid
graph TB
  subgraph Runtime
    APP[Spring Boot App]
  end
  subgraph Container
    IMG[Docker Image]
  end
  subgraph Infra[Environment]
    MYSQL[(MySQL)]
    REDIS[(Redis)]
    MINIO[(MinIO/OSS)]
    DIFY[(Dify API)]
  end

  APP --> MYSQL
  APP --> REDIS
  APP --> MINIO
  APP --> DIFY
  APP -. exposed .->|8070| IMG
```

> 时序图：文档上传流程

```mermaid
sequenceDiagram
  actor User
  participant UI as Frontend
  participant API as AiDocumentController
  participant SVC as AiDocumentService
  participant OSS as OssTemplate
  participant DB as MySQL
  participant EVT as Spring EventBus

  User->>UI: 选择文件与分类
  UI->>API: POST /ai/document/upload
  API->>SVC: uploadDocument(file, dto)
  SVC->>SVC: validateFile(), 校验分类/重名
  SVC->>OSS: putObject(bucket, objectName, stream)
  OSS-->>SVC: 200 + URL
  SVC->>DB: INSERT ai_document
  SVC->>EVT: publish DocumentUploadEvent
  EVT-->>SVC: 异步处理（索引/通知）
  SVC-->>API: AiDocumentVO
  API-->>UI: 200 + 文档信息
```

## 专业设计说明

### 非功能性要求（NFR）
- 可用性：服务应支持零停机配置刷新与可灰度/滚动发布
- 性能：分页查询 P95 < 200ms（业务库 10w 级数据）；文件上传瓶颈为对象存储带宽
- 可靠性：核心写路径（上传、初始化）事务性保障，事件处理具备幂等
- 可扩展性：服务无状态、对象存储横向扩展、DB/Redis 可独立扩容
- 可观测性：标准化日志、必要的审计日志、接口级统计；建议接入指标与分布式链路追踪

### 安全设计
- OAuth2 授权 + 资源服务器拦截；严格区分外部接口与 Inner 接口
- CORS 在生产按白名单收敛；关闭 Swagger 或启用鉴权
- 机密配置通过环境变量或密钥管理（不落代码库），支持按环境覆盖

### 错误处理与一致性
- 控制器统一使用 AjaxResult 包装；明确错误码与信息
- Service 层边界明确：参数校验、业务校验、事务边界；跨资源写入使用本地事务 + 事件补偿

### 幂等与重试
- 文档上传/删除/状态变更事件使用事件载荷标识（如 documentId + 操作类型 + 时间戳），消费方幂等
- 批量操作确保去重与部分失败可重试

### 配置与密钥管理
- application.yml 仅保留非机密默认值；敏感项（DB/Redis/邮箱/Dify）来自环境变量
- 全局配置缓存 GlobalConfigManager 支持按键刷新与全量加载

### 数据治理与合规
- 用户与操作审计（创建/更新/逻辑删除）字段统一；日志避免记录敏感信息
- 对象存储的访问控制遵循最小权限；下载接口需要鉴权

### 扩展与演进
- 引入统一错误码规范与异常映射
- 引入统一的审计日志与操作追踪
- 指标/Tracing 接入 Prometheus + OpenTelemetry


## 更多时序图

> 时序图：鉴权流程（登录与访问受保护接口）

```mermaid
sequenceDiagram
  actor User
  participant UI as Frontend
  participant AUTHC as OAuth2Controller
  participant AS as Authorization Server
  participant RS as Resource Server
  participant API as Protected Controller
  participant REDIS as Redis

  User->>UI: 输入凭证
  UI->>AUTHC: POST /oauth/token (用户名/验证码/短信)
  AUTHC->>AS: 认证请求（密码/验证码校验）
  AS->>REDIS: 写入 Token/Consent
  AS-->>AUTHC: 200 {access_token, refresh_token}
  AUTHC-->>UI: Token 返回

  UI->>RS: GET /protected/resource Authorization: Bearer xxx
  RS->>REDIS: 校验 Token（Opaque/Introspection）
  RS->>API: 通过后转发请求
  API-->>UI: 200 数据
```

> 时序图：系统初始化流程

```mermaid
sequenceDiagram
  actor Admin
  participant UI as Frontend
  participant C as SystemInitController
  participant S as SystemInitService
  participant DB as MySQL
  participant R as Redis
  participant EVT as Spring EventBus

  Admin->>UI: 进入初始化页面
  UI->>C: GET /system/init/options
  C-->>UI: 初始化可选项（行业、地域等）

  Admin->>UI: 提交初始化参数
  UI->>C: POST /system/init
  C->>S: init(params)
  S->>DB: 建立组织/角色/菜单/管理员
  S->>DB: 写入系统配置
  S->>R: 刷新/预热必要缓存
  S->>EVT: 发布 ConfigUpdatedEvent
  EVT-->>S: 异步消费（如刷新本地配置）
  S-->>C: 初始化完成标识
  C-->>UI: 200 成功
```


> 时序图：权限计算（用户菜单/角色权限）

```mermaid
sequenceDiagram
  actor User
  participant UI as Frontend
  participant C as SysMenuController
  participant S as SysMenuService
  participant RM as SysRoleMenuMapper
  participant UR as SysUserRoleMapper
  participant M as SysMenuMapper

  User->>UI: 打开导航
  UI->>C: GET /system/menu/user
  C->>S: getUserMenus(userId)
  S->>UR: selectRolesByUserId(userId)
  UR-->>S: [roleIds]
  S->>RM: selectMenuIdsByRoleIds(roleIds)
  RM-->>S: [menuIds]
  S->>M: selectMenusByIds(menuIds)
  M-->>S: 菜单列表
  S-->>C: 构建菜单树/权限标识
  C-->>UI: 前端可渲染的菜单树与权限集
```

> 时序图：Dify 数据集同步

```mermaid
sequenceDiagram
  participant JOB as Startup Runner
  participant DS as DifyDatasetBuilder
  participant API as Dify API
  participant DB as sys_config/sys_dify_mapping

  JOB->>DS: initDatasetApiKey()
  DS->>API: 获取/创建 dataset api key
  API-->>DS: apiKey
  DS->>DB: 保存/更新 dataset api key 映射
  DS-->>JOB: 完成
```
