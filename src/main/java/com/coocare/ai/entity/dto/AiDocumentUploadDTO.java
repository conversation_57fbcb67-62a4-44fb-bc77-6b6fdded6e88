package com.coocare.ai.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * AI文档上传请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "AI文档上传请求DTO")
public class AiDocumentUploadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "文档名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文档名称不能为空")
    @Size(max = 200, message = "文档名称长度不能超过200个字符")
    private String documentName;

    @Schema(description = "数据集ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据集ID不能为空")
    private Long dateSetId;

    @Schema(description = "所属分类ID（第三层分类）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所属分类ID不能为空")
    private Long categoryId;

    @Schema(description = "文档描述")
    @Size(max = 1000, message = "文档描述长度不能超过1000个字符")
    private String description;

    @Schema(description = "文档标签（多个标签用逗号分隔）")
    @Size(max = 500, message = "文档标签长度不能超过500个字符")
    private String tags;

    @Schema(description = "文档版本")
    @Size(max = 20, message = "文档版本长度不能超过20个字符")
    private String version;

    @Schema(description = "文件的路径")
    private String filePath;

    @Schema(description = "排序号")
    private Integer sortOrder;
}
