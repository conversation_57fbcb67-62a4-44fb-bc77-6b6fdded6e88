-- AI对话统计表建表语句
-- 用于记录每次对话的统计信息，包括对话ID、token使用量、用户信息等
-- 创建时间: 2025-09-03

DROP TABLE IF EXISTS ai_chat_statistics;
CREATE TABLE ai_chat_statistics (
    statistics_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计记录ID',
    conversation_id VARCHAR(255) COMMENT '对话ID',
    message_id VARCHAR(255) COMMENT '消息ID',
    agent_id BIGINT COMMENT '智能体ID',
    agent_name VARCHAR(255) COMMENT '智能体名称',
    input_tokens INT COMMENT '输入token数量',
    output_tokens INT COMMENT '输出token数量',
    total_tokens INT COMMENT '总token数量',
    user_id BIGINT COMMENT '使用人ID',
    username VARCHAR(255) COMMENT '使用人用户名',
    conversation_start_time DATETIME COMMENT '对话开始时间',
    conversation_end_time DATETIME COMMENT '对话结束时间',
    response_time BIGINT COMMENT '响应时间（毫秒）',
    user_input TEXT COMMENT '用户输入内容',
    ai_response TEXT COMMENT 'AI回复内容',
    event_type VARCHAR(100) COMMENT '事件类型',
    status VARCHAR(50) COMMENT '状态：SUCCESS-成功，ERROR-错误',
    error_message TEXT COMMENT '错误信息',
    client_ip VARCHAR(100) COMMENT '客户端IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理信息',
    create_by VARCHAR(255) COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 创建索引
CREATE INDEX idx_conversation_id ON ai_chat_statistics(conversation_id);
CREATE INDEX idx_agent_id ON ai_chat_statistics(agent_id);
CREATE INDEX idx_user_id ON ai_chat_statistics(user_id);
CREATE INDEX idx_create_time ON ai_chat_statistics(create_time);
CREATE INDEX idx_event_type ON ai_chat_statistics(event_type);
CREATE INDEX idx_status ON ai_chat_statistics(status);

-- 添加表注释
ALTER TABLE ai_chat_statistics COMMENT = 'AI对话统计表，记录每次对话的详细统计信息';
