package com.coocare.ai.config.oss;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * OSS/S3 配置属性
 * 映射 application.yml 的 oss.* 配置，用于初始化 S3 客户端
 * 示例与说明见 application.yml 与部署文档
 *
 * <AUTHOR>
 * @since 2020-05-23
 */
@Data
@ConfigurationProperties(prefix = "oss")
public class OssProperties {

    private Boolean enable = false;

    /**
     * 对象存储服务的URL
     */
    private String endpoint;

    /**
     * 自定义域名
     */
    private String customDomain;

    /**
     * true path-style nginx 反向代理和S3默认支持 pathStyle {http://endpoint/bucketname} false
     * supports virtual-hosted-style 阿里云等需要配置为 virtual-hosted-style
     * 模式{http://bucketname.endpoint}
     */
    private Boolean pathStyleAccess = true;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 区域
     */
    private String region;

    private String bucketName;

    /**
     * Access key就像用户ID，可以唯一标识你的账户
     */
    private String accessKey;

    /**
     * Secret key是你账户的密码
     */
    private String secretKey;

    /**
     * 最大线程数，默认： 100
     */
    private Integer maxConnections = 100;

    /**
     * 授权链接过期时间 (秒)
     */
    private Integer exp = 300;

}
