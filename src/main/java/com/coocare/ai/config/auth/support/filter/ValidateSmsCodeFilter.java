//package com.coocare.ai.config.auth.support.filter;
/**
 * 短信验证码校验过滤器（示例/占位）
 * 若启用请取消注释并完善实现
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
//
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.alibaba.fastjson2.JSON;
//import com.baomidou.mybatisplus.core.toolkit.StringPool;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.coocare.ai.config.constants.SecurityConstants;
//import com.coocare.ai.config.domain.AjaxResult;
//import com.coocare.ai.config.exception.RRException;
//import com.coocare.ai.entity.sys.SysOauthClientDetails;
//import com.coocare.ai.service.SysOauthClientDetailsService;
//import com.coocare.ai.utils.WebUtils;
//import jakarta.servlet.FilterChain;
//import jakarta.servlet.ServletException;
//import jakarta.servlet.ServletResponse;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import lombok.RequiredArgsConstructor;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.HttpHeaders;
//import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
//import org.springframework.stereotype.Component;
//import org.springframework.web.filter.OncePerRequestFilter;
//
//import java.io.IOException;
//import java.io.PrintWriter;
//
///**
// * <AUTHOR>
// */
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public class ValidateSmsCodeFilter extends OncePerRequestFilter {
//
//    private final SysOauthClientDetailsService sysOauthClientDetailsService;
//
//
//    @Override
//    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
//        if (!StrUtil.containsAnyIgnoreCase(request.getRequestURI(), SecurityConstants.OAUTH_TOKEN_URL)) {
//            chain.doFilter(request, response);
//            return;
//        }
//        // 判断客户端是否跳过检验
//        if (!isCheckCaptchaClient(request)) {
//            chain.doFilter(request, response);
//            return;
//        }
//        try {
//            checkCode(request);
//        } catch (Exception e) {
//            AjaxResult<String> r = new AjaxResult<>();
//            r.setCode(-1);
//            r.setMsg(e.getMessage());
//            returnJson(response, JSON.toJSONString(r));
//            return;
//        }
//        chain.doFilter(request, response);
//    }
//
//    private boolean isCheckCaptchaClient(HttpServletRequest request) {
//        String header = request.getHeader(HttpHeaders.AUTHORIZATION);
//        String clientId = WebUtils.extractClientId(header).orElse(null);
//        log.info("header:{},当前客户端:{}", header, clientId);
//        SysOauthClientDetails clientDetails = sysOauthClientDetailsService.getOne(Wrappers.<SysOauthClientDetails>lambdaQuery().eq(SysOauthClientDetails::getClientId, clientId));
//        String val = clientDetails.getAdditionalInformation();
//        if (val == null) {
//            return false;
//        }
//        JSONObject information = JSONUtil.parseObj(val);
//        return !StrUtil.equals(SmsFlagTypeEnum.OFF.getType(), information.getStr(CommonConstants.SMS_FLAG));
//    }
//
//
//    /**
//     * 检查code
//     *
//     * @param request
//     */
//    @SneakyThrows
//    private void checkCode(HttpServletRequest request) {
//        String code = request.getParameter("code");
//        if (StrUtil.isBlank(code)) {
//            throw new RRException("验证码不能为空");
//        }
//        String mobile = request.getParameter(SecurityConstants.SMS_PARAMETER_NAME);
//        mobile = mobile.replace(LoginTypeEnum.SMS.getType() + StringPool.AT, "");
//        smsService.checkCode(mobile, code, SmsCodeEnum.LOGIN_IN, true);
//    }
//
//    private void returnJson(ServletResponse response, String json) {
//        PrintWriter writer = null;
//        response.setCharacterEncoding("UTF-8");
//        response.setContentType("application/json; charset=utf-8");
//        try {
//            writer = response.getWriter();
//            writer.print(json);
//
//        } catch (IOException e) {
//            log.error("response error", e);
//        } finally {
//            if (writer != null) {
//                writer.close();
//            }
//        }
//    }
//}
