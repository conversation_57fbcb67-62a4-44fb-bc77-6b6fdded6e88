<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AiDocumentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AiDocument">
        <id column="document_id" property="documentId"/>
        <result column="backend_id" property="backendId"/>
        <result column="dataset_id" property="datasetId"/>
        <result column="document_name" property="documentName"/>
        <result column="original_name" property="originalName"/>
        <result column="file_path" property="filePath"/>
        <result column="file_url" property="fileUrl"/>
        <result column="file_size" property="fileSize"/>
        <result column="file_type" property="fileType"/>
        <result column="mime_type" property="mimeType"/>
        <result column="category_id" property="categoryId"/>
        <result column="product_series_id" property="productSeriesId"/>
        <result column="product_model_id" property="productModelId"/>
        <result column="category_code" property="categoryCode"/>
        <result column="description" property="description"/>
        <result column="tags" property="tags"/>
        <result column="version" property="version"/>
        <result column="download_count" property="downloadCount"/>
        <result column="view_count" property="viewCount"/>
        <result column="status" property="status"/>
        <result column="indexing_status" property="indexingStatus"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="upload_by" property="uploadBy"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 包含分类信息的查询映射结果 -->
    <resultMap id="DocumentDetailResultMap" type="com.coocare.ai.entity.AiDocument" extends="BaseResultMap">
        <result column="category_name" property="categoryName"/>
        <result column="product_series_name" property="productSeriesName"/>
        <result column="product_model_name" property="productModelName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        d.document_id, d.backend_id, d.dataset_id, d.document_name, d.original_name, d.file_path, d.file_url,
        d.file_size, d.file_type, d.mime_type, d.category_id, d.product_series_id, 
        d.product_model_id, d.category_code, d.description, d.tags, d.version, 
        d.download_count, d.view_count, d.status, d.indexing_status, d.sort_order,
        d.upload_by, d.create_by, d.create_time, d.update_by, d.update_time, d.del_flag
    </sql>

    <!-- 包含分类信息的查询结果列 -->
    <sql id="Detail_Column_List">
        <include refid="Base_Column_List"/>,
        c.category_name,
        ps.category_name as product_series_name,
        pm.category_name as product_model_name
    </sql>

    <!-- 根据产品系列ID查询所有文档（包含分类信息） -->
    <select id="selectDocumentsByProductSeriesId" resultMap="DocumentDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM ai_document d
        LEFT JOIN ai_document_category c ON d.category_id = c.category_id
        LEFT JOIN ai_document_category ps ON d.product_series_id = ps.category_id
        LEFT JOIN ai_document_category pm ON d.product_model_id = pm.category_id
        WHERE d.del_flag = '0'
        AND d.product_series_id = #{productSeriesId}
        AND d.status = 1
        ORDER BY d.category_code ASC, d.sort_order ASC, d.create_time DESC
    </select>

    <!-- 根据产品系列ID和分类编码查询文档 -->
    <select id="selectDocumentsByProductSeriesAndCategory" resultMap="DocumentDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM ai_document d
        LEFT JOIN ai_document_category c ON d.category_id = c.category_id
        LEFT JOIN ai_document_category ps ON d.product_series_id = ps.category_id
        LEFT JOIN ai_document_category pm ON d.product_model_id = pm.category_id
        WHERE d.del_flag = '0'
        AND d.product_series_id = #{productSeriesId}
        AND d.category_code = #{categoryCode}
        AND d.status = 1
        ORDER BY d.sort_order ASC, d.create_time DESC
    </select>

    <!-- 根据分类ID查询文档 -->
    <select id="selectDocumentsByCategoryId" resultMap="DocumentDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM ai_document d
        LEFT JOIN ai_document_category c ON d.category_id = c.category_id
        LEFT JOIN ai_document_category ps ON d.product_series_id = ps.category_id
        LEFT JOIN ai_document_category pm ON d.product_model_id = pm.category_id
        WHERE d.del_flag = '0'
        AND d.category_id = #{categoryId}
        AND d.status = 1
        ORDER BY d.sort_order ASC, d.create_time DESC
    </select>

    <!-- 根据产品型号ID查询所有文档 -->
    <select id="selectDocumentsByProductModelId" resultMap="DocumentDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM ai_document d
        LEFT JOIN ai_document_category c ON d.category_id = c.category_id
        LEFT JOIN ai_document_category ps ON d.product_series_id = ps.category_id
        LEFT JOIN ai_document_category pm ON d.product_model_id = pm.category_id
        WHERE d.del_flag = '0'
        AND d.product_model_id = #{productModelId}
        AND d.status = 1
        ORDER BY d.product_series_id ASC, d.category_code ASC, d.sort_order ASC, d.create_time DESC
    </select>

    <!-- 查询文档详情（包含分类信息） -->
    <select id="selectDocumentDetailById" resultMap="DocumentDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM ai_document d
        LEFT JOIN ai_document_category c ON d.category_id = c.category_id
        LEFT JOIN ai_document_category ps ON d.product_series_id = ps.category_id
        LEFT JOIN ai_document_category pm ON d.product_model_id = pm.category_id
        WHERE d.del_flag = '0'
        AND d.document_id = #{documentId}
    </select>

    <!-- 根据文件名查询文档 -->
    <select id="selectByNameAndCategoryId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_document d
        WHERE d.del_flag = '0'
        AND d.document_name = #{documentName}
        AND d.category_id = #{categoryId}
        LIMIT 1
    </select>

    <!-- 统计分类下的文档数量 -->
    <select id="countDocumentsByCategoryId" resultType="int">
        SELECT COUNT(*)
        FROM ai_document
        WHERE del_flag = '0'
        AND category_id = #{categoryId}
        AND status = 1
    </select>

    <!-- 统计产品系列下的文档数量 -->
    <select id="countDocumentsByProductSeriesId" resultType="int">
        SELECT COUNT(*)
        FROM ai_document
        WHERE del_flag = '0'
        AND product_series_id = #{productSeriesId}
        AND status = 1
    </select>

    <!-- 更新文档下载次数 -->
    <update id="incrementDownloadCount">
        UPDATE ai_document 
        SET download_count = download_count + 1,
            update_time = NOW()
        WHERE document_id = #{documentId}
        AND del_flag = '0'
    </update>

    <!-- 更新文档查看次数 -->
    <update id="incrementViewCount">
        UPDATE ai_document 
        SET view_count = view_count + 1,
            update_time = NOW()
        WHERE document_id = #{documentId}
        AND del_flag = '0'
    </update>

    <!-- 批量更新文档状态 -->
    <update id="batchUpdateStatus">
        UPDATE ai_document 
        SET status = #{status},
            update_time = NOW()
        WHERE document_id IN
        <foreach collection="documentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = '0'
    </update>

    <!-- 根据产品系列ID分组查询文档统计信息 -->
    <select id="selectDocumentGroupStatsByProductSeriesId" resultMap="BaseResultMap">
        SELECT 
            d.category_code,
            c.category_name,
            COUNT(*) as download_count,
            SUM(d.file_size) as file_size
        FROM ai_document d
        LEFT JOIN ai_document_category c ON d.category_id = c.category_id
        WHERE d.del_flag = '0'
        AND d.product_series_id = #{productSeriesId}
        AND d.status = 1
        GROUP BY d.category_code, c.category_name
        ORDER BY c.sort_order ASC
    </select>

</mapper>
