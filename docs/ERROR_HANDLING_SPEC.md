# 错误码与异常映射规范

## 设计目标
- 清晰表达错误来源与类别，便于定位与告警
- 对外返回稳定的错误结构，友好地提示客户端

## 错误码结构
- 采用三段式：<域>-<模块>-<编号>，如 `USR-AUTH-001`
  - 域（USR/CFG/DOC/AGN/SEC/COM）
  - 模块（AUTH/CRUD/CONF/UPLOAD/DOWNLOAD...）
  - 编号（三位数字，从 001 开始）

## 错误响应结构
```json
{
  "code": "USR-AUTH-001",
  "message": "用户名或密码错误",
  "traceId": "...",
  "details": {"field": "username"}
}
```

## 控制器层规范
- 全局异常处理器将业务异常映射为固定 JSON 结构
- 参数校验失败（MethodArgumentNotValidException）映射为 `COM-VALID-xxx`
- 鉴权失败（401/403）映射为 `SEC-AUTH-xxx`

## 业务异常建议
- 定义 BizException(code, message)，统一在 Service 抛出
- 控制器不吞异常，仅关注入参与返回包装

## 日志与追踪
- 每次错误记录 traceId（或 MDC）
- 严禁日志打印敏感信息（密码、Token、密钥等）

