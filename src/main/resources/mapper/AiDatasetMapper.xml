<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AiDatasetMapper">
  <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AiDataset">
    <!--@mbg.generated-->
    <!--@Table ai_dataset-->
    <id column="dataset_id" jdbcType="BIGINT" property="datasetId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="indexing_technique" jdbcType="VARCHAR" property="indexingTechnique" />
    <result column="process_rule" jdbcType="VARCHAR" property="processRule" />
    <result column="doc_form" jdbcType="VARCHAR" property="docForm" />
    <result column="retrieval_model" jdbcType="INTEGER" property="retrievalModel" />
    <result column="backend_id" jdbcType="VARCHAR" property="backendId" />
    <result column="dataset_type" jdbcType="VARCHAR" property="datasetType" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="agent_id" jdbcType="BIGINT" property="agentId" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    dataset_id, title, indexing_technique, process_rule, doc_form, retrieval_model, backend_id, 
    dataset_type, category_id, `enable`, agent_id, sort_order, create_by, create_time, 
    update_by, update_time, del_flag
  </sql>
</mapper>