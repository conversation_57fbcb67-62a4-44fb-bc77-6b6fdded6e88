package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.AiDocument;
import com.coocare.ai.entity.dto.AiDocumentQueryDTO;
import com.coocare.ai.entity.dto.AiDocumentUploadDTO;
import com.coocare.ai.entity.vo.AiDocumentGroupVO;
import com.coocare.ai.entity.vo.AiDocumentVO;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import io.github.guoshiqiufeng.dify.dataset.enums.document.DocActionEnum;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * AI文档 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface AiDocumentService extends IService<AiDocument> {

    /**
     * 分页查询文档列表
     *
     * @param pageDomain 分页参数
     * @param queryDTO   查询条件
     * @return 分页结果
     */
    PageUtils pageInfo(PageDomain pageDomain, AiDocumentQueryDTO queryDTO);

    /**
     * 上传文档
     *
     * @param file      上传的文件
     * @param uploadDTO 上传参数
     * @return 上传结果
     */
    AiDocumentVO uploadDocument(MultipartFile file, AiDocumentUploadDTO uploadDTO);

    /**
     * 根据产品系列ID查询文档并按第三层分类分组
     *
     * @param productSeriesId 产品系列ID
     * @return 分组后的文档列表
     */
    List<AiDocumentGroupVO> getDocumentsByProductSeriesGrouped(Long productSeriesId);

    /**
     * 根据产品系列ID和分类编码查询文档
     *
     * @param productSeriesId 产品系列ID
     * @param categoryCode    分类编码
     * @return 文档列表
     */
    List<AiDocumentVO> getDocumentsByProductSeriesAndCategory(Long productSeriesId, String categoryCode);

    /**
     * 根据分类ID查询文档
     *
     * @param categoryId 分类ID
     * @return 文档列表
     */
    List<AiDocumentVO> getDocumentsByCategoryId(Long categoryId);

    /**
     * 根据产品型号ID查询所有文档
     *
     * @param productModelId 产品型号ID
     * @return 文档列表
     */
    List<AiDocumentVO> getDocumentsByProductModelId(Long productModelId);

    /**
     * 获取文档详情
     *
     * @param documentId 文档ID
     * @return 文档详情
     */
    AiDocumentVO getDocumentDetail(Long documentId);

    /**
     * 更新文档信息
     *
     * @param documentId 文档ID
     * @param uploadDTO  更新参数
     * @return 更新结果
     */
    boolean updateDocument(Long documentId, AiDocumentUploadDTO uploadDTO);

    /**
     * 删除文档
     *
     * @param documentId 文档ID
     * @return 删除结果
     */
    boolean deleteDocument(Long documentId, Long datasetId);

    /**
     * 批量删除文档
     *
     * @param documentIds 文档ID列表
     * @return 删除结果
     */
    boolean deleteBatch(List<Long> documentIds, Long datasetId);

    /**
     * 更新文档状态
     *
     * @param documentId 文档ID
     * @param status     状态
     * @return 更新结果
     */
    boolean updateStatus(Long documentId, Long datasetId, DocActionEnum status);

    /**
     * 批量更新文档状态
     *
     * @param documentIds 文档ID列表
     * @param status      状态
     * @return 更新结果
     */
    boolean batchUpdateStatus(List<Long> documentIds, Long datasetId, DocActionEnum status);

    /**
     * 增加文档下载次数
     *
     * @param documentId 文档ID
     * @return 更新结果
     */
    boolean incrementDownloadCount(Long documentId);

    /**
     * 增加文档查看次数
     *
     * @param documentId 文档ID
     * @return 更新结果
     */
    boolean incrementViewCount(Long documentId);

    /**
     * 检查文档名称是否存在
     *
     * @param documentName 文档名称
     * @param categoryId   分类ID
     * @param documentId   文档ID（更新时排除自己）
     * @return 是否存在
     */
    boolean checkDocumentNameExists(String documentName, Long categoryId, Long documentId);

    /**
     * 获取分类下的文档数量
     *
     * @param categoryId 分类ID
     * @return 文档数量
     */
    int getDocumentCountByCategoryId(Long categoryId);

    /**
     * 获取产品系列下的文档数量
     *
     * @param productSeriesId 产品系列ID
     * @return 文档数量
     */
    int getDocumentCountByProductSeriesId(Long productSeriesId);

    /**
     * 下载文档
     *
     * @param documentId 文档ID
     * @return 文档文件信息
     */
    AiDocumentVO downloadDocument(Long documentId);

    /**
     * 根据产品系列ID获取文档分组统计信息
     *
     * @param productSeriesId 产品系列ID
     * @return 分组统计信息
     */
    List<AiDocumentGroupVO> getDocumentGroupStats(Long productSeriesId);
}
