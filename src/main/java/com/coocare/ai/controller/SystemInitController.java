package com.coocare.ai.controller;

import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.AiAgent;
import com.coocare.ai.entity.sys.dto.SystemInitDTO;
import com.coocare.ai.service.AiAgentService;
import com.coocare.ai.service.SystemInitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 系统初始化控制器
 * 用于系统首次启动时的初始化配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Tag(name = "系统初始化管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/init")
public class SystemInitController {

    private final SystemInitService systemInitService;
    private final AiAgentService aiAgentService;

    /**
     * 检查系统初始化状态
     * @return 初始化状态
     */
    @Operation(summary = "检查系统初始化状态", description = "检查系统是否已经完成初始化配置")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "检查成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/status")
    public AjaxResult<?> checkInitStatus() {
        try {
            boolean isInitialized = systemInitService.isSystemInitialized();
            Map<String, Object> result = new HashMap<>();
            result.put("initialized", isInitialized);
            result.put("message", isInitialized ? "系统已初始化" : "系统未初始化");

            return AjaxResult.ok(result);
        } catch (Exception e) {
            log.error("检查系统初始化状态失败", e);
            return AjaxResult.failed("检查系统初始化状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取初始化配置选项
     * @return 配置选项
     */
    @Operation(summary = "获取初始化配置选项", description = "获取系统初始化时可选择的配置项")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/options")
    public AjaxResult<?> getInitOptions() {
        try {
            Map<String, Object> options = new HashMap<>();

            // 行业选项
            List<String> industries = Arrays.asList(
                "制造业", "信息技术", "金融服务", "教育培训", "医疗健康",
                "电子商务", "房地产", "交通运输", "能源化工", "农业食品",
                "文化娱乐", "咨询服务", "政府机构", "非营利组织", "其他"
            );
            options.put("industries", industries);

            // 企业规模选项
            List<String> companyScales = Arrays.asList(
                "1-10人", "11-50人", "51-200人", "201-500人",
                "501-1000人", "1000-5000人", "5000人以上"
            );
            options.put("companyScales", companyScales);

            // 可用智能体选项
            List<AiAgent> availableAgents = aiAgentService.list();
            options.put("availableAgents", availableAgents);

            // 时区选项
            List<String> timeZones = Arrays.asList(
                "Asia/Shanghai", "Asia/Hong_Kong", "Asia/Taipei",
                "UTC", "America/New_York", "Europe/London"
            );
            options.put("timeZones", timeZones);

            // 语言选项
            List<String> languages = Arrays.asList(
                "zh-CN", "zh-TW", "en-US", "ja-JP", "ko-KR"
            );
            options.put("languages", languages);

            return AjaxResult.ok(options);
        } catch (Exception e) {
            log.error("获取初始化配置选项失败", e);
            return AjaxResult.failed("获取初始化配置选项失败: " + e.getMessage());
        }
    }

    /**
     * 执行系统初始化
     * @param initDTO 初始化配置信息
     * @return 初始化结果
     */
    @Operation(summary = "执行系统初始化", description = "根据提供的配置信息执行系统初始化")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "初始化成功"),
        @ApiResponse(responseCode = "400", description = "参数校验失败或系统已初始化"),
        @ApiResponse(responseCode = "500", description = "初始化失败")
    })
    @PostMapping("/execute")
    public AjaxResult<?> executeInit(
            @Parameter(description = "系统初始化配置信息", required = true)
            @Valid @RequestBody SystemInitDTO initDTO) {
        try {
            // 检查系统是否已经初始化
            if (systemInitService.isSystemInitialized()) {
                return AjaxResult.failed("系统已经初始化，无法重复初始化");
            }

            // 执行初始化
            boolean success = systemInitService.initializeSystem(initDTO);

            if (success) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("message", "系统初始化成功");
                result.put("adminUsername", initDTO.getAdminUsername());
                result.put("companyName", initDTO.getCompanyName());

                log.info("系统初始化成功，管理员用户: {}, 企业: {}",
                    initDTO.getAdminUsername(), initDTO.getCompanyName());

                return AjaxResult.ok(result);
            } else {
                return AjaxResult.failed("系统初始化失败");
            }

        } catch (Exception e) {
            log.error("系统初始化失败", e);
            return AjaxResult.failed("系统初始化失败: " + e.getMessage());
        }
    }

    /**
     * 重置系统初始化状态（仅用于开发测试）
     * @return 重置结果
     */
    @Operation(summary = "重置系统初始化状态", description = "重置系统初始化状态，仅用于开发测试环境")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "重置成功"),
        @ApiResponse(responseCode = "500", description = "重置失败")
    })
    @PostMapping("/reset")
    public AjaxResult<?> resetInitStatus() {
        try {
            // 注意：这个接口仅用于开发测试，生产环境应该禁用
            // 可以通过配置文件或环境变量控制是否启用

            log.warn("重置系统初始化状态 - 仅用于开发测试");

            // 这里可以添加重置逻辑，比如删除初始化标识配置
            // configService.removeSysConfig("SYSTEM_INITIALIZED");

            return AjaxResult.ok("系统初始化状态已重置（仅用于开发测试）");

        } catch (Exception e) {
            log.error("重置系统初始化状态失败", e);
            return AjaxResult.failed("重置失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统初始化进度
     * @return 初始化进度信息
     */
    @Operation(summary = "获取系统初始化进度", description = "获取系统初始化的详细进度信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "获取失败")
    })
    @GetMapping("/progress")
    public AjaxResult<?> getInitProgress() {
        try {
            Map<String, Object> progress = new HashMap<>();

            // 检查各个初始化步骤的完成状态
            boolean isInitialized = systemInitService.isSystemInitialized();

            progress.put("overall", isInitialized);
            progress.put("steps", Arrays.asList(
                createStepInfo("检查系统状态", true),
                createStepInfo("创建默认角色", isInitialized),
                createStepInfo("创建默认部门", isInitialized),
                createStepInfo("创建管理员账号", isInitialized),
                createStepInfo("配置企业信息", isInitialized),
                createStepInfo("配置行业信息", isInitialized),
                createStepInfo("配置智能体", isInitialized),
                createStepInfo("配置系统参数", isInitialized)
            ));

            return AjaxResult.ok(progress);

        } catch (Exception e) {
            log.error("获取系统初始化进度失败", e);
            return AjaxResult.failed("获取初始化进度失败: " + e.getMessage());
        }
    }

    /**
     * 创建步骤信息
     */
    private Map<String, Object> createStepInfo(String name, boolean completed) {
        Map<String, Object> step = new HashMap<>();
        step.put("name", name);
        step.put("completed", completed);
        step.put("status", completed ? "success" : "pending");
        return step;
    }

}
