# 本地开发环境（Docker Compose）

## 启动依赖
```bash
docker compose up -d
```
- MySQL: 3306/root:root，库 zhan_ai
- Redis: 6379，无密码
- MinIO: 9000(S3)/9001(Console)，admin/admin123456

## 初始化 MinIO
1. 打开 http://localhost:9001 登录控制台
2. 创建桶：zhan-ai（公共读建议按需配置）
3. 记录 endpoint：http://127.0.0.1:9000

## 应用环境变量（示例）
```bash
export MYSQL_HOST=127.0.0.1
export MYSQL_PORT=3306
export MYSQL_DB=zhan_ai
export MYSQL_USER=root
export MYSQL_PASS=root

export REDIS_HOST=127.0.0.1
export REDIS_PORT=6379

export OSS_ENDPOINT=http://127.0.0.1:9000
export OSS_ACCESS_KEY=admin
export OSS_SECRET_KEY=admin123456
export OSS_BUCKET=zhan-ai
export OSS_PATH_STYLE=true
```

## 运行应用（示例）
```bash
mvn -DskipTests package
java -jar target/zhan-ai-enterprise-edition.jar
```

## 常见问题
- MySQL 连接失败：确认 3306 端口未被占用，或修改映射端口
- MinIO 直链访问失败：检查桶策略与 path-style 配置

