package com.coocare.ai.controller;

import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.service.AiChatStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * AI对话统计 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Tag(name = "AI对话统计管理", description = "AI对话统计相关接口")
@RestController
@RequestMapping("/ai/chat/statistics")
@RequiredArgsConstructor
public class AiChatStatisticsController {

    private final AiChatStatisticsService aiChatStatisticsService;

    @Operation(summary = "获取Token使用统计", description = "根据时间范围获取Token使用统计信息")
    @GetMapping("/token")
    public AjaxResult<Map<String, Object>> getTokenStatistics(
            @Parameter(description = "开始时间", example = "2025-09-01 00:00:00")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间", example = "2025-09-03 23:59:59")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        Map<String, Object> statistics = aiChatStatisticsService.getTokenStatistics(startTime, endTime);
        return AjaxResult.ok(statistics);
    }

    @Operation(summary = "获取智能体使用统计", description = "根据智能体ID和时间范围获取使用统计")
    @GetMapping("/agent/{agentId}")
    public AjaxResult<Map<String, Object>> getAgentStatistics(
            @Parameter(description = "智能体ID")
            @PathVariable Long agentId,
            @Parameter(description = "开始时间", example = "2025-09-01 00:00:00")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间", example = "2025-09-03 23:59:59")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        Map<String, Object> statistics = aiChatStatisticsService.getAgentStatistics(agentId, startTime, endTime);
        return AjaxResult.ok(statistics);
    }

    @Operation(summary = "获取用户使用统计", description = "根据用户ID和时间范围获取使用统计")
    @GetMapping("/user/{userId}")
    public AjaxResult<Map<String, Object>> getUserStatistics(
            @Parameter(description = "用户ID")
            @PathVariable Long userId,
            @Parameter(description = "开始时间", example = "2025-09-01 00:00:00")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间", example = "2025-09-03 23:59:59")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        Map<String, Object> statistics = aiChatStatisticsService.getUserStatistics(userId, startTime, endTime);
        return AjaxResult.ok(statistics);
    }

    @Operation(summary = "获取热门智能体排行榜", description = "根据时间范围获取热门智能体排行榜")
    @GetMapping("/popular-agents")
    public AjaxResult<List<Map<String, Object>>> getPopularAgents(
            @Parameter(description = "开始时间", example = "2025-09-01 00:00:00")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间", example = "2025-09-03 23:59:59")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "限制数量", example = "10")
            @RequestParam(defaultValue = "10") Integer limit) {
        
        List<Map<String, Object>> popularAgents = aiChatStatisticsService.getPopularAgents(startTime, endTime, limit);
        return AjaxResult.ok(popularAgents);
    }

    @Operation(summary = "获取活跃用户排行榜", description = "根据时间范围获取活跃用户排行榜")
    @GetMapping("/active-users")
    public AjaxResult<List<Map<String, Object>>> getActiveUsers(
            @Parameter(description = "开始时间", example = "2025-09-01 00:00:00")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间", example = "2025-09-03 23:59:59")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "限制数量", example = "10")
            @RequestParam(defaultValue = "10") Integer limit) {
        
        List<Map<String, Object>> activeUsers = aiChatStatisticsService.getActiveUsers(startTime, endTime, limit);
        return AjaxResult.ok(activeUsers);
    }

    @Operation(summary = "手动记录对话统计", description = "手动记录一条对话统计信息（用于测试）")
    @PostMapping("/record")
    public AjaxResult<Boolean> recordChatStatistics(
            @Parameter(description = "对话ID") @RequestParam String conversationId,
            @Parameter(description = "消息ID") @RequestParam(required = false) String messageId,
            @Parameter(description = "智能体ID") @RequestParam(required = false) Long agentId,
            @Parameter(description = "智能体名称") @RequestParam(required = false) String agentName,
            @Parameter(description = "Token数量") @RequestParam(required = false) Integer tokenCount,
            @Parameter(description = "输入Token数量") @RequestParam(required = false) Integer inputTokens,
            @Parameter(description = "输出Token数量") @RequestParam(required = false) Integer outputTokens,
            @Parameter(description = "总Token数量") @RequestParam(required = false) Integer totalTokens,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "用户输入") @RequestParam(required = false) String userInput,
            @Parameter(description = "AI回复") @RequestParam(required = false) String aiResponse,
            @Parameter(description = "事件类型") @RequestParam(required = false) String eventType,
            @Parameter(description = "状态") @RequestParam(defaultValue = "SUCCESS") String status,
            @Parameter(description = "响应时间") @RequestParam(required = false) Long responseTime) {
        
        boolean success = aiChatStatisticsService.recordChatStatistics(
            conversationId, messageId, agentId, agentName, inputTokens, outputTokens, totalTokens,
            userId, username
        );
        
        return AjaxResult.ok(success);
    }
}
