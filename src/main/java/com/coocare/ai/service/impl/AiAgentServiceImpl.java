package com.coocare.ai.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.AiAgent;
import com.coocare.ai.entity.AiDeptAgent;
import com.coocare.ai.mapper.AiAgentMapper;
import com.coocare.ai.mapper.AiDeptAgentMapper;
import com.coocare.ai.service.AiAgentService;
import com.coocare.ai.service.SysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 智能体领域服务实现
 * 负责智能体的启用状态读取、默认智能体、与部门的绑定关系维护
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiAgentServiceImpl extends ServiceImpl<AiAgentMapper, AiAgent> implements AiAgentService{

    private final AiDeptAgentMapper aiDeptAgentMapper;
    private final SysConfigService configService;

    @Override
    public List<AiAgent> getAllAvailableAgents() {
        try {
            // 获取所有智能体，包括启用和禁用的
            List<AiAgent> allAgents = list();
            log.info("获取到 {} 个智能体", allAgents.size());
            return allAgents;
        } catch (Exception e) {
            log.error("获取所有可用智能体失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Long> getEnabledAgents() {
        try {
            String enabledAgentsJson = configService.getValue("ENABLED_AGENTS");
            if (StrUtil.isBlank(enabledAgentsJson)) {
                log.info("未找到启用的智能体配置");
                return new ArrayList<>();
            }

            List<Long> enabledAgents = JSONUtil.toList(enabledAgentsJson, Long.class);
            log.info("获取到 {} 个启用的智能体", enabledAgents.size());
            return enabledAgents;
        } catch (Exception e) {
            log.error("获取启用智能体列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public String getDefaultAgent() {
        try {
            String defaultAgent = configService.getValue("DEFAULT_AGENT");
            if (StrUtil.isBlank(defaultAgent)) {
                log.info("未找到默认智能体配置");
                return null;
            }

            log.info("获取到默认智能体: {}", defaultAgent);
            return defaultAgent;
        } catch (Exception e) {
            log.error("获取默认智能体失败", e);
            return null;
        }
    }

    @Override
    public List<AiAgent> getEnabledAgentDetails() {
        try {
            List<Long> enabledAgentIds = getEnabledAgents();
            if (enabledAgentIds.isEmpty()) {
                log.info("没有启用的智能体");
                return new ArrayList<>();
            }

            // 根据ID列表获取智能体详细信息
            List<AiAgent> enabledAgents = list(
                    Wrappers.<AiAgent>lambdaQuery().in(AiAgent::getAgentId, enabledAgentIds)
            );

            // 按照启用列表的顺序排序
            List<AiAgent> sortedAgents = enabledAgentIds.stream()
                    .map(id -> enabledAgents.stream()
                            .filter(agent -> agent.getAgentId().equals(id))
                            .findFirst()
                            .orElse(null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("获取到 {} 个启用智能体的详细信息", sortedAgents.size());
            return sortedAgents;
        } catch (Exception e) {
            log.error("获取启用智能体详细信息失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDeptAgents(Long deptId, List<Long> agentIds, Long defaultAgentId) {
        // 删除现有关联
        aiDeptAgentMapper.deleteByDeptId(deptId);

        if (CollUtil.isEmpty(agentIds)) {
            return true;
        }

        // 创建新的关联
        List<AiDeptAgent> deptAgents = new ArrayList<>();
        for (int i = 0; i < agentIds.size(); i++) {
            Long agentId = agentIds.get(i);
            AiDeptAgent deptAgent = new AiDeptAgent();
            deptAgent.setDeptId(deptId);
            deptAgent.setAgentId(agentId);
            deptAgent.setIsDefault(agentId.equals(defaultAgentId) ? Boolean.TRUE : Boolean.FALSE);
            deptAgent.setSortOrder(i + 1);
            deptAgent.setCreateBy("system");
            deptAgent.setDelFlag("0");
            deptAgents.add(deptAgent);
        }

        return aiDeptAgentMapper.batchInsert(deptAgents) > 0;
    }

    @Override
    public List<AiAgent> getDeptAgents(Long deptId) {
        return aiDeptAgentMapper.selectAgentsByDeptId(deptId);
    }
}
