package com.coocare.ai.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * AI文档分类查询请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "AI文档分类查询请求DTO")
public class AiDocumentCategoryQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类名称（模糊查询）")
    private String categoryName;

    @Schema(description = "分类层级：1-产品型号，2-产品系列，3-文档类型")
    private Integer level;

    @Schema(description = "父级分类ID")
    private Long parentId;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "是否系统分类：0-否，1-是")
    private Integer isSystem;

    @Schema(description = "页码", example = "1")
    private Integer pageNo = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
}
