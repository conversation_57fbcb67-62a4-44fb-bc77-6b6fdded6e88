package com.coocare.ai.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.AiDocumentCategory;
import com.coocare.ai.mapper.AiDocumentCategoryMapper;
import com.coocare.ai.service.AiDocumentCategoryService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * AI文档分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiDocumentCategoryServiceImpl extends ServiceImpl<AiDocumentCategoryMapper, AiDocumentCategory>
        implements AiDocumentCategoryService {

    @Override
    public PageUtils pageInfo(PageDomain pageDomain, String categoryName, Integer level, Long parentId) {
        LambdaQueryWrapper<AiDocumentCategory> wrapper = Wrappers.lambdaQuery(AiDocumentCategory.class)
                .like(StrUtil.isNotBlank(categoryName), AiDocumentCategory::getCategoryName, categoryName)
                .eq(level != null, AiDocumentCategory::getLevel, level)
                .eq(parentId != null, AiDocumentCategory::getParentId, parentId)
                .eq(AiDocumentCategory::getDelFlag, "0")
                .orderByAsc(AiDocumentCategory::getLevel)
                .orderByAsc(AiDocumentCategory::getSortOrder)
                .orderByAsc(AiDocumentCategory::getCreateTime);

        Page<AiDocumentCategory> page = this.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), wrapper);
        return new PageUtils(page);
    }

    @Override
    public List<AiDocumentCategory> getChildrenByParentId(Long parentId) {
        return baseMapper.selectByParentId(parentId);
    }

    @Override
    public List<AiDocumentCategory> getCategoryTree(Long parentId) {
        List<AiDocumentCategory> allCategories = baseMapper.selectCategoryTree(parentId);
        return buildTree(allCategories, parentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createProductModel(AiDocumentCategory category) {
        // 验证参数
        if (category == null || StrUtil.isBlank(category.getCategoryName())) {
            throw new IllegalArgumentException("分类名称不能为空");
        }

        // 检查名称是否重复
        if (checkCategoryNameExists(category.getCategoryName(), null, null)) {
            throw new IllegalArgumentException("产品型号名称已存在");
        }

        // 设置第一层属性
        category.setLevel(AiDocumentCategory.LEVEL_PRODUCT_MODEL);
        category.setParentId(null);
        category.setDelFlag("0");
        category.setCreateTime(LocalDateTime.now());

        return this.save(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createProductSeries(AiDocumentCategory category) {
        // 验证参数
        if (category == null || StrUtil.isBlank(category.getCategoryName()) || category.getParentId() == null) {
            throw new IllegalArgumentException("分类名称和父级分类不能为空");
        }

        // 验证父级分类是否存在且为第一层
        AiDocumentCategory parent = this.getById(category.getParentId());
        if (parent == null || AiDocumentCategory.LEVEL_PRODUCT_MODEL != parent.getLevel()) {
            throw new IllegalArgumentException("父级分类不存在或不是产品型号分类");
        }

        // 检查名称是否重复
        if (checkCategoryNameExists(category.getCategoryName(), category.getParentId(), null)) {
            throw new IllegalArgumentException("产品系列名称在该产品型号下已存在");
        }

        // 设置第二层属性
        category.setLevel(AiDocumentCategory.LEVEL_PRODUCT_SERIES);
        category.setDelFlag("0");
        category.setCreateTime(LocalDateTime.now());

        boolean result = this.save(category);

        // 自动创建第三层文档类型分类
        if (result) {
            createDefaultDocumentTypes(category.getCategoryId());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createDefaultDocumentTypes(Long productSeriesId) {
        // 验证产品系列是否存在
        AiDocumentCategory productSeries = this.getById(productSeriesId);
        if (productSeries == null || AiDocumentCategory.LEVEL_PRODUCT_SERIES != productSeries.getLevel()) {
            throw new IllegalArgumentException("产品系列不存在或层级错误");
        }

        List<AiDocumentCategory> documentTypes = new ArrayList<>();
        for (int i = 0; i < AiDocumentCategory.DEFAULT_DOCUMENT_TYPES.length; i++) {
            String typeName = AiDocumentCategory.DEFAULT_DOCUMENT_TYPES[i];
            String typeCode = AiDocumentCategory.DEFAULT_DOCUMENT_CODES[i];

            AiDocumentCategory documentType = new AiDocumentCategory();
            documentType.setCategoryName(typeName);
            documentType.setCategoryCode(typeCode);
            documentType.setParentId(productSeriesId);
            documentType.setLevel(AiDocumentCategory.LEVEL_DOCUMENT_TYPE);
            documentType.setSortOrder(i + 1);
            documentType.setDelFlag("0");
            documentType.setCreateTime(LocalDateTime.now());

            documentTypes.add(documentType);
        }

        return this.saveBatch(documentTypes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(AiDocumentCategory category) {
        if (category == null || category.getCategoryId() == null) {
            throw new IllegalArgumentException("分类ID不能为空");
        }

        // 检查分类是否存在
        AiDocumentCategory existing = this.getById(category.getCategoryId());
        if (existing == null) {
            throw new IllegalArgumentException("分类不存在");
        }

        // 检查名称是否重复（排除自己）
        if (StrUtil.isNotBlank(category.getCategoryName()) &&
            checkCategoryNameExists(category.getCategoryName(), existing.getParentId(), category.getCategoryId())) {
            throw new IllegalArgumentException("分类名称已存在");
        }

        category.setUpdateTime(LocalDateTime.now());
        return this.updateById(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategory(Long categoryId) {
        if (categoryId == null) {
            throw new IllegalArgumentException("分类ID不能为空");
        }

        // 检查是否存在子分类
        if (hasChildren(categoryId)) {
            throw new IllegalArgumentException("存在子分类，无法删除");
        }

        // 逻辑删除
        AiDocumentCategory category = new AiDocumentCategory();
        category.setCategoryId(categoryId);
        category.setDelFlag("1");
        category.setUpdateTime(LocalDateTime.now());

        return this.updateById(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(List<Long> categoryIds) {
        if (categoryIds == null || categoryIds.isEmpty()) {
            return true;
        }

        // 检查每个分类是否可以删除
        for (Long categoryId : categoryIds) {
            if (hasChildren(categoryId)) {
                throw new IllegalArgumentException("分类ID " + categoryId + " 存在子分类，无法删除");
            }
        }

        // 批量逻辑删除
        List<AiDocumentCategory> categories = categoryIds.stream()
                .map(id -> {
                    AiDocumentCategory category = new AiDocumentCategory();
                    category.setCategoryId(id);
                    category.setDelFlag("1");
                    category.setUpdateTime(LocalDateTime.now());
                    return category;
                })
                .collect(Collectors.toList());

        return this.updateBatchById(categories);
    }

    @Override
    public boolean checkCategoryNameExists(String categoryName, Long parentId, Long categoryId) {
        LambdaQueryWrapper<AiDocumentCategory> wrapper = Wrappers.lambdaQuery(AiDocumentCategory.class)
                .eq(AiDocumentCategory::getCategoryName, categoryName)
                .eq(parentId != null, AiDocumentCategory::getParentId, parentId)
                .isNull(parentId == null, AiDocumentCategory::getParentId)
                .ne(categoryId != null, AiDocumentCategory::getCategoryId, categoryId)
                .eq(AiDocumentCategory::getDelFlag, "0");

        return this.count(wrapper) > 0;
    }

    @Override
    public boolean hasChildren(Long categoryId) {
        return baseMapper.countByParentId(categoryId) > 0;
    }

    @Override
    public boolean updateStatus(Long categoryId, Boolean status) {
        AiDocumentCategory category = new AiDocumentCategory();
        category.setCategoryId(categoryId);
        category.setStatus(status);
        category.setUpdateTime(LocalDateTime.now());
        return this.updateById(category);
    }

    @Override
    public boolean updateSortOrder(Long categoryId, Integer sortOrder) {
        AiDocumentCategory category = new AiDocumentCategory();
        category.setCategoryId(categoryId);
        category.setSortOrder(sortOrder);
        category.setUpdateTime(LocalDateTime.now());
        return this.updateById(category);
    }

    @Override
    public String getCategoryPath(Long categoryId) {
        List<AiDocumentCategory> ancestors = getAncestors(categoryId);
        AiDocumentCategory current = this.getById(categoryId);

        if (current == null) {
            return "";
        }

        List<String> pathNames = ancestors.stream()
                .map(AiDocumentCategory::getCategoryName)
                .collect(Collectors.toList());
        pathNames.add(current.getCategoryName());

        return String.join(" > ", pathNames);
    }

    @Override
    public List<AiDocumentCategory> getAncestors(Long categoryId) {
        List<AiDocumentCategory> ancestors = new ArrayList<>();
        AiDocumentCategory current = this.getById(categoryId);

        while (current != null && current.getParentId() != null) {
            AiDocumentCategory parent = this.getById(current.getParentId());
            if (parent != null) {
                ancestors.add(0, parent); // 插入到开头，保持从根到父的顺序
                current = parent;
            } else {
                break;
            }
        }

        return ancestors;
    }

    @Override
    public List<AiDocumentCategory> getDescendants(Long categoryId) {
        List<AiDocumentCategory> descendants = new ArrayList<>();
        collectDescendants(categoryId, descendants);
        return descendants;
    }

    /**
     * 递归收集所有后代节点
     */
    private void collectDescendants(Long parentId, List<AiDocumentCategory> descendants) {
        List<AiDocumentCategory> children = getChildrenByParentId(parentId);
        for (AiDocumentCategory child : children) {
            descendants.add(child);
            collectDescendants(child.getCategoryId(), descendants);
        }
    }

    /**
     * 构建树结构
     */
    private List<AiDocumentCategory> buildTree(List<AiDocumentCategory> allCategories, Long parentId) {
        return allCategories.stream()
                .filter(category -> {
                    if (parentId == null) {
                        return category.getParentId() == null;
                    } else {
                        return parentId.equals(category.getParentId());
                    }
                })
                .peek(category -> {
                    List<AiDocumentCategory> children = buildTree(allCategories, category.getCategoryId());
                    category.setChildren(children);
                })
                .collect(Collectors.toList());
    }
}
