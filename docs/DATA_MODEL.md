# 数据模型（ER）

## 核心实体与关系（Mermaid）

```mermaid
erDiagram
  sys_user ||--o{ sys_user_role : has
  sys_role ||--o{ sys_user_role : has
  sys_role ||--o{ sys_role_menu : has
  sys_menu ||--o{ sys_role_menu : has
  sys_dept ||--o{ sys_user : belongs_to

  ai_document ||--o{ ai_document_category : categorized_by

  sys_user {
    bigint id PK
    varchar username
    varchar password
    varchar email
    varchar phone
    bigint dept_id FK
    tinyint status
    tinyint del_flag
    datetime create_time
    datetime update_time
  }
  sys_role {
    bigint id PK
    varchar code
    varchar name
    tinyint status
    tinyint del_flag
    datetime create_time
    datetime update_time
  }
  sys_menu {
    bigint id PK
    bigint parent_id
    varchar name
    varchar path
    varchar perms
    int sort
    tinyint type
    tinyint visible
    tinyint status
  }
  sys_user_role {
    bigint user_id FK
    bigint role_id FK
  }
  sys_role_menu {
    bigint role_id FK
    bigint menu_id FK
  }
  sys_dept {
    bigint id PK
    bigint parent_id
    varchar name
    int sort
    tinyint status
    tinyint del_flag
  }
  ai_document {
    bigint id PK
    varchar name
    varchar url
    bigint category_id FK
    bigint size
    varchar mime
    tinyint status
    tinyint del_flag
    datetime create_time
  }
  ai_document_category {
    bigint id PK
    bigint parent_id
    varchar name
    int sort
    tinyint status
    tinyint del_flag
  }
  sys_config {
    bigint id PK
    varchar config_key
    varchar config_value
    varchar remark
    tinyint visible
    datetime create_time
    datetime update_time
  }
```

## 字段与约束（要点）
- 所有表采用逻辑删除 del_flag（0/1）
- 审计字段：create_by/create_time/update_by/update_time（未在所有表中展开）
- 唯一索引：sys_user.username, sys_role.code, sys_menu.path（视实现）
- 外键（逻辑）：user.dept_id -> sys_dept.id；ai_document.category_id -> ai_document_category.id

## 设计说明
- 菜单与角色多对多，用户与角色多对多
- 部门树通过 parent_id 构建；菜单树亦如是
- 文档分类同样为树形结构；文档与分类多对一

