package com.coocare.ai.utils;

import java.util.Collection;

/**
 * 判空工具类
 * 提供对对象、数组、集合、字节数组、字符串与数值类型的判空封装，统一空值语义
 * <p>
 * 注意：方法均为无状态静态方法，线程安全
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public class EmptyUtil {

    public static boolean isEmpty(Object objs) {
        return objs == null;
    }

    public static boolean isEmpty(Object[] objs) {
        return (objs == null) || (objs.length == 0);
    }

    public static boolean isEmpty(Collection<?> objs) {
        return (objs == null) || (objs.size() <= 0);
    }

    public static boolean isEmpty(byte[] objs) {
        return (objs == null) || (objs.length == 0);
    }

    public static boolean isEmpty(String str) {
        return (str == null) || (str.trim().length() == 0);
    }

    public static boolean isEmpty(Long l) {
        return (l == null) || (l == 0L);
    }

    public static boolean isEmpty(Integer objs) {
        return (objs == null) || (objs == 0);
    }

    public static boolean isNotEmpty(Object objs) {
        return !isEmpty(objs);
    }

    public static boolean isNotEmpty(Object[] objs) {
        return !isEmpty(objs);
    }

    public static boolean isNotEmpty(Collection<?> objs) {
        return !isEmpty(objs);
    }

    public static boolean isNotEmpty(byte[] objs) {
        return !isEmpty(objs);
    }

    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    public static boolean isNotEmpty(Long l) {
        return !isEmpty(l);
    }

    public static boolean isNotEmpty(Integer objs) {
        return !isEmpty(objs);
    }
}
