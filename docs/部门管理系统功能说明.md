# 多级部门管理系统功能说明

## 概述

本系统实现了完整的多级部门管理功能，支持管理员新增多级部门、根据部门新增用户、设置部门主管，以及部门与AI智能体的关联管理。

## 主要功能

### 1. 多级部门管理

#### 1.1 数据库设计
- **部门表扩展**：在原有`sys_dept`表基础上新增了以下字段：
  - `dept_code`：部门编码
  - `dept_type`：部门类型（NORMAL-普通部门，VIRTUAL-虚拟部门）
  - `manager_user_id`：部门主管用户ID
  - `description`：部门描述
  - `status`：部门状态

#### 1.2 核心功能
- 支持无限级部门层次结构
- 部门树形展示和管理
- 部门编码管理
- 部门状态控制

### 2. 用户部门管理

#### 2.1 数据库设计
- **用户表扩展**：在`sys_user`表中新增：
  - `is_dept_manager`：是否部门主管标识

#### 2.2 核心功能
- 用户归属部门设置
- 部门主管指定和管理
- 批量用户部门操作
- 用户在部门间转移

### 3. AI智能体关联

#### 3.1 数据库设计
- **AI智能体表**：`ai_agent`
  - `agent_id`：智能体ID
  - `agent_name`：智能体名称
  - `agent_intro`：智能体介绍
  - `agent_description`：详细描述
  - `agent_type`：智能体类型
  - `enable`：是否启用

- **部门智能体关联表**：`sys_dept_agent`
  - `dept_agent_id`：关联ID
  - `dept_id`：部门ID
  - `agent_id`：智能体ID
  - `is_default`：是否默认智能体
  - `sort_order`：排序号

#### 3.2 核心功能
- 部门可关联多个AI智能体
- 设置部门默认AI智能体
- 智能体使用权限控制

## API接口说明

### 部门管理接口

#### 1. 创建部门
```
POST /sys/dept/create
```
**请求参数**：`DeptCreateDTO`
- 支持设置部门基本信息
- 可同时关联AI智能体
- 可设置部门主管

#### 2. 更新部门
```
PUT /sys/dept/update
```
**请求参数**：`DeptUpdateDTO`

#### 3. 获取部门详情
```
GET /sys/dept/detail/{deptId}
```
**返回**：包含AI智能体信息的完整部门信息

#### 4. 部门树形结构
```
GET /sys/dept/tree-with-agents
```
**参数**：
- `parentId`：父部门ID（可选）
- `includeAgents`：是否包含AI智能体信息

#### 5. 部门AI智能体管理
```
GET /sys/dept/{deptId}/agents          # 获取部门智能体列表
GET /sys/dept/{deptId}/default-agent   # 获取默认智能体
POST /sys/dept/{deptId}/agents         # 设置部门智能体关联
```

#### 6. 部门主管管理
```
POST /sys/dept/{deptId}/manager/{userId}  # 设置部门主管
DELETE /sys/dept/{deptId}/manager         # 移除部门主管
```

### 用户部门管理接口

#### 1. 部门用户查询
```
GET /sys/user/dept/{deptId}              # 获取部门用户列表
GET /sys/user/dept/{deptId}/page         # 分页查询部门用户
GET /sys/user/dept/{deptId}/managers     # 获取部门主管列表
```

#### 2. 用户部门操作
```
POST /sys/user/dept/set                  # 设置用户部门
POST /sys/user/dept/batch-set           # 批量设置用户部门
POST /sys/user/dept/transfer            # 转移用户到其他部门
POST /sys/user/dept/remove              # 从部门移除用户
```

#### 3. 部门主管操作
```
POST /sys/user/dept/manager/set         # 设置部门主管
POST /sys/user/dept/manager/remove      # 移除部门主管
```

## 数据传输对象（DTO）

### 部门相关DTO

#### DeptCreateDTO
- 部门创建时使用
- 包含基本信息和AI智能体关联

#### DeptUpdateDTO
- 部门更新时使用
- 支持部分字段更新

#### DeptQueryDTO
- 部门查询条件
- 支持多条件组合查询

#### DeptVO
- 部门详细信息展示
- 包含关联的AI智能体和用户统计

### 用户相关DTO

#### UserDeptDTO
- 用户部门操作
- 支持单个和批量操作

#### DeptManagerDTO
- 部门主管设置
- 包含操作类型

#### UserDeptVO
- 用户部门信息展示
- 包含部门详细信息

## 使用示例

### 1. 创建多级部门结构

```json
// 创建一级部门
{
  "name": "技术中心",
  "deptCode": "TECH",
  "parentId": 1,
  "description": "技术研发中心",
  "agentIds": [1, 2],
  "defaultAgentId": 2
}

// 创建二级部门
{
  "name": "前端开发部",
  "deptCode": "FRONTEND",
  "parentId": 技术中心ID,
  "managerUserId": 用户ID,
  "agentIds": [2],
  "defaultAgentId": 2
}
```

### 2. 设置部门主管

```json
{
  "deptId": 部门ID,
  "userId": 用户ID,
  "operationType": "SET"
}
```

### 3. 批量设置用户部门

```json
{
  "userIds": [用户ID1, 用户ID2, 用户ID3],
  "deptId": 目标部门ID,
  "operationType": "ADD"
}
```

## 权限控制

- 管理员可以创建和管理所有部门
- 部门主管可以管理本部门及子部门的用户
- 普通用户只能查看自己所在部门信息
- AI智能体使用权限基于部门关联控制

## 注意事项

1. **数据一致性**：部门主管设置时会自动更新用户表的主管标识
2. **级联操作**：删除部门时需要先处理子部门和部门用户
3. **权限验证**：所有操作都应该进行相应的权限验证
4. **事务管理**：涉及多表操作的接口都使用了事务管理
5. **缓存更新**：用户信息变更时会清除相关缓存

## 扩展建议

1. **审批流程**：可以为部门创建、用户调动等操作添加审批流程
2. **历史记录**：记录部门变更和用户调动历史
3. **统计报表**：提供部门人员统计和AI智能体使用统计
4. **通知机制**：部门变更时自动通知相关人员
5. **导入导出**：支持批量导入部门结构和用户信息
