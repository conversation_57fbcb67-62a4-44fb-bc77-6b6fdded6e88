package com.coocare.ai.controller.sys;

import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.plugins.logging.annotation.Logging;
import com.coocare.ai.entity.sys.SysDept;
import com.coocare.ai.entity.sys.dto.DeptQueryDTO;
import com.coocare.ai.service.SysDeptService;
import com.coocare.ai.utils.PageDomain;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 部门管理控制器
 * 提供部门树、分页、CRUD、批量删除、部门主管设置等接口
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Tag(name = "部门管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/dept")
public class SysDeptController {

    private final SysDeptService sysDeptService;

    /**
     * 获取部门树形结构
     * @param parentId 父部门ID
     * @param name 部门名称
     * @return 树形结构
     */
    @Operation(summary = "获取部门树形结构", description = "根据父部门ID与名称筛选，返回树形部门结构", operationId = "sysDept_tree")
    @GetMapping("/tree")
    public AjaxResult<?> treeDept(Long parentId, String name) {
        return AjaxResult.ok(sysDeptService.treeDept(parentId, name));
    }

    /**
     * 分页查询部门信息
     * @param pageDomain 分页参数
     * @param name 部门名称
     * @return 分页结果
     */
    @Operation(summary = "分页查询部门信息", description = "根据名称分页查询部门列表", operationId = "sysDept_page")
    @GetMapping("/page")
    public AjaxResult<?> pageInfo(PageDomain pageDomain, String name) {
        return AjaxResult.ok(sysDeptService.pageInfo(pageDomain, name));
    }

    /**
     * 根据ID查询部门详细信息
     * @param id 部门ID
     * @return 部门详细信息
     */
    @Operation(summary = "根据ID查询部门详细信息", description = "根据部门ID获取部门详情", operationId = "sysDept_getById")
    @GetMapping("/{id}")
    public AjaxResult<?> getDeptInfoById(@PathVariable Long id) {
        return AjaxResult.ok(sysDeptService.getById(id));
    }

    /**
     * 根据父部门ID获取子部门列表
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    @Operation(summary = "根据父部门ID获取子部门列表", description = "获取指定父部门的直接子部门列表", operationId = "sysDept_childrenByParent")
    @GetMapping("/children/{parentId}")
    public AjaxResult<?> getChildrenByParentId(@PathVariable Long parentId) {
        return AjaxResult.ok(sysDeptService.getChildrenByParentId(parentId));
    }

    /**
     * 新增部门
     * @param sysDept 部门信息
     * @return success/false
     */
    @Operation(summary = "新增部门", description = "创建新部门，父部门为空时默认设为根部门", operationId = "sysDept_create")
    @Logging("新增部门")
    @PostMapping
    public AjaxResult<?> save(@Valid @RequestBody SysDept sysDept) {
        // 如果没有设置父部门ID，默认设置为0（根部门）
        if (sysDept.getParentId() == null) {
            sysDept.setParentId(0L);
        }
        return AjaxResult.ok(sysDeptService.save(sysDept));
    }

    /**
     * 修改部门
     * @param sysDept 部门信息
     * @return success/false
     */
    @Operation(summary = "修改部门", description = "更新部门信息，防止自引用为父部门", operationId = "sysDept_update")
    @Logging("修改部门")
    @PutMapping()
    public AjaxResult<?> update(@Valid @RequestBody SysDept sysDept) {
        // 检查是否将部门设置为自己的子部门
        if (sysDept.getDeptId().equals(sysDept.getParentId())) {
            return AjaxResult.failed("不能将部门设置为自己的父部门");
        }
        return AjaxResult.ok(sysDeptService.updateById(sysDept));
    }

    /**
     * 删除部门
     * @param id 部门ID
     * @return success/false
     */
    @Operation(summary = "删除部门", description = "逻辑删除部门，删除前校验是否仍有子部门", operationId = "sysDept_delete")
    @Logging("删除部门")
    @DeleteMapping("/{id}")
    public AjaxResult<?> removeById(@PathVariable Long id) {
        // 检查是否有子部门
        if (sysDeptService.hasChildren(id)) {
            return AjaxResult.failed("该部门下还有子部门，不能删除");
        }
        
        // 这里可以添加检查是否有用户关联的逻辑
        // 暂时使用逻辑删除
        SysDept dept = new SysDept();
        dept.setDeptId(id);
        dept.setDelFlag("1");
        return AjaxResult.ok(sysDeptService.updateById(dept));
    }

    /**
     * 批量删除部门
     * @param ids 部门ID数组
     * @return success/false
     */
    @Operation(summary = "批量删除部门", description = "对多个部门执行逻辑删除，逐一校验是否仍有子部门", operationId = "sysDept_deleteBatch")
    @Logging("批量删除部门")
    @DeleteMapping
    public AjaxResult<?> removeByIds(@RequestBody Long[] ids) {
        for (Long id : ids) {
            if (sysDeptService.hasChildren(id)) {
                return AjaxResult.failed("部门ID为 " + id + " 的部门下还有子部门，不能删除");
            }
        }
        
        // 批量逻辑删除
        for (Long id : ids) {
            SysDept dept = new SysDept();
            dept.setDeptId(id);
            dept.setDelFlag("1");
            sysDeptService.updateById(dept);
        }
        return AjaxResult.ok();
    }

    /**
     * 获取所有部门列表（不分页）
     * @return 部门列表
     */
    @Operation(summary = "获取所有部门列表", description = "返回全部部门列表（不分页）", operationId = "sysDept_listAll")
    @GetMapping("/list")
    public AjaxResult<?> listAll() {
        return AjaxResult.ok(sysDeptService.list());
    }


    /**
     * 分页查询部门列表（新版本）
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页查询部门列表", description = "新版分页接口，支持更多查询条件", operationId = "sysDept_pageNew")
    @PostMapping("/page")
    public AjaxResult<?> pageNew(@RequestBody DeptQueryDTO queryDTO) {
        PageDomain pageDomain = new PageDomain();
        pageDomain.setPageNo(queryDTO.getPageNo());
        pageDomain.setPageSize(queryDTO.getPageSize());
        return AjaxResult.ok(sysDeptService.pageInfoNew(pageDomain, queryDTO));
    }

    /**
     * 设置部门主管
     * @param deptId 部门ID
     * @param userId 用户ID
     * @return success/false
     */
    @Operation(summary = "设置部门主管", description = "为指定部门设置主管用户", operationId = "sysDept_setManager")
    @Logging("设置部门主管")
    @PostMapping("/{deptId}/manager/{userId}")
    public AjaxResult<?> setDeptManager(@PathVariable Long deptId, @PathVariable Long userId) {
        return AjaxResult.ok(sysDeptService.setDeptManager(deptId, userId));
    }

    /**
     * 移除部门主管
     * @param deptId 部门ID
     * @return success/false
     */
    @Operation(summary = "移除部门主管", description = "移除指定部门的主管用户", operationId = "sysDept_removeManager")
    @Logging("移除部门主管")
    @DeleteMapping("/{deptId}/manager")
    public AjaxResult<?> removeDeptManager(@PathVariable Long deptId) {
        return AjaxResult.ok(sysDeptService.removeDeptManager(deptId));
    }

}
