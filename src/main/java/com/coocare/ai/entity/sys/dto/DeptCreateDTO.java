package com.coocare.ai.entity.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 部门创建DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "部门创建DTO")
public class DeptCreateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "部门名称", required = true)
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 50, message = "部门名称长度不能超过50个字符")
    private String name;

    @Schema(description = "部门编码")
    @Size(max = 50, message = "部门编码长度不能超过50个字符")
    private String deptCode;

    @Schema(description = "父部门ID")
    private Long parentId;

    @Schema(description = "部门类型")
    private String deptType = "NORMAL";

    @Schema(description = "部门主管用户ID")
    private Long managerUserId;

    @Schema(description = "部门联系电话")
    @Size(max = 20, message = "部门联系电话长度不能超过20个字符")
    private String phone;

    @Schema(description = "部门联系邮箱")
    @Email(message = "部门联系邮箱格式不正确")
    @Size(max = 100, message = "部门联系邮箱长度不能超过100个字符")
    private String email;

    @Schema(description = "部门群组邮箱")
    @Email(message = "部门群组邮箱格式不正确")
    @Size(max = 100, message = "部门群组邮箱长度不能超过100个字符")
    private String groupEmail;

    @Schema(description = "部门办公电话")
    @Size(max = 20, message = "部门办公电话长度不能超过20个字符")
    private String officePhone;

    @Schema(description = "部门描述")
    @Size(max = 500, message = "部门描述长度不能超过500个字符")
    private String description;

    @Schema(description = "部门状态")
    private Integer status = 1;

    @Schema(description = "排序号")
    private Integer sortOrder = 0;

    @Schema(description = "关联的AI智能体ID列表")
    private List<Long> agentIds;

    @Schema(description = "默认AI智能体ID")
    private Long defaultAgentId;
}
