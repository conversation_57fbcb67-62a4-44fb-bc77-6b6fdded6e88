package com.coocare.ai.config.constants;


import com.coocare.ai.enums.LanguageEnum;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 通用常量定义
 * 应用范围内使用的基础常量（编码、分页参数、通用标识等）
 *
 * <AUTHOR>
 * @since 2024-04-22
 */

public interface CommonConstants {

    String UNDERLINE = "_";

    /** 默认语言 */
    LanguageEnum DEFAULT_LANG = LanguageEnum.LANGUAGE_ZH_CN;

    /**
     * header 中租户ID
     */
    String TENANT_ID = "TENANT-ID";

    Long TENANT_ID_1 = 1L;

    /**
     * 菜单树根节点
     */
    Long MENU_TREE_ROOT_ID = -1L;

    /**
     * 客户端允许同时在线数量
     */
    String ONLINE_QUANTITY = "online_quantity";

    /**
     * 请求开始时间
     */
    String REQUEST_START_TIME = "REQUEST-START-TIME";

    /**
     * 当前页
     */
    String CURRENT = "current";

    /**
     * size
     */
    String SIZE = "size";

    /**
     * 编码
     */
    String UTF8 = "UTF-8";

    /**
     * 成功标记
     */
    Integer SUCCESS = 0;

    /**
     * 失败标记
     */
    Integer FAIL = 1;

    /**
     * 密码传输是否加密
     */
    String ENC_FLAG = "enc_flag";

    /**
     * 锁定
     */
    String STATUS_LOCK = "9";

    PasswordEncoder ENCODER = new BCryptPasswordEncoder();
}
