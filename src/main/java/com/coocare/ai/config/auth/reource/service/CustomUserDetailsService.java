package com.coocare.ai.config.auth.reource.service;

import cn.hutool.core.util.ArrayUtil;
import com.coocare.ai.config.constants.CommonConstants;
import com.coocare.ai.config.constants.SecurityConstants;
import com.coocare.ai.config.constants.UserDetail;
import com.coocare.ai.entity.sys.SysUser;
import com.coocare.ai.entity.sys.dto.UserInfo;
import org.springframework.core.Ordered;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.*;

/**
/**
 * 自定义用户详情服务 SPI
 * 支持多客户端/多授权类型的用户加载与排序
 *
 * <AUTHOR>
 * @since 2022-06-02
 */

public interface CustomUserDetailsService extends UserDetailsService, Ordered {

    /**
     * 是否支持此客户端校验
     *
     * @param clientId  请求客户端
     * @param grantType 授权类型
     * @return true/false
     */
    default boolean support(String clientId, String grantType) {
        return true;
    }

    /**
     * 排序值 默认取最大的
     *
     * @return 排序值
     */
    @Override
    default int getOrder() {
        return 0;
    }

    /**
     * 构建userdetails
     *
     * @param result 用户信息
     * @return UserDetails
     * @throws UsernameNotFoundException
     */
    default UserDetails getUserDetails(UserInfo result) {
        // @formatter:off
        return Optional.of(result)
                .map(this::convertUserDetails)
                .orElseThrow(() -> new UsernameNotFoundException("用户不存在"));
        // @formatter:on
    }

    /**
     * UserInfo 转 UserDetails
     *
     * @param info
     * @return 返回UserDetails对象
     */
    default UserDetails convertUserDetails(UserInfo info) {
        Set<String> dbAuthsSet = new HashSet<>();
        if (ArrayUtil.isNotEmpty(info.getRoles())) {
            // 获取角色
            Arrays.stream(info.getRoles()).forEach(roleId -> dbAuthsSet.add(SecurityConstants.ROLE + roleId));
            // 获取资源
            if (null != info.getPermissions()) {
                dbAuthsSet.addAll(Arrays.asList(info.getPermissions()));
            }

        }
        Collection<? extends GrantedAuthority> authorities = AuthorityUtils
                .createAuthorityList(dbAuthsSet.toArray(new String[0]));
        SysUser user = info.getSysUser();

        // 构造security用户
        return new UserDetail(user.getUserId(), user.getUsername(), user.getPhone(), user.getAvatar(),
                user.getName(), SecurityConstants.BCRYPT + user.getPassword(), user.getEmail(), true,
                true, true, !CommonConstants.STATUS_LOCK.equals(user.getLockFlag()), authorities);
    }

    /**
     * 通过用户实体查询
     *
     * @param user user
     * @return
     */
    default UserDetails loadUserByUser(UserDetail user) {
        return this.loadUserByUsername(user.getUsername());
    }
}
