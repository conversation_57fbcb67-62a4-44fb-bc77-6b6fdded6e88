package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * AI对话统计表
 * 用于记录每次对话的统计信息，包括对话ID、token使用量、用户信息等
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_chat_statistics")
@Schema(description = "AI对话统计表")
public class AiChatStatistics implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 统计记录ID
     */
    @Schema(description = "统计记录ID")
    @TableId(value = "statistics_id", type = IdType.ASSIGN_ID)
    private Long statisticsId;

    /**
     * 对话ID
     */
    @Schema(description = "对话ID")
    @TableField(value = "conversation_id")
    private String conversationId;

    /**
     * 消息ID
     */
    @Schema(description = "消息ID")
    @TableField(value = "message_id")
    private String messageId;

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID")
    @TableField(value = "agent_id")
    private Long agentId;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称")
    @TableField(value = "agent_name")
    private String agentName;

    /**
     * 输入token数量
     */
    @Schema(description = "输入token数量")
    @TableField(value = "input_tokens")
    private Integer inputTokens;

    /**
     * 输出token数量
     */
    @Schema(description = "输出token数量")
    @TableField(value = "output_tokens")
    private Integer outputTokens;

    /**
     * 总token数量
     */
    @Schema(description = "总token数量")
    @TableField(value = "total_tokens")
    private Integer totalTokens;

    /**
     * 使用人ID
     */
    @Schema(description = "使用人ID")
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 使用人用户名
     */
    @Schema(description = "使用人用户名")
    @TableField(value = "username")
    private String username;

    /**
     * 使用人姓名
     */
    @Schema(description = "使用人姓名")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 对话开始时间
     */
    @Schema(description = "对话开始时间")
    @TableField(value = "conversation_start_time")
    private LocalDateTime conversationStartTime;

    /**
     * 对话结束时间
     */
    @Schema(description = "对话结束时间")
    @TableField(value = "conversation_end_time")
    private LocalDateTime conversationEndTime;

    /**
     * 响应时间（毫秒）
     */
    @Schema(description = "响应时间（毫秒）")
    @TableField(value = "response_time")
    private Long responseTime;

    /**
     * 用户输入内容
     */
    @Schema(description = "用户输入内容")
    @TableField(value = "user_input")
    private String userInput;

    /**
     * AI回复内容
     */
    @Schema(description = "AI回复内容")
    @TableField(value = "ai_response")
    private String aiResponse;

    /**
     * 事件类型
     */
    @Schema(description = "事件类型")
    @TableField(value = "event_type")
    private String eventType;

    /**
     * 状态：SUCCESS-成功，ERROR-错误
     */
    @Schema(description = "状态：SUCCESS-成功，ERROR-错误")
    @TableField(value = "status")
    private String status;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    @TableField(value = "error_message")
    private String errorMessage;

    /**
     * 客户端IP地址
     */
    @Schema(description = "客户端IP地址")
    @TableField(value = "client_ip")
    private String clientIp;

    /**
     * 用户代理信息
     */
    @Schema(description = "用户代理信息")
    @TableField(value = "user_agent")
    private String userAgent;

}
