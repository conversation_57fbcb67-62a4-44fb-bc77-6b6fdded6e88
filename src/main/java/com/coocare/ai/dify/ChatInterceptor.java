package com.coocare.ai.dify;


import com.alibaba.fastjson2.JSONObject;
import com.coocare.ai.service.AiChatStatisticsService;
import io.github.guoshiqiufeng.dify.chat.pipeline.ChatMessagePipelineModel;
import io.github.guoshiqiufeng.dify.core.pipeline.PipelineContext;
import io.github.guoshiqiufeng.dify.core.pipeline.PipelineProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 聊天消息拦截器
 * <p>
 * 该拦截器实现了Dify聊天管道处理接口，用于拦截和处理聊天消息事件。
 * 主要功能是在聊天消息结束时记录对话统计信息，包括消息内容、用户信息、
 * 时间戳等数据，用于后续的数据分析和统计报表生成。
 * </p>
 *
 * <p>
 * 拦截器的工作流程：
 * 1. 通过support方法判断是否为"message_end"事件
 * 2. 如果支持该事件，则调用process方法进行处理
 * 3. 在process方法中调用统计服务记录对话数据
 * 4. 记录处理结果和异常信息到日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025/9/3
 **/
@Slf4j  // 启用SLF4J日志功能
@Component  // 标记为Spring组件，由Spring容器管理
@RequiredArgsConstructor  // Lombok注解，自动生成包含final字段的构造函数
public class ChatInterceptor implements PipelineProcess<ChatMessagePipelineModel> {

    /**
     * AI聊天统计服务
     * <p>
     * 用于记录和统计聊天对话的相关数据，包括消息数量、用户活跃度、
     * 对话时长等统计信息。该服务负责将聊天数据持久化到数据库中。
     * </p>
     */
    private final AiChatStatisticsService aiChatStatisticsService;

    /**
     * 判断当前拦截器是否支持处理指定的管道上下文
     * <p>
     * 该方法用于过滤需要处理的事件类型。只有当聊天消息事件类型为"message_end"时，
     * 才会触发后续的统计记录处理。这样可以确保只在消息完全结束后才进行统计，
     * 避免在消息传输过程中重复记录。
     * </p>
     *
     * @param context 管道上下文，包含聊天消息的完整信息和事件类型
     * @return 是否支持处理该上下文：true表示支持并将调用process方法，false表示跳过处理
     */
    @Override
    public boolean support(PipelineContext<ChatMessagePipelineModel> context) {
        // 只处理消息结束事件，确保消息完整性
        return "message_end".equals(context.getModel().getEvent());
    }

    /**
     * 处理聊天消息管道上下文
     * <p>
     * 当support方法返回true时，该方法会被调用来处理具体的业务逻辑。
     * 主要功能是提取聊天上下文中的相关信息，并调用统计服务进行数据记录。
     * 处理过程包括日志记录、异常处理和结果反馈。
     * </p>
     *
     * <p>
     * 处理流程：
     * 1. 记录完整的上下文信息到日志（INFO级别）
     * 2. 调用统计服务记录聊天数据
     * 3. 根据处理结果记录相应的日志信息
     * 4. 捕获并记录任何处理过程中的异常
     * </p>
     *
     * @param context 管道上下文，包含完整的聊天消息信息、用户数据、时间戳等
     */
    @Override
    public void process(PipelineContext<ChatMessagePipelineModel> context) {
        // 记录完整的上下文信息，便于调试和问题排查
        log.info("ChatInterceptor context:{}", JSONObject.toJSONString(context));

        // 记录对话统计信息到数据库
        try {
            // 调用统计服务处理聊天数据
            boolean success = aiChatStatisticsService.recordChatStatistics(context);

            // 根据处理结果记录相应日志
            if (success) {
                log.debug("对话统计信息记录成功");
            } else {
                log.warn("对话统计信息记录失败");
            }
        } catch (Exception e) {
            // 记录异常信息，确保拦截器异常不影响主流程
            log.error("记录对话统计信息时发生异常", e);
        }
    }
}
