package com.coocare.ai.config.auth.support.handler;

import cn.hutool.core.util.StrUtil;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.service.SysUserService;
import com.coocare.ai.utils.EmptyUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AuthorizationCodeRequestAuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 认证失败处理器
 * 统一将 OAuth2 相关异常转换为 AjaxResult JSON 响应
 *
 * <AUTHOR>
 * @since 2022-06-02
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthenticationFailureEventHandler implements AuthenticationFailureHandler {

    private static final MappingJackson2HttpMessageConverter errorHttpResponseConverter = new MappingJackson2HttpMessageConverter();

    private final SysUserService sysUserService;
    private final RedisTemplate redisTemplate;

    /**
     * Called when an authentication attempt fails.
     *
     * @param request   the request during which the authentication attempt occurred.
     * @param response  the response.
     * @param exception the exception which was thrown to reject the authentication
     *                  request.
     */
    @Override
    @SneakyThrows
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                        AuthenticationException exception) {
        String grantType = request.getParameter(OAuth2ParameterNames.GRANT_TYPE);
        String errMsg = exception.getLocalizedMessage();
        if (exception instanceof OAuth2AuthorizationCodeRequestAuthenticationException) {
            errMsg = ((OAuth2AuthorizationCodeRequestAuthenticationException) exception).getError().getErrorCode();
        } else if (exception instanceof OAuth2AuthenticationException) {
            if (EmptyUtil.isEmpty(errMsg)) {
                final String description = ((OAuth2AuthenticationException) exception).getError().getDescription();
                errMsg = EmptyUtil.isEmpty(description) ? ((OAuth2AuthenticationException) exception).getError().getErrorCode() : description;
            }
        }
        log.info("登录失败，异常：{}", errMsg);

        // 密码模式记录错误信息
//        if (OAuth2ParameterNames.PASSWORD.equals(grantType)) {
//            String username = request.getParameter(OAuth2ParameterNames.USERNAME);
//            if (exception instanceof OAuth2AuthenticationException) {
//                OAuth2AuthenticationException authenticationException = (OAuth2AuthenticationException) exception;
//                String errorCode = authenticationException.getError().getErrorCode();
//                if (!"username_not_found".equals(errorCode)) {
//                    // 密码错误记录错误次数 （TOC用户不记录）
//                    String header = WebUtils.getRequest().getHeader(SecurityConstants.HEADER_TOC);
//                    String clientId = WebUtils.extractClientId(request.getHeader(HttpHeaders.AUTHORIZATION)).orElse(null);
//                    if (!SecurityConstants.HEADER_TOC_YES.equals(header)) {
//                        recordLoginFailureTimes(username, clientId);
//                    }
//                }
//            }
//
//            // 记录登录失败错误信息
//            sendFailureEventLog(request, exception, username);
//        }

        // 写出错误信息
        sendErrorResponse(request, response, exception);
    }

    /**
     * 记录失败日志
     *
     * @param request   HttpServletRequest
     * @param exception 错误日志
     * @param username  用户名
     */
    private void sendFailureEventLog(HttpServletRequest request, AuthenticationException exception, String username) {

    }

    /**
     * 记录登录失败此处，如果操作阈值 调用接口锁定用户
     *
     * @param username username
     */
//    private void recordLoginFailureTimes(String username, String clientId) {
//        redisTemplate.setValueSerializer(new StringRedisSerializer());
//        String key = String.format("%s:%s:%s", CacheConstants.LOGIN_ERROR_TIMES, clientId, username);
//        String timeStr = (String) redisTemplate.opsForValue().get(key);
//        if (EmptyUtil.isEmpty(timeStr)) {
//            return;
//        }
//        Long deltaTimes = 6L;
//        Long times = Long.valueOf(timeStr);
//        long leaveTimes = deltaTimes - times;
//        if (leaveTimes <= 0) {
//            switch (ClientEnum.of(clientId)) {
//                case GENIUS:
//                case AREA_RESOURCE:
//                case AREA_OPERATION:
//                case ADVISER_TEAM:
//                    sysUserService.lockUser(username, ClientEnum.of(clientId));
//                    break;
//            }
//            // 1小时后解锁
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("username", username);
//            jsonObject.put("clientId", clientId);
//            MqMessage mqMessage = new MqMessage();
//            mqMessage.setTag(MqConstant.USER_UNLOCK);
//            mqMessage.setMessage(jsonObject.toJSONString());
//            // 1小时 毫秒
//            mqMessage.setDelay(3600000L);
//            mqUtils.sendDelayMessage(mqMessage);
//        }
//    }

    private void sendErrorResponse(HttpServletRequest request, HttpServletResponse response,
                                   AuthenticationException exception) throws IOException {
        ServletServerHttpResponse httpResponse = new ServletServerHttpResponse(response);
        httpResponse.setStatusCode(HttpStatus.OK);
        String errorMessage;

        if (exception instanceof OAuth2AuthenticationException) {
            OAuth2AuthenticationException authorizationException = (OAuth2AuthenticationException) exception;
            errorMessage = StrUtil.isBlank(authorizationException.getError().getDescription())
                    ? authorizationException.getError().getErrorCode()
                    : authorizationException.getError().getDescription();

            this.errorHttpResponseConverter.write(AjaxResult.failed(authorizationException.getError().getErrorCode(), errorMessage), MediaType.APPLICATION_JSON, httpResponse);

        } else {
            errorMessage = exception.getLocalizedMessage();
            this.errorHttpResponseConverter.write(AjaxResult.failed(errorMessage), MediaType.APPLICATION_JSON, httpResponse);
        }
    }

}
