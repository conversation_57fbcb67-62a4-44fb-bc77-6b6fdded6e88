package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.AiDocumentCategory;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;

import java.util.List;

/**
 * <p>
 * AI文档分类 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface AiDocumentCategoryService extends IService<AiDocumentCategory> {

    /**
     * 分页查询分类列表
     *
     * @param pageDomain   分页参数
     * @param categoryName 分类名称（模糊查询）
     * @param level        分类层级
     * @param parentId     父级ID
     * @return 分页结果
     */
    PageUtils pageInfo(PageDomain pageDomain, String categoryName, Integer level, Long parentId);

    /**
     * 根据父级ID查询子分类列表
     *
     * @param parentId 父级ID，为null时查询顶级分类
     * @return 子分类列表
     */
    List<AiDocumentCategory> getChildrenByParentId(Long parentId);

    /**
     * 获取分类树结构
     *
     * @param parentId 父级ID，为null时查询完整树结构
     * @return 分类树列表
     */
    List<AiDocumentCategory> getCategoryTree(Long parentId);

    /**
     * 创建产品型号分类（第一层）
     *
     * @param category 分类信息
     * @return 创建结果
     */
    boolean createProductModel(AiDocumentCategory category);

    /**
     * 创建产品系列分类（第二层）
     * 创建成功后会自动生成第三层的文档类型分类
     *
     * @param category 分类信息
     * @return 创建结果
     */
    boolean createProductSeries(AiDocumentCategory category);

    /**
     * 为产品系列自动创建第三层文档类型分类
     *
     * @param productSeriesId 产品系列ID
     * @return 创建结果
     */
    boolean createDefaultDocumentTypes(Long productSeriesId);

    /**
     * 更新分类信息
     *
     * @param category 分类信息
     * @return 更新结果
     */
    boolean updateCategory(AiDocumentCategory category);

    /**
     * 删除分类（逻辑删除）
     * 如果存在子分类，则不允许删除
     *
     * @param categoryId 分类ID
     * @return 删除结果
     */
    boolean deleteCategory(Long categoryId);

    /**
     * 批量删除分类
     *
     * @param categoryIds 分类ID列表
     * @return 删除结果
     */
    boolean deleteBatch(List<Long> categoryIds);

    /**
     * 检查分类名称是否存在
     *
     * @param categoryName 分类名称
     * @param parentId     父级ID
     * @param categoryId   分类ID（更新时排除自己）
     * @return 是否存在
     */
    boolean checkCategoryNameExists(String categoryName, Long parentId, Long categoryId);

    /**
     * 检查分类是否存在子分类
     *
     * @param categoryId 分类ID
     * @return 是否存在子分类
     */
    boolean hasChildren(Long categoryId);

    /**
     * 启用/禁用分类
     *
     * @param categoryId 分类ID
     * @param status     状态：0-禁用，1-启用
     * @return 操作结果
     */
    boolean updateStatus(Long categoryId, Boolean status);

    /**
     * 更新分类排序
     *
     * @param categoryId 分类ID
     * @param sortOrder  排序号
     * @return 操作结果
     */
    boolean updateSortOrder(Long categoryId, Integer sortOrder);

    /**
     * 根据分类ID获取完整路径
     *
     * @param categoryId 分类ID
     * @return 分类路径（用 > 分隔）
     */
    String getCategoryPath(Long categoryId);

    /**
     * 获取分类的所有祖先节点
     *
     * @param categoryId 分类ID
     * @return 祖先节点列表（从根节点到父节点）
     */
    List<AiDocumentCategory> getAncestors(Long categoryId);

    /**
     * 获取分类的所有后代节点
     *
     * @param categoryId 分类ID
     * @return 后代节点列表
     */
    List<AiDocumentCategory> getDescendants(Long categoryId);
}
