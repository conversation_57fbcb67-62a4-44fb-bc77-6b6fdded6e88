# 安全基线与威胁模型（简）

## 范畴
- 平台：后端服务、数据库、Redis、对象存储、外部 Dify 服务
- 访问：外网/内网、机器对机器（M2M）、用户对服务（U2S）

## 主要威胁（STRIDE）
- S（伪装）：令牌被盗用 -> 强制 HTTPS，Token 存储安全，最小权限
- T（篡改）：请求参数篡改 -> 签名/鉴权、参数校验
- R（抵赖）：关键操作留存审计日志
- I（信息泄露）：敏感信息脱敏/最小化日志
- D（拒绝服务）：限流、峰值保护、缓存
- E（权限提升）：细粒度授权、白名单化开放接口

## 安全基线
- 传输安全：HTTPS、HSTS、安全 Header（X-Content-Type-Options, CSP）
- 身份鉴别：OAuth2、Token 续签策略；内网接口使用 Inner 保护
- 访问控制：最小权限，角色/菜单/数据权限分层
- 配置安全：机密配置走环境变量/密钥管理，定期轮换
- 依赖安全：定期依赖扫描与升级
- 运维安全：最小权限账号、操作审计、容器镜像签名

## 处置与响应
- 安全事件分级与响应流程
- 关键告警（鉴权失败、异常峰值、对象存储失败率）
- 应急开关与降级策略

