package com.coocare.ai.controller.sys;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocare.ai.config.constants.CacheConstants;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.plugins.logging.annotation.Logging;
import com.coocare.ai.entity.sys.SysRole;
import com.coocare.ai.service.SysMenuService;
import com.coocare.ai.service.SysRoleService;
import com.coocare.ai.utils.PageDomain;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色管理控制器
 * 提供角色分页、CRUD、状态切换、分配菜单等接口
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Tag(name = "角色管理")
@RestController
@RequestMapping("/sys/role")
@RequiredArgsConstructor
public class SysRoleController {

    private final SysRoleService sysRoleService;
    private final SysMenuService sysMenuService;

    /**
     * 分页查询角色信息
     * @param pageDomain 分页对象
     * @param name 查询条件
     * @return 分页对象
     */
    @Operation(summary = "分页查询角色列表", description = "根据角色名称分页查询角色信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("page")
    public AjaxResult<?> pageInfo(
            @Parameter(description = "分页参数") PageDomain pageDomain,
            @Parameter(description = "角色名称，支持模糊搜索") String name) {
        return AjaxResult.success(sysRoleService.pageInfo(pageDomain, name));
    }

    /**
     * 添加角色
     * @param sysRole 角色信息
     * @return success、false
     */
    @Operation(summary = "新增角色", description = "创建新的系统角色")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "新增成功"),
        @ApiResponse(responseCode = "400", description = "参数校验失败"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @Logging("添加角色")
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('sys_role_add')")
    @CacheEvict(value = CacheConstants.ROLE_DETAILS, allEntries = true)
    public AjaxResult<?> save(
            @Parameter(description = "角色信息", required = true) @Valid @RequestBody SysRole sysRole) {
        return AjaxResult.success(sysRoleService.save(sysRole));
    }

    /**
     * 修改角色
     * @param sysRole 角色信息
     * @return success/false
     */
    @Logging("修改角色")
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('sys_role_edit')")
    @CacheEvict(value = CacheConstants.ROLE_DETAILS, allEntries = true)
    public AjaxResult<?> update(@RequestBody SysRole sysRole) {
        return AjaxResult.success(sysRoleService.updateById(sysRole));
    }

    /**
     * 删除角色
     * @param ids
     * @return
     */
    @Logging("删除角色")
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('sys_role_del')")
    @CacheEvict(value = CacheConstants.ROLE_DETAILS, allEntries = true)
    public AjaxResult<?> removeById(@RequestBody Long[] ids) {
        return AjaxResult.ok(sysRoleService.removeRoleByIds(ids));
    }

    /**
     * 通过ID查询角色信息
     * @param id ID
     * @return 角色信息
     */
    @Schema(description = "删除")
    @DeleteMapping("/details/{id}")
    public AjaxResult<?> del(@PathVariable("id") Long id) {
        return AjaxResult.success(sysRoleService.delById(id));
    }

    @Schema(description = "状态切换")
    @PutMapping("/change/{id}/{status}")
    public AjaxResult<?> change(@PathVariable("id") Long id, @PathVariable("status") Boolean status) {
        return AjaxResult.success(sysRoleService.change(id, status));
    }

    @Schema(description = "角色分配菜单")
    @PutMapping("/assignMenu")
    public AjaxResult<?> assignMenu(Long roleId, List<Long> menuIds) {
        return AjaxResult.success(sysRoleService.assignMenu(roleId, menuIds));
    }

    /**
     * 获取角色列表
     * @return 角色列表
     */
    @GetMapping("/list")
    public AjaxResult<?> listRoles() {
        return AjaxResult.ok(sysRoleService.list(Wrappers.emptyWrapper()));
    }

    /**
     * 通过角色ID 查询角色列表
     * @param roleIdList 角色ID
     * @return
     */
    @PostMapping("/getRoleList")
    public AjaxResult<?> getRoleList(@RequestBody List<Long> roleIdList) {
        return AjaxResult.ok(sysRoleService.findRolesByRoleIds(roleIdList, CollUtil.join(roleIdList, StrUtil.UNDERLINE)));
    }

}
