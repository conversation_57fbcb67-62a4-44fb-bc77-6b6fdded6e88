package com.coocare.ai.controller.ai;

import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.dto.AiDocumentQueryDTO;
import com.coocare.ai.entity.dto.AiDocumentUploadDTO;
import com.coocare.ai.entity.vo.AiDocumentGroupVO;
import com.coocare.ai.entity.vo.AiDocumentVO;
import com.coocare.ai.service.AiDocumentService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import io.github.guoshiqiufeng.dify.dataset.enums.document.DocActionEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * AI文档管理控制器
 * 提供文档分页、上传、查询、下载、状态更新与删除等 REST 接口
 * 与 AiDocumentService 协作实现业务
 *
 * 安全与幂等：
 * - 对外接口遵循统一异常返回；上传与状态更新需结合权限控制
 * - 下载与查看会触发计数更新
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Tag(name = "AI文档管理")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/document")
public class AiDocumentController {

    private final AiDocumentService documentService;

    /**
     * 分页查询文档列表
     */
    @Operation(summary = "分页查询文档列表", description = "根据查询条件分页获取文档列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/page")
    public AjaxResult<PageUtils> page(AiDocumentQueryDTO queryDTO) {
        try {
            PageDomain pageDomain = new PageDomain();
            pageDomain.setPageNo(queryDTO.getPageNo());
            pageDomain.setPageSize(queryDTO.getPageSize());
            
            PageUtils pageUtils = documentService.pageInfo(pageDomain, queryDTO);
            return AjaxResult.ok(pageUtils);
        } catch (Exception e) {
            log.error("分页查询文档列表失败", e);
            return AjaxResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 上传文档
     */
    @Operation(summary = "上传文档", description = "上传文档到指定的第三层分类")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "上传成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/upload")
    public AjaxResult<AiDocumentVO> upload(
            @Parameter(description = "上传的文件", required = true) @RequestParam("file") MultipartFile file,
            @Valid AiDocumentUploadDTO uploadDTO) {
        try {
            AiDocumentVO result = documentService.uploadDocument(file, uploadDTO);
            return AjaxResult.ok(result);
        } catch (IllegalArgumentException e) {
            return AjaxResult.failed(e.getMessage());
        } catch (Exception e) {
            log.error("上传文档失败", e);
            return AjaxResult.failed("上传失败: " + e.getMessage());
        }
    }

    /**
     * 根据产品系列ID查询文档并按第三层分类分组
     */
    @Operation(summary = "根据产品系列查询分组文档", description = "根据产品系列ID查询文档并按第三层分类分组展示")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/product-series/{productSeriesId}/grouped")
    public AjaxResult<List<AiDocumentGroupVO>> getDocumentsByProductSeriesGrouped(
            @Parameter(description = "产品系列ID", required = true) @PathVariable Long productSeriesId) {
        try {
            List<AiDocumentGroupVO> result = documentService.getDocumentsByProductSeriesGrouped(productSeriesId);
            return AjaxResult.ok(result);
        } catch (Exception e) {
            log.error("查询分组文档失败", e);
            return AjaxResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据产品系列ID和分类编码查询文档
     */
    @Operation(summary = "根据产品系列和分类查询文档", description = "根据产品系列ID和分类编码查询文档列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/product-series/{productSeriesId}/category/{categoryCode}")
    public AjaxResult<List<AiDocumentVO>> getDocumentsByProductSeriesAndCategory(
            @Parameter(description = "产品系列ID", required = true) @PathVariable Long productSeriesId,
            @Parameter(description = "分类编码", required = true) @PathVariable String categoryCode) {
        try {
            List<AiDocumentVO> result = documentService.getDocumentsByProductSeriesAndCategory(productSeriesId, categoryCode);
            return AjaxResult.ok(result);
        } catch (Exception e) {
            log.error("查询文档失败", e);
            return AjaxResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据分类ID查询文档
     */
    @Operation(summary = "根据分类ID查询文档", description = "根据分类ID查询该分类下的所有文档")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/category/{categoryId}")
    public AjaxResult<List<AiDocumentVO>> getDocumentsByCategoryId(
            @Parameter(description = "分类ID", required = true) @PathVariable Long categoryId) {
        try {
            List<AiDocumentVO> result = documentService.getDocumentsByCategoryId(categoryId);
            return AjaxResult.ok(result);
        } catch (Exception e) {
            log.error("查询文档失败", e);
            return AjaxResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据产品型号ID查询所有文档
     */
    @Operation(summary = "根据产品型号查询文档", description = "根据产品型号ID查询该型号下的所有文档")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/product-model/{productModelId}")
    public AjaxResult<List<AiDocumentVO>> getDocumentsByProductModelId(
            @Parameter(description = "产品型号ID", required = true) @PathVariable Long productModelId) {
        try {
            List<AiDocumentVO> result = documentService.getDocumentsByProductModelId(productModelId);
            return AjaxResult.ok(result);
        } catch (Exception e) {
            log.error("查询文档失败", e);
            return AjaxResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询文档详情
     */
    @Operation(summary = "根据ID查询文档详情", description = "根据文档ID获取文档的详细信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "文档不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}")
    public AjaxResult<AiDocumentVO> getById(
            @Parameter(description = "文档ID", required = true) @PathVariable Long id) {
        try {
            AiDocumentVO document = documentService.getDocumentDetail(id);
            if (document == null) {
                return AjaxResult.failed("文档不存在");
            }
            // 增加查看次数
            documentService.incrementViewCount(id);
            return AjaxResult.ok(document);
        } catch (Exception e) {
            log.error("查询文档详情失败", e);
            return AjaxResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新文档信息
     */
    @Operation(summary = "更新文档信息", description = "更新文档的基本信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "404", description = "文档不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/{id}")
    public AjaxResult<String> update(
            @Parameter(description = "文档ID", required = true) @PathVariable Long id,
            @Valid @RequestBody AiDocumentUploadDTO uploadDTO) {
        try {
            boolean result = documentService.updateDocument(id, uploadDTO);
            return result ? AjaxResult.ok("更新成功") : AjaxResult.failed("更新失败");
        } catch (IllegalArgumentException e) {
            return AjaxResult.failed(e.getMessage());
        } catch (Exception e) {
            log.error("更新文档信息失败", e);
            return AjaxResult.failed("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除文档
     */
    @Operation(summary = "删除文档", description = "删除指定文档（逻辑删除）")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "404", description = "文档不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("{datasetId}/{id}")
    public AjaxResult<String> delete(
            @Parameter(description = "数据集ID", required = true) @PathVariable Long datasetId,
            @Parameter(description = "文档ID", required = true) @PathVariable Long id) {
        try {
            boolean result = documentService.deleteDocument(id, datasetId);
            return result ? AjaxResult.ok("删除成功") : AjaxResult.failed("删除失败");
        } catch (Exception e) {
            log.error("删除文档失败", e);
            return AjaxResult.failed("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除文档
     */
    @Operation(summary = "批量删除文档", description = "批量删除指定的文档")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @DeleteMapping("{datasetId}/batch")
    public AjaxResult<String> deleteBatch(
            @Parameter(description = "数据集ID", required = true) @PathVariable Long datasetId,
            @RequestBody List<Long> documentIds) {
        try {
            boolean result = documentService.deleteBatch(documentIds, datasetId);
            return result ? AjaxResult.ok("删除成功") : AjaxResult.failed("删除失败");
        } catch (Exception e) {
            log.error("批量删除文档失败", e);
            return AjaxResult.failed("删除失败: " + e.getMessage());
        }
    }

    /**
     * 更新文档状态
     */
    @Operation(summary = "更新文档状态", description = "更新文档的启用状态")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "操作成功"),
        @ApiResponse(responseCode = "404", description = "文档不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/{datasetId}/{id}/status/{status}")
    public AjaxResult<String> updateStatus(
            @Parameter(description = "数据集ID", required = true) @PathVariable Long datasetId,
            @Parameter(description = "文档ID", required = true) @PathVariable Long id,
            @Parameter(description = "状态：0-禁用，1-启用，2-审核中", required = true) @PathVariable DocActionEnum status) {
        try {
            boolean result = documentService.updateStatus(id, datasetId, status);
            return result ? AjaxResult.ok("操作成功") : AjaxResult.failed("操作失败");
        } catch (Exception e) {
            log.error("更新文档状态失败", e);
            return AjaxResult.failed("操作失败: " + e.getMessage());
        }
    }

    /**
     * 下载文档
     */
    @Operation(summary = "下载文档", description = "下载指定文档，会增加下载次数")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "下载成功"),
        @ApiResponse(responseCode = "404", description = "文档不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}/download")
    public AjaxResult<AiDocumentVO> download(
            @Parameter(description = "文档ID", required = true) @PathVariable Long id) {
        try {
            AiDocumentVO document = documentService.downloadDocument(id);
            if (document == null) {
                return AjaxResult.failed("文档不存在");
            }
            return AjaxResult.ok(document);
        } catch (Exception e) {
            log.error("下载文档失败", e);
            return AjaxResult.failed("下载失败: " + e.getMessage());
        }
    }

    /**
     * 获取文档分组统计信息
     */
    @Operation(summary = "获取文档分组统计", description = "获取产品系列下各分类的文档统计信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/product-series/{productSeriesId}/stats")
    public AjaxResult<List<AiDocumentGroupVO>> getDocumentGroupStats(
            @Parameter(description = "产品系列ID", required = true) @PathVariable Long productSeriesId) {
        try {
            List<AiDocumentGroupVO> result = documentService.getDocumentGroupStats(productSeriesId);
            return AjaxResult.ok(result);
        } catch (Exception e) {
            log.error("查询文档统计信息失败", e);
            return AjaxResult.failed("查询失败: " + e.getMessage());
        }
    }
}
