package com.coocare.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocare.ai.entity.AiDocumentCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * AI文档分类 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Mapper
public interface AiDocumentCategoryMapper extends BaseMapper<AiDocumentCategory> {

    /**
     * 根据父级ID查询子分类列表
     *
     * @param parentId 父级ID
     * @return 子分类列表
     */
    List<AiDocumentCategory> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 查询分类树结构
     *
     * @param parentId 父级ID，为null时查询所有
     * @return 分类树列表
     */
    List<AiDocumentCategory> selectCategoryTree(@Param("parentId") Long parentId);

    /**
     * 根据分类名称和父级ID查询分类
     *
     * @param categoryName 分类名称
     * @param parentId     父级ID
     * @return 分类信息
     */
    AiDocumentCategory selectByNameAndParentId(@Param("categoryName") String categoryName, 
                                               @Param("parentId") Long parentId);

    /**
     * 查询某个分类下是否存在子分类
     *
     * @param parentId 父级ID
     * @return 子分类数量
     */
    int countByParentId(@Param("parentId") Long parentId);

    /**
     * 批量插入分类
     *
     * @param categories 分类列表
     * @return 插入数量
     */
    int batchInsert(@Param("categories") List<AiDocumentCategory> categories);
}
