package com.coocare.ai.listener;

import com.coocare.ai.config.GlobalConfigManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 配置更新监听器
 * 监听配置变更事件，自动更新全局配置缓存
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConfigUpdateListener {

    private final GlobalConfigManager globalConfigManager;

    /**
     * 监听配置更新事件
     * 当配置发生变化时，刷新全局配置缓存
     */
    public void handleConfigUpdate(String configKey, String configValue) {
        try {
            log.info("接收到配置更新事件: key={}, value={}", configKey, configValue);
            
            // 更新全局配置缓存
            globalConfigManager.updateConfig(configKey, configValue);
            
            log.debug("全局配置缓存已更新: key={}", configKey);
            
        } catch (Exception e) {
            log.error("处理配置更新事件失败: key={}", configKey, e);
        }
    }

    /**
     * 监听配置删除事件
     */
    public void handleConfigDelete(String configKey) {
        try {
            log.info("接收到配置删除事件: key={}", configKey);
            
            // 从全局配置缓存中移除
            globalConfigManager.removeConfig(configKey);
            
            log.debug("全局配置缓存已移除: key={}", configKey);
            
        } catch (Exception e) {
            log.error("处理配置删除事件失败: key={}", configKey, e);
        }
    }

    /**
     * 监听系统初始化完成事件
     * 重新加载所有配置
     */
    public void handleSystemInitComplete() {
        try {
            log.info("接收到系统初始化完成事件，重新加载全局配置");
            
            // 重新加载所有配置
            globalConfigManager.loadAllConfigs();
            
            log.info("系统初始化完成后配置重新加载完成，当前缓存项数: {}", 
                globalConfigManager.getCacheSize());
            
        } catch (Exception e) {
            log.error("处理系统初始化完成事件失败", e);
        }
    }
}
