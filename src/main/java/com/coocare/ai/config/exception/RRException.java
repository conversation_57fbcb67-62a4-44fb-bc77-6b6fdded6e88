package com.coocare.ai.config.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class RRException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private String msg;
    private int code = 500;
    private Object[] args;

    public RRException(String msg) {
        super(msg);
        this.msg = msg;
    }

    public RRException(String msg, Throwable e) {
        super(msg, e);
        this.msg = msg;
    }

    public RRException(String msg, int code) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }

    public RRException(String msg, int code, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
    }

    public RRException(String msg, Object... args) {
        super(msg);
        this.msg = msg;
        this.args = args;
    }

    public RRException(String msg, Object[] args, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.args = args;
    }

    public RRException(String msg, int code, Object[] args) {
        super(msg);
        this.msg = msg;
        this.code = code;
        this.args = args;
    }

    public RRException(String msg, int code, Object[] args, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
        this.args = args;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setArgs(Object[] args) {
        this.args = args;
    }
}
