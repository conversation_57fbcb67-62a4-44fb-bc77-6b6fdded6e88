package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.AiChatStatistics;
import io.github.guoshiqiufeng.dify.chat.pipeline.ChatMessagePipelineModel;
import io.github.guoshiqiufeng.dify.core.pipeline.PipelineContext;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * AI对话统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public interface AiChatStatisticsService extends IService<AiChatStatistics> {

    /**
     * 记录对话统计信息
     *
     * @param context 管道上下文
     * @return 是否保存成功
     */
    boolean recordChatStatistics(PipelineContext<ChatMessagePipelineModel> context);

    /**
     * 手动记录对话统计信息
     *
     * @param conversationId 对话ID
     * @param messageId      消息ID
     * @param agentId        智能体ID
     * @param agentName      智能体名称
     * @param inputTokens    输入token数量
     * @param outputTokens   输出token数量
     * @param totalTokens    总token数量
     * @param userId         用户ID
     * @param username       用户名
     * @return 是否保存成功
     */
    boolean recordChatStatistics(String conversationId, String messageId, Long agentId, String agentName,
                                 Integer inputTokens, Integer outputTokens, Integer totalTokens,
                                 Long userId, String username);

    /**
     * 根据时间范围获取token使用统计
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计信息
     */
    Map<String, Object> getTokenStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据智能体ID获取使用统计
     *
     * @param agentId   智能体ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计信息
     */
    Map<String, Object> getAgentStatistics(Long agentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据用户ID获取使用统计
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计信息
     */
    Map<String, Object> getUserStatistics(Long userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取热门智能体排行榜
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 智能体排行榜
     */
    List<Map<String, Object>> getPopularAgents(LocalDateTime startTime, LocalDateTime endTime, Integer limit);

    /**
     * 获取活跃用户排行榜
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 用户排行榜
     */
    List<Map<String, Object>> getActiveUsers(LocalDateTime startTime, LocalDateTime endTime, Integer limit);
}
