package com.coocare.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocare.ai.entity.AiAgent;
import com.coocare.ai.entity.AiDeptAgent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部门与AI智能体关联 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Mapper
public interface AiDeptAgentMapper extends BaseMapper<AiDeptAgent> {

    /**
     * 根据部门ID查询关联的AI智能体列表
     *
     * @param deptId 部门ID
     * @return AI智能体列表
     */
    List<AiAgent> selectAgentsByDeptId(@Param("deptId") Long deptId);

    /**
     * 根据部门ID查询默认AI智能体
     *
     * @param deptId 部门ID
     * @return 默认AI智能体
     */
    AiAgent selectDefaultAgentByDeptId(@Param("deptId") Long deptId);

    /**
     * 根据智能体ID查询关联的部门ID列表
     *
     * @param agentId 智能体ID
     * @return 部门ID列表
     */
    List<Long> selectDeptIdsByAgentId(@Param("agentId") Long agentId);

    /**
     * 删除部门的所有AI智能体关联
     *
     * @param deptId 部门ID
     * @return 影响行数
     */
    int deleteByDeptId(@Param("deptId") Long deptId);

    /**
     * 批量插入部门AI智能体关联
     *
     * @param deptAgents 关联列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<AiDeptAgent> deptAgents);
}