package com.coocare.ai.config.auth.support.core;

import com.coocare.ai.config.auth.support.handler.AuthenticationSuccessEventHandler;
import com.coocare.ai.config.auth.support.handler.FormAuthenticationFailureHandler;
import com.coocare.ai.config.auth.support.handler.SsoLogoutSuccessHandler;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;

/**
 * 表单登录 HttpConfigurer
 * 抽取 spring security 的表单登录配置（登录页/成功与失败处理/登出处理/CSRF）
 *
 * <AUTHOR>
 * @since 2022-06-04
 */
public final class FormIdentityLoginConfigurer
        extends AbstractHttpConfigurer<FormIdentityLoginConfigurer, HttpSecurity> {

    @Override
    public void init(HttpSecurity http) throws Exception {
        http.formLogin(formLogin -> {
                    formLogin.loginPage("/login");
                    formLogin.failureHandler(new FormAuthenticationFailureHandler());
                    formLogin.successHandler(new AuthenticationSuccessEventHandler());
                })
                .logout() // SSO登出成功处理
                .logoutSuccessHandler(new SsoLogoutSuccessHandler())
                .deleteCookies("JSESSIONID")
                .invalidateHttpSession(true)
                .and()
                .csrf()
                .disable();
    }

}
