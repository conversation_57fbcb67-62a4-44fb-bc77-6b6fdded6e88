package com.coocare.ai.controller;


import com.coocare.ai.config.GlobalConfigManager;
import com.coocare.ai.config.constants.ConfigConstants;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.oss.OssProperties;
import com.coocare.ai.config.oss.OssTemplate;
import io.github.guoshiqiufeng.dify.client.spring6.builder.DifyServerBuilder;
import io.github.guoshiqiufeng.dify.core.config.DifyProperties;
import io.github.guoshiqiufeng.dify.server.DifyServer;
import io.github.guoshiqiufeng.dify.server.client.DifyServerTokenDefault;
import io.github.guoshiqiufeng.dify.server.dto.response.ApiKeyResponse;
import io.github.guoshiqiufeng.dify.server.dto.response.AppsResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;

/**
 * 智能体后端服务控制器
 * 提供与 Dify 后端服务的交互接口（应用列表、API Key 初始化）、以及对象存储示例接口
 *
 * <AUTHOR>
 * @since 2025-08-19
 */

@Tag(name = "智能体后端服务控制器")
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/sys/backend")
public class ServerController {

    private final GlobalConfigManager globalConfigManager;

    /**
     * 获取所有应用
     * 初始化应用API密钥
     * 获取API秘钥
     *
     */

//    private final DifyChat difyChat;
    private final OssProperties ossProperties;
    private final OssTemplate ossTemplate;

    @Operation(summary = "文件上传接口", description = "用于上传文件到服务器")
    @GetMapping("fileUpload")
    public AjaxResult<?> fileUpload(){

        return AjaxResult.ok(ossTemplate.getObject(ossProperties.getBucketName(), "123.jpeg"));
    }


    @Operation(summary = "文件上传接口", description = "用于上传文件到服务器")
    @GetMapping("apps")
    public AjaxResult<?> apps(){

        DifyServer difyServer = DifyServerBuilder.create(DifyServerBuilder.DifyServerClientBuilder
                .builder()
                .baseUrl(globalConfigManager.getConfig(ConfigConstants.DIFY_SERVICE_URL))
                .serverProperties(new DifyProperties.Server("<EMAIL>", "abcd@1234"))
                .serverToken(new DifyServerTokenDefault())
                .clientConfig(new DifyProperties.ClientConfig())
                .restClientBuilder(RestClient.builder())
                .webClientBuilder(WebClient.builder())
                .build());

        List<AppsResponse> appList = difyServer.apps(null, null);

        appList.forEach(app -> {
            log.info("app: {}", app);
            List<ApiKeyResponse> apiKeys = difyServer.getAppApiKey(app.getId());
            log.info("apiKeys: {}", apiKeys);
        });
        return AjaxResult.ok(appList);
    }

}
