# AI对话统计功能

## 功能概述

AI对话统计功能用于记录和分析每次AI对话的详细信息，包括token使用量、用户信息、智能体信息、响应时间等关键指标。

## 功能特性

### 1. 自动统计记录
- 在`ChatInterceptor`中自动拦截每次对话的`message_end`事件
- 自动提取对话ID、token数量、用户信息、智能体信息等
- 记录对话开始/结束时间、响应时间、客户端IP等详细信息

### 2. 数据统计分析
- Token使用量统计（总量、输入、输出）
- 智能体使用情况分析
- 用户活跃度分析
- 热门智能体排行榜
- 活跃用户排行榜

### 3. RESTful API接口
提供完整的统计查询接口，支持按时间范围、用户、智能体等维度查询。

## 数据库表结构

### ai_chat_statistics 表字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| statistics_id | BIGINT | 统计记录ID（主键） |
| conversation_id | VARCHAR(255) | 对话ID |
| message_id | VARCHAR(255) | 消息ID |
| agent_id | BIGINT | 智能体ID |
| agent_name | VARCHAR(255) | 智能体名称 |
| token_count | INT | 使用的token数量 |
| input_tokens | INT | 输入token数量 |
| output_tokens | INT | 输出token数量 |
| total_tokens | INT | 总token数量 |
| user_id | BIGINT | 使用人ID |
| username | VARCHAR(255) | 使用人用户名 |
| user_name | VARCHAR(255) | 使用人姓名 |
| conversation_start_time | DATETIME | 对话开始时间 |
| conversation_end_time | DATETIME | 对话结束时间 |
| response_time | BIGINT | 响应时间（毫秒） |
| user_input | TEXT | 用户输入内容 |
| ai_response | TEXT | AI回复内容 |
| event_type | VARCHAR(100) | 事件类型 |
| status | VARCHAR(50) | 状态（SUCCESS/ERROR） |
| error_message | TEXT | 错误信息 |
| client_ip | VARCHAR(100) | 客户端IP地址 |
| user_agent | VARCHAR(500) | 用户代理信息 |
| create_by | VARCHAR(255) | 创建人 |
| create_time | DATETIME | 创建时间 |
| update_by | VARCHAR(255) | 更新人 |
| update_time | DATETIME | 更新时间 |
| del_flag | CHAR(1) | 删除标志 |

## API接口说明

### 1. Token使用统计
```
GET /ai/chat/statistics/token?startTime=2025-09-01 00:00:00&endTime=2025-09-03 23:59:59
```

### 2. 智能体使用统计
```
GET /ai/chat/statistics/agent/{agentId}?startTime=2025-09-01 00:00:00&endTime=2025-09-03 23:59:59
```

### 3. 用户使用统计
```
GET /ai/chat/statistics/user/{userId}?startTime=2025-09-01 00:00:00&endTime=2025-09-03 23:59:59
```

### 4. 热门智能体排行榜
```
GET /ai/chat/statistics/popular-agents?startTime=2025-09-01 00:00:00&endTime=2025-09-03 23:59:59&limit=10
```

### 5. 活跃用户排行榜
```
GET /ai/chat/statistics/active-users?startTime=2025-09-01 00:00:00&endTime=2025-09-03 23:59:59&limit=10
```

### 6. 手动记录统计（测试用）
```
POST /ai/chat/statistics/record
```

## 部署说明

### 1. 数据库初始化
执行 `db/ai_chat_statistics.sql` 文件创建统计表：
```sql
mysql -u username -p database_name < db/ai_chat_statistics.sql
```

### 2. 配置说明
无需额外配置，统计功能会自动启用。

### 3. 监控建议
- 定期清理历史统计数据（建议保留3-6个月）
- 监控统计表大小，必要时进行分表
- 为高频查询字段添加合适的索引

## 使用示例

### 查询今日Token使用情况
```bash
curl -X GET "http://localhost:8070/ai/chat/statistics/token?startTime=2025-09-03 00:00:00&endTime=2025-09-03 23:59:59"
```

### 查询智能体使用排行榜
```bash
curl -X GET "http://localhost:8070/ai/chat/statistics/popular-agents?startTime=2025-09-01 00:00:00&endTime=2025-09-03 23:59:59&limit=5"
```

## 注意事项

1. **性能考虑**：统计记录是异步进行的，不会影响对话响应性能
2. **数据隐私**：用户输入和AI回复内容会被记录，请注意数据隐私保护
3. **存储空间**：建议定期清理历史数据，避免表过大影响查询性能
4. **错误处理**：统计记录失败不会影响正常对话功能

## 扩展功能

后续可以基于这些统计数据实现：
- 用户行为分析
- 智能体性能优化建议
- 成本分析和预算控制
- 使用趋势预测
- 个性化推荐
