-- AI 数据集表建表脚本
-- 支持：与分类非硬性关联、分类能力（文档类/QA类）、source_id 字段、启用开关、与智能体关联（一个智能体可关联多个数据集）

DROP TABLE IF EXISTS `ai_dataset`;

CREATE TABLE `ai_dataset` (
  `dataset_id` bigint NOT NULL COMMENT '数据集ID',
  `title` varchar(200) NOT NULL COMMENT '数据集标题',
  `icon` varchar(200) DEFAULT NULL COMMENT '图标',
  `indexing_technique` varchar(50) DEFAULT NULL COMMENT '索引方式',
  `process_rule` varchar(200) DEFAULT NULL COMMENT '处理规则',
  `doc_form` varchar(50) DEFAULT NULL COMMENT '索引内容形式',
  `retrieval_model` int DEFAULT NULL COMMENT '检索模式',
  `backend_id` varchar(100) DEFAULT NULL COMMENT '后端实际ID',
  `dataset_type` varchar(20) NOT NULL DEFAULT 'DOCUMENT' COMMENT '数据集类型：DOCUMENT-文档类，QA-问答类',
  `category_id` bigint DEFAULT NULL COMMENT '可选关联分类ID（非硬性关联）',
  `source_id` varchar(100) DEFAULT NULL COMMENT '来源ID',
  `enable` tinyint DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `agent_id` bigint DEFAULT NULL COMMENT '关联的智能体ID',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志：0-正常，1-删除',
  PRIMARY KEY (`dataset_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_dataset_type` (`dataset_type`),
  KEY `idx_enable` (`enable`),
  KEY `idx_del_flag` (`del_flag`),
  CONSTRAINT `fk_dataset_agent` FOREIGN KEY (`agent_id`) REFERENCES `ai_agent` (`agent_id`),
  CONSTRAINT `fk_dataset_category` FOREIGN KEY (`category_id`) REFERENCES `ai_document_category` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI 数据集表';


