package com.coocare.ai.config;

import cn.hutool.core.util.StrUtil;
import com.coocare.ai.config.constants.ConfigConstants;
import com.coocare.ai.service.SysConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Component;

import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * <p>
 * 全局配置管理器
 * 负责在应用启动时加载配置并提供全局访问接口
 * 支持配置的动态更新和缓存管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Slf4j
@Component
public class GlobalConfigManager implements ApplicationRunner {

    // 配置缓存
    private final ConcurrentHashMap<String, String> configCache = new ConcurrentHashMap<>();
    // 读写锁，保证配置读取和更新的线程安全
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    private final ReentrantReadWriteLock.ReadLock readLock = lock.readLock();
    private final ReentrantReadWriteLock.WriteLock writeLock = lock.writeLock();
    @Resource
    private SysConfigService configService;

    private JavaMailSenderImpl mailSender;

    @Bean
    public JavaMailSender javaMailSender() {
        this.mailSender = new JavaMailSenderImpl();
        return this.mailSender;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始加载全局配置...");
        loadAllConfigs();
        log.info("全局配置加载完成，共加载 {} 项配置", configCache.size());
    }

    /**
     * 加载所有配置到缓存
     */
    public void loadAllConfigs() {
        writeLock.lock();
        try {
            configCache.clear();

            // 加载智能体服务配置
            loadConfigIfExists(ConfigConstants.DATASET_API_KEY);
            loadConfigIfExists(ConfigConstants.DIFY_SERVICE_URL);
            loadConfigIfExists(ConfigConstants.DIFY_SERVICE_EMAIL);
            loadConfigIfExists(ConfigConstants.DIFY_SERVICE_PASSWORD);

            // 加载企业信息配置
            loadConfigIfExists(ConfigConstants.COMPANY_INFO_KEY);
            loadConfigIfExists(ConfigConstants.COMPANY_PHONE);
            loadConfigIfExists(ConfigConstants.COMPANY_EMAIL);
            loadConfigIfExists(ConfigConstants.COMPANY_INDUSTRY);
            loadConfigIfExists(ConfigConstants.COMPANY_INDUSTRY_SUB);
            loadConfigIfExists(ConfigConstants.COMPANY_SCALE);

            // 加载系统配置
            loadConfigIfExists(ConfigConstants.SYSTEM_TIMEZONE);
            loadConfigIfExists(ConfigConstants.SYSTEM_LANGUAGE);
            loadConfigIfExists(ConfigConstants.EMAIL_NOTIFICATION_ENABLED);
            loadConfigIfExists(ConfigConstants.SMS_NOTIFICATION_ENABLED);

            // 加载智能体配置
            loadConfigIfExists(ConfigConstants.ENABLED_AGENTS);
            loadConfigIfExists(ConfigConstants.DEFAULT_AGENT);

            // 加载邮件配置
            loadConfigIfExists(ConfigConstants.MAIL_HOST);
            loadConfigIfExists(ConfigConstants.MAIL_PORT);
            loadConfigIfExists(ConfigConstants.MAIL_USERNAME);
            loadConfigIfExists(ConfigConstants.MAIL_PASSWORD);
            loadConfigIfExists(ConfigConstants.MAIL_PROTOCOL);
            loadConfigIfExists(ConfigConstants.MAIL_AUTH);
            loadConfigIfExists(ConfigConstants.MAIL_STARTTLS);

            mailSender.setHost(configCache.get(ConfigConstants.MAIL_HOST));
            mailSender.setPort(Integer.parseInt(configCache.get(ConfigConstants.MAIL_PORT)));
            mailSender.setUsername(configCache.get(ConfigConstants.MAIL_USERNAME));
            mailSender.setPassword(configCache.get(ConfigConstants.MAIL_PASSWORD));
            mailSender.setProtocol(configCache.get(ConfigConstants.MAIL_PROTOCOL) != null ? configCache.get(ConfigConstants.MAIL_PROTOCOL) : "smtp");

            Properties props = mailSender.getJavaMailProperties();
            props.put("mail.transport.protocol", configCache.get(ConfigConstants.MAIL_PROTOCOL) != null ? configCache.get(ConfigConstants.MAIL_PROTOCOL) : "smtp");
            props.put("mail.smtp.auth", configCache.get(ConfigConstants.MAIL_AUTH) != null ? configCache.get(ConfigConstants.MAIL_AUTH) : "true");
            props.put("mail.smtp.starttls.enable", configCache.get(ConfigConstants.MAIL_STARTTLS) != null ? configCache.get(ConfigConstants.MAIL_STARTTLS) : "true");
            props.put("mail.debug", "false");

            log.debug("配置缓存已更新，当前缓存项数: {}", configCache.size());

        } catch (Exception e) {
            log.error("加载全局配置失败", e);
        } finally {
            writeLock.unlock();
        }
    }

    /**
     * 如果配置存在则加载到缓存
     */
    private void loadConfigIfExists(String key) {
        try {
            String value = configService.getValue(key);
            if (StrUtil.isNotBlank(value)) {
                configCache.put(key, value);
                log.debug("加载配置: {} = {}", key, value);
            }
        } catch (Exception e) {
            log.warn("加载配置失败: {}", key, e);
        }
    }

    /**
     * 获取配置值
     */
    public String getConfig(String key) {
        readLock.lock();
        try {
            return configCache.get(key);
        } finally {
            readLock.unlock();
        }
    }

    /**
     * 获取配置值，如果不存在则返回默认值
     */
    public String getConfig(String key, String defaultValue) {
        String value = getConfig(key);
        return StrUtil.isNotBlank(value) ? value : defaultValue;
    }

    /**
     * 更新配置缓存
     */
    public void updateConfig(String key, String value) {
        writeLock.lock();
        try {
            if (StrUtil.isNotBlank(value)) {
                configCache.put(key, value);
            } else {
                configCache.remove(key);
            }
            log.debug("更新配置缓存: {} = {}", key, value);
        } finally {
            writeLock.unlock();
        }
    }

    /**
     * 移除配置缓存
     */
    public void removeConfig(String key) {
        writeLock.lock();
        try {
            configCache.remove(key);
            log.debug("移除配置缓存: {}", key);
        } finally {
            writeLock.unlock();
        }
    }

    // ==================== 智能体服务配置获取方法 ====================

    /**
     * 获取数据集API密钥
     */
    public String getDatasetApiKey() {
        return getConfig(ConfigConstants.DATASET_API_KEY);
    }

    /**
     * 获取Dify服务URL
     */
    public String getDifyServiceUrl() {
        return getConfig(ConfigConstants.DIFY_SERVICE_URL);
    }


    // ==================== 系统配置获取方法 ====================

    /**
     * 获取系统时区
     */
    public String getSystemTimezone() {
        return getConfig(ConfigConstants.SYSTEM_TIMEZONE, "Asia/Shanghai");
    }

    /**
     * 获取系统语言
     */
    public String getSystemLanguage() {
        return getConfig(ConfigConstants.SYSTEM_LANGUAGE, "zh-CN");
    }

    /**
     * 是否启用邮件通知
     */
    public boolean isEmailNotificationEnabled() {
        String value = getConfig(ConfigConstants.EMAIL_NOTIFICATION_ENABLED, "true");
        return Boolean.parseBoolean(value);
    }

    /**
     * 是否启用短信通知
     */
    public boolean isSmsNotificationEnabled() {
        String value = getConfig(ConfigConstants.SMS_NOTIFICATION_ENABLED, "false");
        return Boolean.parseBoolean(value);
    }

    /**
     * 获取默认智能体
     */
    public String getDefaultAgent() {
        return getConfig(ConfigConstants.DEFAULT_AGENT);
    }

    /**
     * 检查系统是否已初始化
     */
    public boolean isSystemInitialized() {
        String value = getConfig(ConfigConstants.SYSTEM_INIT_FLAG);
        return "true".equals(value);
    }

    /**
     * 刷新指定配置项
     */
    public void refreshConfig(String key) {
        writeLock.lock();
        try {
            loadConfigIfExists(key);
        } finally {
            writeLock.unlock();
        }
    }

    /**
     * 获取所有缓存的配置项数量
     */
    public int getCacheSize() {
        readLock.lock();
        try {
            return configCache.size();
        } finally {
            readLock.unlock();
        }
    }
}
