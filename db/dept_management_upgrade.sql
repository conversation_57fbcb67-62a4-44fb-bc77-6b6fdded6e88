-- 部门管理系统升级脚本
-- 支持多级部门管理、部门主管设置、部门AI Agent关联

-- 1. 创建AI智能体表（如果不存在）
CREATE TABLE IF NOT EXISTS `ai_agent` (
  `agent_id` bigint NOT NULL AUTO_INCREMENT COMMENT '智能体ID',
  `agent_name` varchar(100) NOT NULL COMMENT '智能体名称',
  `agent_intro` varchar(500) DEFAULT NULL COMMENT '智能体介绍',
  `agent_description` text COMMENT '智能体详细描述',
  `agent_type` varchar(50) DEFAULT 'GENERAL' COMMENT '智能体类型：GENERAL-通用，SPECIALIZED-专业',
  `agent_config` text COMMENT '智能体配置信息（JSON格式）',
  `enable` tinyint DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志：0-正常，1-删除',
  PRIMARY KEY (`agent_id`),
  KEY `idx_agent_name` (`agent_name`),
  KEY `idx_enable` (`enable`),
  KEY `idx_del_flag` (`del_flag`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI智能体表';

-- 2. 创建部门AI智能体关联表
CREATE TABLE IF NOT EXISTS `sys_dept_agent` (
  `dept_agent_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  `agent_id` bigint NOT NULL COMMENT '智能体ID',
  `is_default` tinyint DEFAULT '0' COMMENT '是否默认智能体：0-否，1-是',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志：0-正常，1-删除',
  PRIMARY KEY (`dept_agent_id`),
  UNIQUE KEY `uk_dept_agent` (`dept_id`, `agent_id`, `del_flag`),
  KEY `idx_dept_id` (`dept_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门AI智能体关联表';

-- 3. 扩展用户表，添加部门主管标识字段
ALTER TABLE `sys_user` 
ADD COLUMN IF NOT EXISTS `is_dept_manager` tinyint DEFAULT '0' COMMENT '是否部门主管：0-否，1-是' AFTER `dept_id`;

-- 4. 扩展部门表，添加更多管理字段
ALTER TABLE `sys_dept` 
ADD COLUMN IF NOT EXISTS `dept_code` varchar(50) DEFAULT NULL COMMENT '部门编码' AFTER `name`,
ADD COLUMN IF NOT EXISTS `dept_type` varchar(20) DEFAULT 'NORMAL' COMMENT '部门类型：NORMAL-普通部门，VIRTUAL-虚拟部门' AFTER `dept_code`,
ADD COLUMN IF NOT EXISTS `manager_user_id` bigint DEFAULT NULL COMMENT '部门主管用户ID' AFTER `dept_type`,
ADD COLUMN IF NOT EXISTS `description` varchar(500) DEFAULT NULL COMMENT '部门描述' AFTER `office_phone`,
ADD COLUMN IF NOT EXISTS `status` tinyint DEFAULT '1' COMMENT '部门状态：0-禁用，1-启用' AFTER `description`;

-- 5. 为部门表添加外键约束（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'sys_dept' 
     AND CONSTRAINT_NAME = 'fk_dept_manager') = 0,
    'ALTER TABLE sys_dept ADD CONSTRAINT fk_dept_manager FOREIGN KEY (manager_user_id) REFERENCES sys_user(user_id)',
    'SELECT "fk_dept_manager constraint already exists" AS message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 插入默认AI智能体数据
INSERT IGNORE INTO `ai_agent` (`agent_id`, `agent_name`, `agent_intro`, `agent_description`, `agent_type`, `enable`, `sort_order`, `create_by`, `del_flag`) VALUES
(1, '通用助手', '通用AI助手，可以处理各种常见问题', '这是一个通用的AI助手，能够回答各种问题，提供基础的帮助和支持。适用于所有部门的日常咨询需求。', 'GENERAL', 1, 1, 'system', '0'),
(2, '技术支持专家', '专业的技术支持AI助手', '专门针对技术问题设计的AI助手，具备丰富的技术知识库，能够解决各种技术难题，适合技术部门使用。', 'SPECIALIZED', 1, 2, 'system', '0'),
(3, '客服助手', '专业的客户服务AI助手', '专门为客户服务设计的AI助手，具备良好的沟通技巧和客户服务经验，能够处理客户咨询和投诉。', 'SPECIALIZED', 1, 3, 'system', '0'),
(4, '销售顾问', '专业的销售支持AI助手', '专门为销售团队设计的AI助手，具备产品知识和销售技巧，能够协助销售人员进行客户沟通和产品推荐。', 'SPECIALIZED', 1, 4, 'system', '0'),
(5, '人事助手', '专业的人力资源AI助手', '专门为人力资源部门设计的AI助手，熟悉人事政策和流程，能够协助处理员工咨询和人事管理事务。', 'SPECIALIZED', 1, 5, 'system', '0');

-- 7. 为默认部门分配AI智能体
-- 技术部分配技术支持专家和通用助手
INSERT IGNORE INTO ai_dept_agent (`dept_id`, `agent_id`, `is_default`, `sort_order`, `create_by`, `del_flag`)
SELECT d.dept_id, 2, 1, 1, 'system', '0' FROM sys_dept d WHERE d.name = '技术部' AND d.del_flag = '0';

INSERT IGNORE INTO ai_dept_agent (`dept_id`, `agent_id`, `is_default`, `sort_order`, `create_by`, `del_flag`)
SELECT d.dept_id, 1, 0, 2, 'system', '0' FROM sys_dept d WHERE d.name = '技术部' AND d.del_flag = '0';

-- 市场部分配销售顾问和通用助手
INSERT IGNORE INTO ai_dept_agent (`dept_id`, `agent_id`, `is_default`, `sort_order`, `create_by`, `del_flag`)
SELECT d.dept_id, 4, 1, 1, 'system', '0' FROM sys_dept d WHERE d.name = '市场部' AND d.del_flag = '0';

INSERT IGNORE INTO ai_dept_agent (`dept_id`, `agent_id`, `is_default`, `sort_order`, `create_by`, `del_flag`)
SELECT d.dept_id, 1, 0, 2, 'system', '0' FROM sys_dept d WHERE d.name = '市场部' AND d.del_flag = '0';

-- 人事部分配人事助手和通用助手
INSERT IGNORE INTO ai_dept_agent (`dept_id`, `agent_id`, `is_default`, `sort_order`, `create_by`, `del_flag`)
SELECT d.dept_id, 5, 1, 1, 'system', '0' FROM sys_dept d WHERE d.name = '人事部' AND d.del_flag = '0';

INSERT IGNORE INTO ai_dept_agent (`dept_id`, `agent_id`, `is_default`, `sort_order`, `create_by`, `del_flag`)
SELECT d.dept_id, 1, 0, 2, 'system', '0' FROM sys_dept d WHERE d.name = '人事部' AND d.del_flag = '0';

-- 财务部分配通用助手
INSERT IGNORE INTO ai_dept_agent (`dept_id`, `agent_id`, `is_default`, `sort_order`, `create_by`, `del_flag`)
SELECT d.dept_id, 1, 1, 1, 'system', '0' FROM sys_dept d WHERE d.name = '财务部' AND d.del_flag = '0';

-- 8. 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS `idx_user_dept_manager` ON `sys_user` (`dept_id`, `is_dept_manager`);
CREATE INDEX IF NOT EXISTS `idx_dept_manager` ON `sys_dept` (`manager_user_id`);
CREATE INDEX IF NOT EXISTS `idx_dept_status` ON `sys_dept` (`status`);
CREATE INDEX IF NOT EXISTS `idx_dept_code` ON `sys_dept` (`dept_code`);

-- 9. 插入示例多级部门数据
INSERT IGNORE INTO `sys_dept` (`name`, `dept_code`, `parent_id`, `sort_order`, `phone`, `email`, `description`, `status`, `del_flag`, `create_time`, `update_time`) VALUES
-- 技术部下的子部门
('研发部', 'RD', (SELECT dept_id FROM (SELECT dept_id FROM sys_dept WHERE name = '技术部' AND del_flag = '0' LIMIT 1) AS temp), 1, '010-12345678-101', '<EMAIL>', '负责产品研发和技术创新', 1, '0', NOW(), NOW()),
('测试部', 'QA', (SELECT dept_id FROM (SELECT dept_id FROM sys_dept WHERE name = '技术部' AND del_flag = '0' LIMIT 1) AS temp), 2, '010-12345678-102', '<EMAIL>', '负责产品质量保证和测试', 1, '0', NOW(), NOW()),
('运维部', 'OPS', (SELECT dept_id FROM (SELECT dept_id FROM sys_dept WHERE name = '技术部' AND del_flag = '0' LIMIT 1) AS temp), 3, '010-12345678-103', '<EMAIL>', '负责系统运维和技术支持', 1, '0', NOW(), NOW()),

-- 市场部下的子部门
('销售部', 'SALES', (SELECT dept_id FROM (SELECT dept_id FROM sys_dept WHERE name = '市场部' AND del_flag = '0' LIMIT 1) AS temp), 1, '010-12345680-101', '<EMAIL>', '负责产品销售和客户开发', 1, '0', NOW(), NOW()),
('市场推广部', 'MARKETING', (SELECT dept_id FROM (SELECT dept_id FROM sys_dept WHERE name = '市场部' AND del_flag = '0' LIMIT 1) AS temp), 2, '010-12345680-102', '<EMAIL>', '负责品牌推广和市场活动', 1, '0', NOW(), NOW()),

-- 人事部下的子部门
('招聘部', 'RECRUIT', (SELECT dept_id FROM (SELECT dept_id FROM sys_dept WHERE name = '人事部' AND del_flag = '0' LIMIT 1) AS temp), 1, '010-12345682-101', '<EMAIL>', '负责人才招聘和选拔', 1, '0', NOW(), NOW()),
('培训部', 'TRAINING', (SELECT dept_id FROM (SELECT dept_id FROM sys_dept WHERE name = '人事部' AND del_flag = '0' LIMIT 1) AS temp), 2, '010-12345682-102', '<EMAIL>', '负责员工培训和发展', 1, '0', NOW(), NOW());
