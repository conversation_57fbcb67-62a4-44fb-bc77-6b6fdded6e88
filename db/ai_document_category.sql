-- AI文档分类表建表脚本
-- 支持3层分类结构：产品型号 -> 产品系列 -> 文档类型

DROP TABLE IF EXISTS `ai_document_category`;

CREATE TABLE `ai_document_category` (
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) DEFAULT NULL COMMENT '分类编码',
  `parent_id` bigint DEFAULT NULL COMMENT '父级分类ID，NULL表示顶级分类',
  `level` tinyint NOT NULL DEFAULT '1' COMMENT '分类层级：1-产品型号，2-产品系列，3-文档类型',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `description` varchar(500) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(200) DEFAULT NULL COMMENT '分类图标',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `is_system` tinyint DEFAULT '0' COMMENT '是否系统分类：0-否，1-是（第三层自动生成的分类）',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志：0-正常，1-删除',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_status` (`status`),
  KEY `idx_del_flag` (`del_flag`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_category_name` (`category_name`),
  KEY `idx_category_code` (`category_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI文档分类表';

-- 初始化示例数据
-- 第一层：产品型号
INSERT INTO `ai_document_category` (`category_id`, `category_name`, `category_code`, `parent_id`, `level`, `sort_order`, `description`, `status`, `is_system`, `create_by`, `del_flag`) VALUES
(1001, 'HP EliteBook', 'HP_ELITEBOOK', NULL, 1, 1, 'HP EliteBook系列商务笔记本', 1, 0, 'system', '0'),
(1002, 'HP ProBook', 'HP_PROBOOK', NULL, 1, 2, 'HP ProBook系列商务笔记本', 1, 0, 'system', '0'),
(1003, 'HP Pavilion', 'HP_PAVILION', NULL, 1, 3, 'HP Pavilion系列消费级笔记本', 1, 0, 'system', '0'),
(1004, 'HP OMEN', 'HP_OMEN', NULL, 1, 4, 'HP OMEN系列游戏笔记本', 1, 0, 'system', '0'),
(1005, 'HP Spectre', 'HP_SPECTRE', NULL, 1, 5, 'HP Spectre系列高端超薄笔记本', 1, 0, 'system', '0');

-- 第二层：产品系列（以HP EliteBook为例）
INSERT INTO `ai_document_category` (`category_id`, `category_name`, `category_code`, `parent_id`, `level`, `sort_order`, `description`, `status`, `is_system`, `create_by`, `del_flag`) VALUES
(2001, 'HP EliteBook 6 G1i 13', 'HP_ELITEBOOK_6_G1I_13', 1001, 2, 1, 'HP EliteBook 6 G1i 13英寸商务笔记本', 1, 0, 'system', '0'),
(2002, 'HP EliteBook 6 G1i 14', 'HP_ELITEBOOK_6_G1I_14', 1001, 2, 2, 'HP EliteBook 6 G1i 14英寸商务笔记本', 1, 0, 'system', '0'),
(2003, 'HP EliteBook 8 G1i 14', 'HP_ELITEBOOK_8_G1I_14', 1001, 2, 3, 'HP EliteBook 8 G1i 14英寸商务笔记本', 1, 0, 'system', '0'),
(2004, 'HP EliteBook 8 G1i 16', 'HP_ELITEBOOK_8_G1I_16', 1001, 2, 4, 'HP EliteBook 8 G1i 16英寸商务笔记本', 1, 0, 'system', '0');

-- 第三层：文档类型（为HP EliteBook 6 G1i 13自动生成）
INSERT INTO `ai_document_category` (`category_id`, `category_name`, `category_code`, `parent_id`, `level`, `sort_order`, `description`, `status`, `is_system`, `create_by`, `del_flag`) VALUES
(3001, '产品资料', 'PRODUCT_DATA', 2001, 3, 1, '产品技术资料和说明文档', 1, 1, 'system', '0'),
(3002, '产品图库', 'PRODUCT_GALLERY', 2001, 3, 2, '产品高清图片和展示图库', 1, 1, 'system', '0'),
(3003, '产品彩页', 'PRODUCT_BROCHURE', 2001, 3, 3, '产品宣传彩页和营销资料', 1, 1, 'system', '0'),
(3004, '产品证书', 'PRODUCT_CERTIFICATE', 2001, 3, 4, '产品认证证书和资质文件', 1, 1, 'system', '0'),
(3005, 'Quick Specs', 'QUICK_SPECS', 2001, 3, 5, '产品快速规格参数表', 1, 1, 'system', '0'),
(3006, '图说产品', 'PRODUCT_ILLUSTRATION', 2001, 3, 6, '产品图解和功能说明', 1, 1, 'system', '0');

-- 第三层：文档类型（为HP EliteBook 6 G1i 14自动生成）
INSERT INTO `ai_document_category` (`category_id`, `category_name`, `category_code`, `parent_id`, `level`, `sort_order`, `description`, `status`, `is_system`, `create_by`, `del_flag`) VALUES
(3007, '产品资料', 'PRODUCT_DATA', 2002, 3, 1, '产品技术资料和说明文档', 1, 1, 'system', '0'),
(3008, '产品图库', 'PRODUCT_GALLERY', 2002, 3, 2, '产品高清图片和展示图库', 1, 1, 'system', '0'),
(3009, '产品彩页', 'PRODUCT_BROCHURE', 2002, 3, 3, '产品宣传彩页和营销资料', 1, 1, 'system', '0'),
(3010, '产品证书', 'PRODUCT_CERTIFICATE', 2002, 3, 4, '产品认证证书和资质文件', 1, 1, 'system', '0'),
(3011, 'Quick Specs', 'QUICK_SPECS', 2002, 3, 5, '产品快速规格参数表', 1, 1, 'system', '0'),
(3012, '图说产品', 'PRODUCT_ILLUSTRATION', 2002, 3, 6, '产品图解和功能说明', 1, 1, 'system', '0');

-- 第三层：文档类型（为HP EliteBook 8 G1i 14自动生成）
INSERT INTO `ai_document_category` (`category_id`, `category_name`, `category_code`, `parent_id`, `level`, `sort_order`, `description`, `status`, `is_system`, `create_by`, `del_flag`) VALUES
(3013, '产品资料', 'PRODUCT_DATA', 2003, 3, 1, '产品技术资料和说明文档', 1, 1, 'system', '0'),
(3014, '产品图库', 'PRODUCT_GALLERY', 2003, 3, 2, '产品高清图片和展示图库', 1, 1, 'system', '0'),
(3015, '产品彩页', 'PRODUCT_BROCHURE', 2003, 3, 3, '产品宣传彩页和营销资料', 1, 1, 'system', '0'),
(3016, '产品证书', 'PRODUCT_CERTIFICATE', 2003, 3, 4, '产品认证证书和资质文件', 1, 1, 'system', '0'),
(3017, 'Quick Specs', 'QUICK_SPECS', 2003, 3, 5, '产品快速规格参数表', 1, 1, 'system', '0'),
(3018, '图说产品', 'PRODUCT_ILLUSTRATION', 2003, 3, 6, '产品图解和功能说明', 1, 1, 'system', '0');

-- 第三层：文档类型（为HP EliteBook 8 G1i 16自动生成）
INSERT INTO `ai_document_category` (`category_id`, `category_name`, `category_code`, `parent_id`, `level`, `sort_order`, `description`, `status`, `is_system`, `create_by`, `del_flag`) VALUES
(3019, '产品资料', 'PRODUCT_DATA', 2004, 3, 1, '产品技术资料和说明文档', 1, 1, 'system', '0'),
(3020, '产品图库', 'PRODUCT_GALLERY', 2004, 3, 2, '产品高清图片和展示图库', 1, 1, 'system', '0'),
(3021, '产品彩页', 'PRODUCT_BROCHURE', 2004, 3, 3, '产品宣传彩页和营销资料', 1, 1, 'system', '0'),
(3022, '产品证书', 'PRODUCT_CERTIFICATE', 2004, 3, 4, '产品认证证书和资质文件', 1, 1, 'system', '0'),
(3023, 'Quick Specs', 'QUICK_SPECS', 2004, 3, 5, '产品快速规格参数表', 1, 1, 'system', '0'),
(3024, '图说产品', 'PRODUCT_ILLUSTRATION', 2004, 3, 6, '产品图解和功能说明', 1, 1, 'system', '0');

-- 为其他产品型号添加一些示例产品系列
-- HP ProBook系列
INSERT INTO `ai_document_category` (`category_id`, `category_name`, `category_code`, `parent_id`, `level`, `sort_order`, `description`, `status`, `is_system`, `create_by`, `del_flag`) VALUES
(2005, 'HP ProBook 4 G1i 14', 'HP_PROBOOK_4_G1I_14', 1002, 2, 1, 'HP ProBook 4 G1i 14英寸商务笔记本', 1, 0, 'system', '0'),
(2006, 'HP ProBook 6 G1i 16', 'HP_PROBOOK_6_G1I_16', 1002, 2, 2, 'HP ProBook 6 G1i 16英寸商务笔记本', 1, 0, 'system', '0');

-- HP Pavilion系列
INSERT INTO `ai_document_category` (`category_id`, `category_name`, `category_code`, `parent_id`, `level`, `sort_order`, `description`, `status`, `is_system`, `create_by`, `del_flag`) VALUES
(2007, 'HP Pavilion 14-dv', 'HP_PAVILION_14_DV', 1003, 2, 1, 'HP Pavilion 14-dv系列消费级笔记本', 1, 0, 'system', '0'),
(2008, 'HP Pavilion 15-eg', 'HP_PAVILION_15_EG', 1003, 2, 2, 'HP Pavilion 15-eg系列消费级笔记本', 1, 0, 'system', '0');

-- 注意：实际使用时，第三层分类应该通过应用程序自动生成，而不是手动插入
-- 这里只是为了演示数据结构
