package com.coocare.ai.controller.client;


import com.alibaba.fastjson2.JSONObject;
import com.coocare.ai.config.GlobalConfigManager;
import com.coocare.ai.config.auth.reource.annotation.Inner;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.AiAgent;
import com.coocare.ai.service.AiAgentService;
import io.github.guoshiqiufeng.dify.chat.DifyChat;
import io.github.guoshiqiufeng.dify.chat.dto.request.ChatMessageSendRequest;
import io.github.guoshiqiufeng.dify.chat.dto.response.ChatMessageSendCompletionResponse;
import io.github.guoshiqiufeng.dify.client.spring6.builder.DifyChatBuilder;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * 智能体的应用
 *
 * <AUTHOR>
 * @since 2025-08-19
 */

@Tag(name = "智能体的应用")
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/rest/agent")
public class AgentController {

    private final GlobalConfigManager globalConfigManager;
    private final AiAgentService aiAgentService;

    @SneakyThrows
    @GetMapping("messageEnd/{agentId}/{conversationId}")
    public AjaxResult<?> messageEnd(
            @PathVariable("agentId") Long agentId,
            @PathVariable("conversationId") String conversationId) {
        return AjaxResult.ok();
    }


    @Inner(value = false)
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatMessageSendCompletionResponse> send(@RequestHeader("agent_id") Long agentId, @RequestBody ChatMessageSendRequest chatMessageSendRequest){
        AiAgent aiAgent = aiAgentService.getById(agentId);
        chatMessageSendRequest.setApiKey(JSONObject.parseObject(aiAgent.getAgentConfig()).getString("apiKey"));
        chatMessageSendRequest.setUserId("user-1");
        DifyChat difyChat = DifyChatBuilder.create(
                DifyChatBuilder.DifyChatClientBuilder
                        .builder()
                        .baseUrl(globalConfigManager.getDifyServiceUrl())
                        .build());
        return difyChat.sendChatMessageStream(chatMessageSendRequest);
    }

    @Inner(value = false)
    @PostMapping(value = "/streamTest", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatMessageSendCompletionResponse> sendTest(){
        ChatMessageSendRequest chatMessageSendRequest = new ChatMessageSendRequest();
        chatMessageSendRequest.setContent("你是谁");
        chatMessageSendRequest.setApiKey("app-iHlMwboTtHvyp9Nqew3t3utR");
        chatMessageSendRequest.setUserId("user-333");
        DifyChat difyChat = DifyChatBuilder.create(
                DifyChatBuilder.DifyChatClientBuilder
                        .builder()
                        .baseUrl(globalConfigManager.getDifyServiceUrl())
                        .build());
        return difyChat.sendChatMessageStream(chatMessageSendRequest);
    }

}
