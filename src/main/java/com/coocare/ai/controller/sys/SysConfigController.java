package com.coocare.ai.controller.sys;

import com.coocare.ai.config.GlobalConfigManager;
import com.coocare.ai.config.domain.AjaxResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 系统配置控制器
 * 提供配置管理和测试接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Tag(name = "系统配置管理", description = "系统配置相关接口")
@RestController
@RequestMapping("/sys/config")
@RequiredArgsConstructor
public class SysConfigController {

    private final GlobalConfigManager globalConfigManager;

    @Operation(summary = "获取智能体服务配置信息", description = "返回智能体服务相关关键配置项值", operationId = "sysConfig_agentService")
    @GetMapping("/agentService")
    public AjaxResult<?> getAgentServiceConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("datasetApiKey", globalConfigManager.getDatasetApiKey());
        config.put("difyServiceUrl", globalConfigManager.getDifyServiceUrl());
        return AjaxResult.success(config);
    }

    @Operation(summary = "获取系统配置信息", description = "返回系统时区、语言、通知开关、默认智能体等配置", operationId = "sysConfig_system")
    @GetMapping("/system")
    public AjaxResult<?> getSystemConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("timezone", globalConfigManager.getSystemTimezone());
        config.put("language", globalConfigManager.getSystemLanguage());
        config.put("emailNotificationEnabled", globalConfigManager.isEmailNotificationEnabled());
        config.put("smsNotificationEnabled", globalConfigManager.isSmsNotificationEnabled());
        config.put("defaultAgent", globalConfigManager.getDefaultAgent());
        config.put("systemInitialized", globalConfigManager.isSystemInitialized());
        config.put("cacheSize", globalConfigManager.getCacheSize());
        
        return AjaxResult.success(config);
    }

    @Operation(summary = "刷新全局配置缓存", description = "重新加载所有配置到全局缓存", operationId = "sysConfig_refreshAll")
    @PostMapping("/refresh")
    public AjaxResult<?> refreshConfig() {
        try {
            globalConfigManager.loadAllConfigs();
            return AjaxResult.success("配置缓存刷新成功，当前缓存项数: " + globalConfigManager.getCacheSize());
        } catch (Exception e) {
            return AjaxResult.failed("配置缓存刷新失败: " + e.getMessage());
        }
    }

    @Operation(summary = "刷新指定配置项", description = "仅刷新指定配置键对应的缓存值", operationId = "sysConfig_refreshOne")
    @PostMapping("/refresh/{configKey}")
    public AjaxResult<?> refreshSpecificConfig(@PathVariable String configKey) {
        try {
            globalConfigManager.refreshConfig(configKey);
            String value = globalConfigManager.getConfig(configKey);
            return AjaxResult.success("配置项刷新成功");
        } catch (Exception e) {
            return AjaxResult.failed("配置项刷新失败: " + e.getMessage());
        }
    }
}
