package com.coocare.ai.config.auth.reource.annotation;

import java.lang.annotation.*;

/**
 * 服务内部调用注解
 * 标注后由 AOP 切面放行内部请求（基于 FROM 请求头识别）
 *
 * <AUTHOR>
 * @since 2020-06-14
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Inner {

	/**
	 * 是否AOP统一处理
	 * @return false, true
	 */
	boolean value() default true;

	/**
	 * 需要特殊判空的字段(预留)
	 * @return {}
	 */
	String[] field() default {};

}
