package com.coocare.ai.config.constants;

/**
 * <p>
 * 配置项常量类
 * 定义系统中使用的配置键名常量
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
public interface ConfigConstants {

    // ==================== 系统初始化相关 ====================
    
    /** 系统初始化完成标识 */
    String SYSTEM_INIT_FLAG = "SYSTEM_INITIALIZED";
    
    /** 企业信息配置 */
    String COMPANY_INFO_KEY = "COMPANY_INFO";
    
    /** 行业信息配置 */
    String INDUSTRY_INFO_KEY = "INDUSTRY_INFO";
    
    /** 智能体配置 */
    String AGENT_CONFIG_KEY = "AGENT_CONFIG";

    // ==================== 智能体服务配置 ====================
    
    /** 数据集API密钥 */
    String DATASET_API_KEY = "DATASET_API_KEY";
    
    /** Dify服务URL */
    String DIFY_SERVICE_URL = "DIFY_SERVICE_URL";

    /** Dify管理员账号 */
    String DIFY_SERVICE_EMAIL = "DIFY_SERVICE_EMAIL";

    /** Dify管理员密码 */
    String DIFY_SERVICE_PASSWORD = "DIFY_SERVICE_PASSWORD";
    
    // ==================== 企业信息相关 ====================
    
    /** 企业联系电话 */
    String COMPANY_PHONE = "COMPANY_PHONE";
    
    /** 企业邮箱 */
    String COMPANY_EMAIL = "COMPANY_EMAIL";
    
    /** 所属行业 */
    String COMPANY_INDUSTRY = "COMPANY_INDUSTRY";
    
    /** 行业细分 */
    String COMPANY_INDUSTRY_SUB = "COMPANY_INDUSTRY_SUB";
    
    /** 企业规模 */
    String COMPANY_SCALE = "COMPANY_SCALE";

    // ==================== 系统配置相关 ====================
    
    /** 系统时区 */
    String SYSTEM_TIMEZONE = "SYSTEM_TIMEZONE";
    
    /** 系统语言 */
    String SYSTEM_LANGUAGE = "SYSTEM_LANGUAGE";
    
    /** 邮件通知启用状态 */
    String EMAIL_NOTIFICATION_ENABLED = "EMAIL_NOTIFICATION_ENABLED";
    
    /** 短信通知启用状态 */
    String SMS_NOTIFICATION_ENABLED = "SMS_NOTIFICATION_ENABLED";
    
    /** 启用的智能体列表 */
    String ENABLED_AGENTS = "ENABLED_AGENTS";
    
    /** 默认智能体 */
    String DEFAULT_AGENT = "DEFAULT_AGENT";

    // ==================== 邮件配置相关 ====================

    String MAIL_HOST = "MAIL_HOST";
    String MAIL_PORT = "MAIL_PORT";
    String MAIL_USERNAME = "MAIL_USERNAME";
    String MAIL_PASSWORD = "MAIL_PASSWORD";
    String MAIL_PROTOCOL = "MAIL_PROTOCOL";
    String MAIL_AUTH = "MAIL_AUTH";
    String MAIL_STARTTLS = "MAIL_STARTTLS";

}
