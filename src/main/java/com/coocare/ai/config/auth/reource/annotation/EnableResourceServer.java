package com.coocare.ai.config.auth.reource.annotation;

import com.coocare.ai.config.auth.reource.component.ResourceServerAutoConfiguration;
import com.coocare.ai.config.auth.reource.component.ResourceServerConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 资源服务器启用注解
 * 导入资源服务器自动配置与安全配置
 *
 * <AUTHOR>
 * @since 2022-06-04
 */
@Documented
@Inherited
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Import({ResourceServerAutoConfiguration.class, ResourceServerConfiguration.class})
public @interface EnableResourceServer {

}
