# Google TOTP 身份验证器实现

本项目实现了基于时间的一次性密码（TOTP）身份验证功能，兼容Google Authenticator等标准TOTP应用。

## 功能特性

- ✅ 生成符合RFC 6238标准的TOTP验证码
- ✅ 生成Base32编码的密钥
- ✅ 生成标准的otpauth://二维码URL
- ✅ 生成二维码图片（PNG格式）
- ✅ 验证用户输入的6位验证码
- ✅ 支持时间窗口偏移（默认±3个30秒窗口）
- ✅ 完整的测试用例

## 依赖库

项目使用以下依赖：

```xml
<!-- 二维码生成依赖 -->
<dependency>
    <groupId>com.google.zxing</groupId>
    <artifactId>core</artifactId>
    <version>3.5.3</version>
</dependency>
<dependency>
    <groupId>com.google.zxing</groupId>
    <artifactId>javase</artifactId>
    <version>3.5.3</version>
</dependency>

<!-- Commons Codec for Base32/Base64 -->
<dependency>
    <groupId>commons-codec</groupId>
    <artifactId>commons-codec</artifactId>
</dependency>
```

## 核心类

### GoogleAuthenticatorUtils

主要的工具类，提供以下静态方法：

- `generateSecretKey()` - 生成Base32编码的密钥
- `genSecret(user, host)` - 生成密钥和二维码URL
- `getQRBarcodeURL(user, host, secret)` - 生成二维码URL
- `generateQRCodeImage(text, width, height)` - 生成二维码图片
- `generateCurrentTOTP(secret)` - 生成当前时间的验证码
- `generateTOTP(secret, timeStamp)` - 生成指定时间的验证码
- `authCode(code, secret)` - 验证用户输入的验证码

## 使用示例

### 1. 基本使用流程

```java
// 1. 生成密钥和二维码URL
GoogleAuthenticatorUtils.TOTPResult result = 
    GoogleAuthenticatorUtils.genSecret("<EMAIL>", "example.com");

String secret = result.getSecret();
String qrCodeUrl = result.getQrCodeUrl();

// 2. 生成二维码图片
byte[] qrCodeImage = GoogleAuthenticatorUtils.generateQRCodeImage(qrCodeUrl, 300, 300);

// 保存二维码图片
try (FileOutputStream fos = new FileOutputStream("qrcode.png")) {
    fos.write(qrCodeImage);
}

// 3. 用户扫描二维码后，验证用户输入的验证码
String userInputCode = "123456"; // 用户输入的6位验证码
Boolean isValid = GoogleAuthenticatorUtils.authCode(userInputCode, secret);

if (isValid) {
    System.out.println("验证成功！");
} else {
    System.out.println("验证失败！");
}
```

### 2. 生成测试验证码

```java
// 生成当前时间的验证码（用于测试）
String secret = "JBSWY3DPEHPK3PXP";
String currentCode = GoogleAuthenticatorUtils.generateCurrentTOTP(secret);
System.out.println("当前验证码: " + currentCode);

// 验证生成的验证码
Boolean isValid = GoogleAuthenticatorUtils.authCode(currentCode, secret);
System.out.println("验证结果: " + isValid); // 应该输出 true
```

### 3. Web应用集成示例

```java
@RestController
@RequestMapping("/api/totp")
public class TOTPController {

    @PostMapping("/generate")
    public ResponseEntity<Map<String, Object>> generateTOTP(@RequestParam String username) {
        try {
            // 生成密钥和二维码
            GoogleAuthenticatorUtils.TOTPResult result = 
                GoogleAuthenticatorUtils.genSecret(username, "yourapp.com");
            
            // 生成二维码图片
            byte[] qrCodeImage = GoogleAuthenticatorUtils.generateQRCodeImage(
                result.getQrCodeUrl(), 300, 300);
            
            Map<String, Object> response = new HashMap<>();
            response.put("secret", result.getSecret());
            response.put("qrCodeUrl", result.getQrCodeUrl());
            response.put("qrCodeImage", Base64.getEncoder().encodeToString(qrCodeImage));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Map.of("error", e.getMessage()));
        }
    }

    @PostMapping("/verify")
    public ResponseEntity<Map<String, Object>> verifyTOTP(
            @RequestParam String code, 
            @RequestParam String secret) {
        
        Boolean isValid = GoogleAuthenticatorUtils.authCode(code, secret);
        
        Map<String, Object> response = new HashMap<>();
        response.put("valid", isValid);
        response.put("message", isValid ? "验证成功" : "验证失败");
        
        return ResponseEntity.ok(response);
    }
}
```

## 运行测试

### 1. 运行单元测试

```bash
mvn test -Dtest=GoogleAuthenticatorUtilsTest
```

### 2. 运行简单测试（不依赖Spring Boot）

```bash
# 编译项目
mvn compile

# 运行简单测试
java -cp "target/classes:target/dependency/*" com.coocare.ai.demo.SimpleTOTPTest
```

### 3. 运行演示程序

```bash
java -cp "target/classes:target/dependency/*" com.coocare.ai.demo.TOTPDemo
```

## 测试用例说明

### GoogleAuthenticatorUtilsTest

完整的JUnit测试用例，包括：

- `testGenerateSecretKey()` - 测试密钥生成
- `testGetQRBarcodeURL()` - 测试二维码URL生成
- `testGenSecret()` - 测试密钥和URL生成
- `testGenerateQRCodeImage()` - 测试二维码图片生成
- `testGenerateCurrentTOTP()` - 测试当前验证码生成
- `testGenerateTOTP()` - 测试指定时间验证码生成
- `testAuthCode()` - 测试验证码验证
- `testTimeWindowValidation()` - 测试时间窗口验证
- `testCompleteFlow()` - 测试完整流程

### SimpleTOTPTest

简单的测试类，不依赖Spring Boot框架，可以直接运行验证功能。

## 安全注意事项

1. **密钥存储**: 生成的密钥应该安全存储，建议加密保存在数据库中
2. **时间同步**: 服务器时间应该与标准时间同步，时间偏差会影响验证
3. **重放攻击**: 建议记录已使用的验证码，防止重放攻击
4. **备用码**: 建议为用户生成备用恢复码，以防设备丢失

## 兼容性

- 兼容Google Authenticator
- 兼容Microsoft Authenticator
- 兼容Authy
- 兼容其他符合RFC 6238标准的TOTP应用

## 技术规范

- 算法: HMAC-SHA1
- 验证码长度: 6位数字
- 时间步长: 30秒
- 时间窗口: ±3步（可配置）
- 密钥编码: Base32

## 故障排除

### 常见问题

1. **验证码不匹配**
   - 检查服务器时间是否准确
   - 确认密钥是否正确
   - 检查用户设备时间是否同步

2. **二维码无法扫描**
   - 确认二维码图片清晰度
   - 检查二维码URL格式是否正确
   - 尝试手动输入密钥

3. **编译错误**
   - 确认Java版本为17或以上
   - 检查Maven依赖是否正确下载
   - 确认ZXing和Commons Codec依赖已添加

## 更新日志

### v1.0.0 (2025-08-19)
- 初始版本发布
- 实现基本TOTP功能
- 添加二维码生成功能
- 完整的测试用例
- 演示程序和文档
