package com.coocare.ai.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.sys.SysDept;
import com.coocare.ai.entity.sys.SysUser;
import com.coocare.ai.entity.sys.dto.DeptQueryDTO;
import com.coocare.ai.mapper.SysDeptMapper;
import com.coocare.ai.mapper.SysUserMapper;
import com.coocare.ai.service.SysDeptService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 部门领域服务实现
 * 提供部门树构建、分页、CRUD、主管设置与聚合视图（含智能体与用户统计）
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Service
@RequiredArgsConstructor
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements SysDeptService {

    private final SysUserMapper userMapper;

    @Override
    public List<Tree<Long>> treeDept(Long parentId, String name) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .like(StrUtil.isNotBlank(name), SysDept::getName, name)
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);

        List<SysDept> deptList = this.list(wrapper);

        if (CollUtil.isEmpty(deptList)) {
            return CollUtil.newArrayList();
        }

        return TreeUtil.build(deptList, parentId, TreeNodeConfig.DEFAULT_CONFIG.setIdKey("deptId"),
                (treeNode, tree) -> {
                    tree.setId(treeNode.getDeptId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setName(treeNode.getName());
                    tree.putExtra("phone", treeNode.getPhone());
                    tree.putExtra("email", treeNode.getEmail());
                    tree.putExtra("sortOrder", treeNode.getSortOrder());
                    tree.putExtra("createTime", treeNode.getCreateTime());
                    tree.putExtra("updateTime", treeNode.getUpdateTime());
                });
    }

    @Override
    public PageUtils pageInfo(PageDomain pageDomain, String name) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .like(StrUtil.isNotBlank(name), SysDept::getName, name)
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);

        Page<SysDept> page = this.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), wrapper);
        return new PageUtils(page);
    }

    @Override
    public List<SysDept> getChildrenByParentId(Long parentId) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .eq(SysDept::getParentId, parentId)
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);
        return this.list(wrapper);
    }

    @Override
    public boolean hasChildren(Long deptId) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .eq(SysDept::getParentId, deptId)
                .eq(SysDept::getDelFlag, "0");
        return this.count(wrapper) > 0;
    }

    @Override
    public PageUtils pageInfoNew(PageDomain pageDomain, DeptQueryDTO queryDTO) {
        LambdaQueryWrapper<SysDept> wrapper = Wrappers.lambdaQuery(SysDept.class)
                .like(StrUtil.isNotBlank(queryDTO.getName()), SysDept::getName, queryDTO.getName())
                .eq(StrUtil.isNotBlank(queryDTO.getDeptCode()), SysDept::getDeptCode, queryDTO.getDeptCode())
                .eq(queryDTO.getParentId() != null, SysDept::getParentId, queryDTO.getParentId())
                .eq(queryDTO.getManagerUserId() != null, SysDept::getManagerUserId, queryDTO.getManagerUserId())
                .eq(SysDept::getDelFlag, "0")
                .orderByAsc(SysDept::getSortOrder);

        Page<SysDept> page = this.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), wrapper);
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDeptManager(Long deptId, Long userId) {
        // 更新部门主管字段
        SysDept dept = new SysDept();
        dept.setDeptId(deptId);
        dept.setManagerUserId(userId);
        boolean deptUpdated = this.updateById(dept);

        // 清除该部门其他用户的主管标识
        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getDeptId, deptId)
                .eq(SysUser::getIsDeptManager, 1)
                .eq(SysUser::getDelFlag, "0");
        List<SysUser> currentManagers = userMapper.selectList(wrapper);

        for (SysUser manager : currentManagers) {
            manager.setIsDeptManager(0);
            userMapper.updateById(manager);
        }

        // 设置新的部门主管标识
        SysUser newManager = userMapper.selectById(userId);
        if (newManager != null) {
            newManager.setIsDeptManager(1);
            newManager.setDeptId(deptId); // 确保用户属于该部门
            userMapper.updateById(newManager);
        }

        return deptUpdated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeDeptManager(Long deptId) {
        // 清除部门主管字段
        SysDept dept = new SysDept();
        dept.setDeptId(deptId);
        dept.setManagerUserId(null);
        boolean deptUpdated = this.updateById(dept);

        // 清除该部门所有用户的主管标识
        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getDeptId, deptId)
                .eq(SysUser::getIsDeptManager, 1)
                .eq(SysUser::getDelFlag, "0");
        List<SysUser> managers = userMapper.selectList(wrapper);

        for (SysUser manager : managers) {
            manager.setIsDeptManager(0);
            userMapper.updateById(manager);
        }

        return deptUpdated;
    }

}
