package com.coocare.ai.entity.sys;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 客户端信息
 * </p>
 *
 * <AUTHOR>
 * @since 2018-05-15
 */
@Data
@Schema(description = "客户端信息")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysOauthClientDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    @Schema(description = "id")
    private Long id;

    @NotBlank(message = "client_id 不能为空")
    @Schema(description = "客户端id")
    private String clientId;

    @NotBlank(message = "client_secret 不能为空")
    @Schema(description = "客户端密钥")
    private String clientSecret;

    @Schema(description = "资源id列表")
    private String resourceIds;

    @NotBlank(message = "scope 不能为空")
    @Schema(description = "作用域")
    private String scope;

    @Schema(description = "授权方式")
    private String authorizedGrantTypes;

    @Schema(description = "回调地址")
    private String webServerRedirectUri;

    @Schema(description = "权限列表")
    private String authorities;

    @Schema(description = "请求令牌有效时间")
    private Integer accessTokenValidity;

    @Schema(description = "刷新令牌有效时间")
    private Integer refreshTokenValidity;

    @Schema(description = "扩展信息")
    private String additionalInformation;

    @Schema(description = "是否自动放行")
    private String autoapprove;

    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标记,1:已删除,0:正常")
    private String delFlag;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
