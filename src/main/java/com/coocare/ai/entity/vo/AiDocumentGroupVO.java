package com.coocare.ai.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * AI文档分组响应VO
 * 用于按第三层分类分组展示文档
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "AI文档分组响应VO")
public class AiDocumentGroupVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类编码")
    private String categoryCode;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "分类描述")
    private String categoryDescription;

    @Schema(description = "分类图标")
    private String categoryIcon;

    @Schema(description = "排序号")
    private Integer sortOrder;

    @Schema(description = "文档数量")
    private Integer documentCount;

    @Schema(description = "文档总大小（字节）")
    private Long totalFileSize;

    @Schema(description = "文档总大小（格式化显示）")
    private String totalFileSizeFormatted;

    @Schema(description = "该分类下的文档列表")
    private List<AiDocumentVO> documents;

    /**
     * 格式化文件总大小
     */
    public String getTotalFileSizeFormatted() {
        if (totalFileSize == null || totalFileSize == 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = totalFileSize.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }

    /**
     * 获取文档数量
     */
    public Integer getDocumentCount() {
        if (documents == null) {
            return 0;
        }
        return documents.size();
    }

    /**
     * 计算文档总大小
     */
    public Long getTotalFileSize() {
        if (documents == null || documents.isEmpty()) {
            return 0L;
        }
        return documents.stream()
                .mapToLong(doc -> doc.getFileSize() != null ? doc.getFileSize() : 0L)
                .sum();
    }
}
