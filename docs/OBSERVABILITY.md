# 可观测性设计

## 指标（Metrics）
- 业务：接口 QPS、错误率、P95/P99、上传成功率、文档数/分类数
- 资源：JVM（堆/GC/线程）、连接池、Redis 命中率、DB 慢查询
- 告警阈值：错误率>2%，P95>1s，GC 频繁

## 日志（Logging）
- 统一 JSON 布局；携带 traceId、userId、ip、uri、cost 等
- 日志分级：error/warn/info/debug，敏感信息脱敏

## 链路追踪（Tracing）
- OpenTelemetry 采样策略：默认 0.1–1% 采样，错误强制采样
- 关键链路：登录、初始化、文件上传、Dify 调用

## 实施建议
- Spring Boot Actuator + Micrometer 暴露指标
- 接入 Prometheus + Grafana；OTel Collector + Tempo/Jaeger

