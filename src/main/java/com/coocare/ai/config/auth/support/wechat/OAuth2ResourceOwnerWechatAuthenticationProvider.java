//package com.coocare.ai.config.auth.support.wechat;
/**
 * 微信登录认证提供者（示例/占位）
 * 若启用请实现构造与接口逻辑
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
//
//import com.coocare.ai.config.auth.support.base.OAuth2ResourceOwnerBaseAuthenticationProvider;
//import com.coocare.ai.config.constants.SecurityConstants;
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//import org.springframework.security.authentication.AuthenticationManager;
//import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
//import org.springframework.security.oauth2.core.AuthorizationGrantType;
//import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
//import org.springframework.security.oauth2.core.OAuth2ErrorCodes;
//import org.springframework.security.oauth2.core.OAuth2Token;
//import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
//import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
//import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenGenerator;
//
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @date date
// *
// * 短信登录的核心处理
// */
//public class OAuth2ResourceOwnerWechatAuthenticationProvider
//		extends OAuth2ResourceOwnerBaseAuthenticationProvider<OAuth2ResourceOwnerWechatAuthenticationToken> {
//
//	private static final Logger LOGGER = LogManager.getLogger(OAuth2ResourceOwnerWechatAuthenticationProvider.class);
//
//	/**
//	 * Constructs an {@code OAuth2AuthorizationCodeAuthenticationProvider} using the
//	 * provided parameters.
//	 * @param authenticationManager
//	 * @param authorizationService the authorization service
//	 * @param tokenGenerator the token generator
//	 * @since 0.2.3
//	 */
//	public OAuth2ResourceOwnerWechatAuthenticationProvider(AuthenticationManager authenticationManager,
//			OAuth2AuthorizationService authorizationService,
//			OAuth2TokenGenerator<? extends OAuth2Token> tokenGenerator) {
//		super(authenticationManager, authorizationService, tokenGenerator);
//	}
//
//	@Override
//	public boolean supports(Class<?> authentication) {
//		boolean supports = OAuth2ResourceOwnerWechatAuthenticationToken.class.isAssignableFrom(authentication);
//		LOGGER.debug("supports authentication=" + authentication + " returning " + supports);
//		return supports;
//	}
//
//	@Override
//	public void checkClient(RegisteredClient registeredClient) {
//		assert registeredClient != null;
//		if (!registeredClient.getAuthorizationGrantTypes()
//			.contains(new AuthorizationGrantType(SecurityConstants.WECHAT))) {
//			throw new OAuth2AuthenticationException(OAuth2ErrorCodes.UNAUTHORIZED_CLIENT);
//		}
//	}
//
//	@Override
//	public UsernamePasswordAuthenticationToken buildToken(Map<String, Object> reqParameters) {
//		String code = (String) reqParameters.get(SecurityConstants.WECHAT_PARAMETER_NAME);
//		return new UsernamePasswordAuthenticationToken(code, null);
//	}
//
//}
