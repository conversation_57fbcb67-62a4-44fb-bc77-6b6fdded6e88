package com.coocare.ai.config.oss;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * OSS 自动配置
 * 在开启 oss.enable 时装配 FileTemplate
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties({OssProperties.class})
@ConditionalOnProperty(name = "oss.enable", havingValue = "true")
public class OssAutoConfiguration {

    private final OssProperties properties;

    @Bean
    @Primary
    @ConditionalOnMissingBean(OssTemplate.class)
    public FileTemplate ossTemplate() {
        return new OssTemplate(properties);
    }

}
