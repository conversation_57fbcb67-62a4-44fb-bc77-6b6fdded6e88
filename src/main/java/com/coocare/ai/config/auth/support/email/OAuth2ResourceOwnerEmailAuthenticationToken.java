package com.coocare.ai.config.auth.support.email;

import com.coocare.ai.config.auth.support.base.OAuth2ResourceOwnerBaseAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.AuthorizationGrantType;

import java.util.Map;
import java.util.Set;

/**
 * 邮箱验证码登录认证Token
 *
 * <AUTHOR>
 */
public class OAuth2ResourceOwnerEmailAuthenticationToken extends OAuth2ResourceOwnerBaseAuthenticationToken {

    /**
     * 邮箱验证码登录认证Token构造器
     *
     * @param authorizationGrantType 授权类型
     * @param clientPrincipal 客户端认证信息
     * @param scopes 作用域
     * @param additionalParameters 额外参数
     */
    public OAuth2ResourceOwnerEmailAuthenticationToken(AuthorizationGrantType authorizationGrantType,
                                                       Authentication clientPrincipal,
                                                       Set<String> scopes,
                                                       Map<String, Object> additionalParameters) {
        super(authorizationGrantType, clientPrincipal, scopes, additionalParameters);
    }
}
