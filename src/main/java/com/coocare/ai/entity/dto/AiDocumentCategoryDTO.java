package com.coocare.ai.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * AI文档分类请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Schema(description = "AI文档分类请求DTO")
public class AiDocumentCategoryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类ID（更新时必填）")
    private Long categoryId;

    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    private String categoryName;

    @Schema(description = "分类编码")
    @Size(max = 50, message = "分类编码长度不能超过50个字符")
    private String categoryCode;

    @Schema(description = "父级分类ID")
    private Long parentId;

    @Schema(description = "分类层级：1-产品型号，2-产品系列，3-文档类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分类层级不能为空")
    private Integer level;

    @Schema(description = "排序号")
    private Integer sortOrder;

    @Schema(description = "分类描述")
    @Size(max = 500, message = "分类描述长度不能超过500个字符")
    private String description;

    @Schema(description = "分类图标")
    @Size(max = 200, message = "分类图标长度不能超过200个字符")
    private String icon;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;
}
