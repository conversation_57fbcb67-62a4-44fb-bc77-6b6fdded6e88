# OAuth2 认证服务器测试指南

## 概述

本项目已完成 Spring Security OAuth2 Authorization Server 的配置，支持以下认证方式：
- 密码登录
- 邮箱验证码登录
- 短信验证码登录

所有认证方式都返回 Bearer 格式的 token。

## 前置准备

### 1. 数据库配置

执行 `db/oauth_client_setup.sql` 脚本，创建 OAuth 客户端配置：

```sql
-- 执行此脚本创建客户端配置
source db/oauth_client_setup.sql;
```

### 2. 邮件服务配置

在 `application.yml` 中配置邮件服务：

```yaml
spring:
  mail:
    host: smtp.qq.com  # 邮件服务器
    port: 587
    username: <EMAIL>  # 发送邮箱
    password: your-app-password  # 邮箱授权码
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

### 3. 创建测试用户

确保数据库中有测试用户，包含邮箱字段。

## API 测试

### 1. 密码登录

**接口**: `POST /oauth2/token/password`

**参数**:
- `username`: 用户名
- `password`: 密码
- `clientId`: 客户端ID（默认：genius）
- `clientSecret`: 客户端密钥（默认：genius）

**示例请求**:
```bash
curl -X POST "http://localhost:8070/oauth2/token/password" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=123456&clientId=genius&clientSecret=genius"
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 43199,
    "scope": "server"
  }
}
```

### 2. 邮箱验证码登录

#### 步骤1：发送邮箱验证码

**接口**: `POST /common/email/send`

**参数**:
- `email`: 邮箱地址

**示例请求**:
```bash
curl -X POST "http://localhost:8070/common/email/send" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=<EMAIL>"
```

#### 步骤2：使用验证码登录

**接口**: `POST /oauth2/token/email`

**参数**:
- `email`: 邮箱地址
- `emailCode`: 验证码
- `clientId`: 客户端ID（默认：genius）
- `clientSecret`: 客户端密钥（默认：genius）

**示例请求**:
```bash
curl -X POST "http://localhost:8070/oauth2/token/email" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=<EMAIL>&emailCode=123456&clientId=genius&clientSecret=genius"
```

### 3. 直接调用 OAuth2 Token 端点

#### 密码模式

```bash
curl -X POST "http://localhost:8070/oauth2/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Basic Z2VuaXVzOmdlbml1cw==" \
  -d "grant_type=password&username=admin&password=123456"
```

#### 邮箱验证码模式

```bash
curl -X POST "http://localhost:8070/oauth2/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Basic Z2VuaXVzOmdlbml1cw==" \
  -d "grant_type=urn:ietf:params:oauth:grant-type:email_code&email=<EMAIL>&email_captcha=123456"
```

#### 短信验证码模式

```bash
curl -X POST "http://localhost:8070/oauth2/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Basic Z2VuaXVzOmdlbml1cw==" \
  -d "grant_type=mobile&mobile=13800138000&code=123456"
```

#### 刷新 Token

```bash
curl -X POST "http://localhost:8070/oauth2/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Basic Z2VuaXVzOmdlbml1cw==" \
  -d "grant_type=refresh_token&refresh_token=YOUR_REFRESH_TOKEN"
```

### 4. 使用 Bearer Token 访问受保护资源

```bash
curl -X GET "http://localhost:8070/api/protected-resource" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 配置说明

### OAuth2 客户端配置

| 客户端ID | 支持的授权类型 | 用途 |
|---------|---------------|------|
| genius | password, refresh_token, mobile, email_code | 默认客户端 |
| web | password, refresh_token, authorization_code, email_code | Web 应用 |
| mobile | password, refresh_token, mobile, email_code | 移动应用 |

### Token 配置

- **Access Token 有效期**: 12小时（43200秒）
- **Refresh Token 有效期**: 30天（2592000秒）
- **Token 格式**: Bearer
- **Token 存储**: Redis

### 邮箱验证码配置

- **验证码长度**: 6位数字
- **有效期**: 5分钟
- **存储前缀**: `email_code:`
- **Redis Key**: `email_code:{email}`

## 故障排除

### 1. 客户端认证失败

检查客户端ID和密钥是否正确，确保数据库中存在对应的客户端配置。

### 2. 邮箱验证码发送失败

检查邮件服务配置，确保邮箱和授权码正确。

### 3. 用户不存在

确保数据库中存在对应的用户记录，且邮箱字段不为空。

### 4. Token 格式错误

所有 Token 都应该是 Bearer 格式，在请求头中使用：
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 安全建议

1. 在生产环境中使用强密码作为客户端密钥
2. 定期轮换客户端密钥
3. 设置合适的 Token 有效期
4. 使用 HTTPS 传输敏感信息
5. 实施速率限制防止暴力攻击
