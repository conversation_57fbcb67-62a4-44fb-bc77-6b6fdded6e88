package com.coocare.ai.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.entity.sys.SysConfig;
import com.coocare.ai.listener.ConfigUpdateListener;
import com.coocare.ai.mapper.SysConfigMapper;
import com.coocare.ai.service.SysConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Service
@RequiredArgsConstructor
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements SysConfigService {

    private final ConfigUpdateListener configUpdateListener;

    @Override
//    @Caching(evict = {
//            @CacheEvict(cacheNames = "SysConfigObject", key = "#key"),
//            @CacheEvict(cacheNames = "SysConfig", key = "#key")
//    })
    public void updateValueByKey(String key, String value) {
        update(Wrappers.lambdaUpdate(SysConfig.class).set(SysConfig::getParamValue, value).eq(SysConfig::getParamKey, key));

        // 更新全局配置缓存
        configUpdateListener.handleConfigUpdate(key, value);
    }

    @Override
    public void deleteBatch(Long[] ids) {
        removeByIds(Arrays.asList(ids));
    }

    @Override
//    @Cacheable(cacheNames = "SysConfig", key = "#key")
    public String getValue(String key) {
        SysConfig config = getOne(Wrappers.<SysConfig>lambdaQuery().eq(SysConfig::getParamKey, key));
        return config == null ? null : config.getParamValue();
    }

    @Override
//    @Caching(evict = {
//            @CacheEvict(cacheNames = "SysConfigObject", key = "#key"),
//            @CacheEvict(cacheNames = "SysConfig", key = "#key")
//    })
    public void removeSysConfig(String key) {
        remove(Wrappers.<SysConfig>lambdaQuery().eq(SysConfig::getParamKey, key));

        // 从全局配置缓存中移除
        configUpdateListener.handleConfigDelete(key);
    }


    @Override
//    @Caching(evict = {
//            @CacheEvict(cacheNames = "SysConfigObject", key = "#sysConfig.paramKey"),
//            @CacheEvict(cacheNames = "SysConfig", key = "#sysConfig.paramKey")
//    })
    public void saveOrUpdateSysConfigByKey(SysConfig sysConfig) {
        long count = count(Wrappers.lambdaQuery(SysConfig.class)
                .eq(SysConfig::getParamKey, sysConfig.getParamKey())
        );
        if (count > 0) {
            updateValueByKey(sysConfig.getParamKey(), sysConfig.getParamValue());
        } else {
            sysConfig.setConfigId(null);
            save(sysConfig);

            // 新增配置时也要更新全局配置缓存
            configUpdateListener.handleConfigUpdate(sysConfig.getParamKey(), sysConfig.getParamValue());
        }
    }


    @Override
//    @Cacheable(cacheNames = "SysConfigObject", key = "#key")
    public <T> T getSysConfigObject(String key, Class<T> clazz) {
        String value = getValue(key);
        if (StrUtil.isBlank(value)) {
            return null;
        }
        String className = "java.lang.String";
        if (className.equals(clazz.getName())) {
            return (T) value;
        } else {
            return JSON.parseObject(value, clazz);
        }
    }



}
