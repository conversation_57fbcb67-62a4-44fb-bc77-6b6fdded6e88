package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 部门AI智能体关联表
 */
@Schema(description = "部门AI智能体关联表")
@Data
@TableName(value = "ai_dept_agent")
public class AiDeptAgent implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 关联ID
     */
    @TableId(value = "dept_agent_id", type = IdType.AUTO)
    @Schema(description = "关联ID")
    private Long deptAgentId;

    /**
     * 部门ID
     */
    @TableField(value = "dept_id")
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 智能体ID
     */
    @TableField(value = "agent_id")
    @Schema(description = "智能体ID")
    private Long agentId;

    /**
     * 是否默认智能体：0-否，1-是
     */
    @TableField(value = "is_default")
    @Schema(description = "是否默认智能体：0-否，1-是")
    private Boolean isDefault;

    /**
     * 排序号
     */
    @TableField(value = "sort_order")
    @Schema(description = "排序号")
    private Integer sortOrder;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标志：0-正常，1-删除
     */
    @TableField(value = "del_flag")
    @Schema(description = "删除标志：0-正常，1-删除")
    private String delFlag;
}