package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI智能体表
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Schema(description = "AI智能体表")
@Data
@TableName(value = "ai_agent")
public class AiAgent implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 智能体ID
     */
    @TableId(value = "agent_id", type = IdType.ASSIGN_ID)
    @Schema(description = "智能体ID")
    private Long agentId;

    /**
     * 智能体名称
     */
    @TableField(value = "agent_name")
    @Schema(description = "智能体名称")
    private String agentName;

    /**
     * 智能体介绍
     */
    @TableField(value = "agent_intro")
    @Schema(description = "智能体介绍")
    private String agentIntro;

    /**
     * 智能体详细描述
     */
    @TableField(value = "agent_description")
    @Schema(description = "智能体详细描述")
    private String agentDescription;

    /**
     * 智能体类型：GENERAL-通用，SPECIALIZED-专业
     */
    @TableField(value = "agent_type")
    @Schema(description = "智能体类型：GENERAL-通用，SPECIALIZED-专业")
    private String agentType;

    /**
     * 智能体配置信息（JSON格式）
     */
    @TableField(value = "agent_config")
    @Schema(description = "智能体配置信息（JSON格式）")
    private String agentConfig;

    /**
     * 是否启用：0-禁用，1-启用
     */
    @TableField(value = "`enable`")
    @Schema(description = "是否启用：0-禁用，1-启用")
    private Byte enable;

    /**
     * 排序号
     */
    @TableField(value = "sort_order")
    @Schema(description = "排序号")
    private Integer sortOrder;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标志：0-正常，1-删除
     */
    @TableField(value = "del_flag")
    @Schema(description = "删除标志：0-正常，1-删除")
    private String delFlag;
}