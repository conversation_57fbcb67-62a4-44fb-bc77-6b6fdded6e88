package com.coocare.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocare.ai.entity.sys.SysRole;
import com.coocare.ai.entity.sys.SysUser;
import com.coocare.ai.entity.sys.dto.*;
import com.coocare.ai.entity.sys.vo.UserDeptVO;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;

import java.util.List;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 分页查询用户列表
     *
     * @param searchWord
     * @param status
     * @param pageDomain
     * @return
     */
    PageUtils queryPage(String searchWord, Boolean status, PageDomain pageDomain);

    /**
     * 根据用户名查询用户
     *
     * @param username
     * @return
     */
    SysUser queryUserByName(String username);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    SysUser queryUserByEmail(String email);

    /**
     * 新增用户
     *
     * @param userDTO
     * @return
     */
    Long saveUser(UserDTO userDTO);

    /**
     * 初始化密码
     *
     * @param userId
     * @return
     */
    Boolean resetPassword(Long userId);

    /**
     * 修改用户密码
     *
     * @param oldPass
     * @param newPass
     * @return
     */
    Boolean changeUserPass(String oldPass, String newPass);

    /**
     * 修改用户信息
     *
     * @param userDTO
     * @return
     */
    Boolean updateUser(UserDTO userDTO);

    /**
     * 状态切换
     *
     * @param id
     * @param status
     * @return
     */
    Boolean change(Long id, Boolean status);

    /**
     * 分配角色
     *
     * @return
     */
    Object assignRoles(UserRoleAssignDTO dto);

    /**
     * 获取登录用户的信息
     *
     * @param userId
     * @return
     */
    UserInfo userInfo(Long userId);

    /**
     * 根据用户名加载用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    UserInfo loadUserByUsername(String username);

    /**
     * 根据邮箱加载用户信息
     *
     * @param email 邮箱
     * @return 用户信息
     */
    UserInfo loadUserByEmail(String email);

    /**
     * 获取用户角色
     *
     * @param id
     * @return
     */
    List<SysRole> getRolesByUser(Long id);

    /**
     * 校验密码
     *
     * @param password
     * @return
     */
    Boolean checkPassword(String password);

    /**
     * 根据部门ID查询用户列表
     * @param deptId 部门ID
     * @param includeChildren 是否包含子部门用户
     * @return 用户列表
     */
    List<UserDeptVO> getUsersByDeptId(Long deptId, boolean includeChildren);

    /**
     * 分页查询部门用户
     * @param deptId 部门ID
     * @param searchWord 搜索关键词
     * @param includeChildren 是否包含子部门用户
     * @param pageDomain 分页参数
     * @return 分页结果
     */
    PageUtils queryDeptUsersPage(Long deptId, String searchWord, boolean includeChildren, PageDomain pageDomain);

    /**
     * 设置用户部门
     * @param userDeptDTO 用户部门DTO
     * @return 设置结果
     */
    boolean setUserDept(UserDeptDTO userDeptDTO);

    /**
     * 批量设置用户部门
     * @param userDeptDTO 用户部门DTO
     * @return 设置结果
     */
    boolean batchSetUserDept(UserDeptDTO userDeptDTO);

    /**
     * 设置部门主管
     * @param deptManagerDTO 部门主管DTO
     * @return 设置结果
     */
    boolean setDeptManager(DeptManagerDTO deptManagerDTO);

    /**
     * 移除部门主管
     * @param deptManagerDTO 部门主管DTO
     * @return 移除结果
     */
    boolean removeDeptManager(DeptManagerDTO deptManagerDTO);

    /**
     * 获取部门主管列表
     * @param deptId 部门ID
     * @return 部门主管列表
     */
    List<UserDeptVO> getDeptManagers(Long deptId);

    /**
     * 转移用户到其他部门
     * @param userIds 用户ID列表
     * @param fromDeptId 原部门ID
     * @param toDeptId 目标部门ID
     * @return 转移结果
     */
    boolean transferUsersToDept(List<Long> userIds, Long fromDeptId, Long toDeptId);

    /**
     * 从部门中移除用户
     * @param userIds 用户ID列表
     * @param deptId 部门ID
     * @return 移除结果
     */
    boolean removeUsersFromDept(List<Long> userIds, Long deptId);
}
