# 数据权限模型

## 范畴
- 功能权限：基于角色-菜单（接口/按钮）授权
- 数据权限：基于部门与用户维度对数据可见性的限制

## 数据范围（DataScope）
- ALL：全部数据
- DEPT: 本部门数据
- DEPT_AND_SUB: 本部门及下级部门
- SELF: 仅本人数据
- CUSTOM: 自定义部门集合

## 设计思路
- 用户具备多个角色，取最大范围（或并集）
- 通过 AOP/注解 或 Query Wrapper 工具在 Mapper/Service 层注入数据范围条件
- 自定义范围通过角色-部门关联表实现

## 参考实现（伪代码）
```java
// Service 层
@DataScope(type = DataScopeType.DEPT_AND_SUB, deptAlias = "d", userAlias = "u")
public Page<UserVO> pageUsers(UserQuery q) { ... }

// AOP 注入 SQL 片段
WHERE (
  ${deptAlias}.dept_id IN (select id from sys_dept where find_in_set(id, getDeptTree(${currentDeptId})))
  OR ${userAlias}.user_id = ${currentUserId}
)
```

## Mapper 层实现模式（MyBatis-Plus）
```java
QueryWrapper<SysUser> qw = new QueryWrapper<>();
// 注入 dataScope 条件
DataScopeHelper.apply(qw, currentUser);
return userMapper.selectPage(page, qw);
```

## 注意事项
- 与逻辑删除条件（del_flag=0）共同生效
- 跨表查询需在主表或关联表上注入正确的别名
- 若存在缓存，需考虑数据权限对缓存的隔离（按角色/部门/用户维度）

