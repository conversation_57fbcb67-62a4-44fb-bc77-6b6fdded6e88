package com.coocare.ai.utils;

import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 字符串工具类
 * 提供常用字符串处理方法；例如将逗号分隔的字符串安全转换为 Long 列表（忽略非法项）
 * <p>
 * 注意：方法均为无状态静态方法，线程安全
 *
 * <AUTHOR>
 * @since 2025-08-19
 */

public class StringUtil {

    /**
     * 转换逗号分隔的long字符串为Long数字
     *
     * @param strs
     * @return
     */
    public static List<Long> parseLong(String strs) {
        List<Long> ls = new ArrayList<>();
        if (!StringUtils.isEmpty(strs)) {
            for (String str : strs.split(",")) {
                try {
                    ls.add(Long.valueOf(str));
                } catch (NumberFormatException e) {
                }
            }
        }
        return ls;
    }
}
