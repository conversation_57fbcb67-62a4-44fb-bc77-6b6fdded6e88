<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocare.ai.mapper.AiDeptAgentMapper">
  <resultMap id="BaseResultMap" type="com.coocare.ai.entity.AiDeptAgent">
    <!--@mbg.generated-->
    <!--@Table ai_dept_agent-->
    <id column="dept_agent_id" jdbcType="BIGINT" property="deptAgentId" />
    <result column="dept_id" jdbcType="BIGINT" property="deptId" />
    <result column="agent_id" jdbcType="BIGINT" property="agentId" />
    <result column="is_default" jdbcType="TINYINT" property="isDefault" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    dept_agent_id, dept_id, agent_id, is_default, sort_order, create_by, create_time, 
    update_by, update_time, del_flag
  </sql>

    <!-- AI智能体映射结果 -->
    <resultMap id="AgentResultMap" type="com.coocare.ai.entity.AiAgent">
        <id column="agent_id" property="agentId" />
        <result column="agent_name" property="agentName" />
        <result column="agent_intro" property="agentIntro" />
        <result column="enable" property="enable" />
    </resultMap>

    <!-- 根据部门ID查询关联的AI智能体列表 -->
    <select id="selectAgentsByDeptId" resultMap="AgentResultMap">
        SELECT a.agent_id, a.agent_name, a.agent_intro, a.enable
        FROM ai_agent a
        INNER JOIN ai_dept_agent da ON a.agent_id = da.agent_id
        WHERE da.dept_id = #{deptId}
          AND da.del_flag = '0'
          AND a.del_flag = '0'
          AND a.enable = 1
        ORDER BY da.sort_order ASC, da.create_time ASC
    </select>

    <!-- 根据部门ID查询默认AI智能体 -->
    <select id="selectDefaultAgentByDeptId" resultMap="AgentResultMap">
        SELECT a.agent_id, a.agent_name, a.agent_intro, a.enable
        FROM ai_agent a
        INNER JOIN ai_dept_agent da ON a.agent_id = da.agent_id
        WHERE da.dept_id = #{deptId}
          AND da.is_default = 1
          AND da.del_flag = '0'
          AND a.del_flag = '0'
          AND a.enable = 1
        LIMIT 1
    </select>

    <!-- 根据智能体ID查询关联的部门ID列表 -->
    <select id="selectDeptIdsByAgentId" resultType="java.lang.Long">
        SELECT da.dept_id
        FROM ai_dept_agent da
        WHERE da.agent_id = #{agentId}
          AND da.del_flag = '0'
    </select>

    <!-- 删除部门的所有AI智能体关联 -->
    <update id="deleteByDeptId">
        UPDATE ai_dept_agent
        SET del_flag = '1', update_time = NOW()
        WHERE dept_id = #{deptId}
          AND del_flag = '0'
    </update>

    <!-- 批量插入部门AI智能体关联 -->
    <insert id="batchInsert">
        INSERT INTO ai_dept_agent (dept_id, agent_id, is_default, sort_order, create_by, create_time, del_flag)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId}, #{item.agentId}, #{item.isDefault}, #{item.sortOrder}, #{item.createBy}, NOW(), '0')
        </foreach>
    </insert>

</mapper>