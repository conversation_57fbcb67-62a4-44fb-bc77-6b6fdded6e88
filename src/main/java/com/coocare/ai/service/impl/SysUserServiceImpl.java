package com.coocare.ai.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.config.auth.reource.util.SecurityUtils;
import com.coocare.ai.config.constants.CacheConstants;
import com.coocare.ai.config.constants.CommonConstants;
import com.coocare.ai.config.exception.RRException;
import com.coocare.ai.entity.sys.*;
import com.coocare.ai.entity.sys.dto.*;
import com.coocare.ai.entity.sys.vo.UserDeptVO;
import com.coocare.ai.mapper.SysUserMapper;
import com.coocare.ai.service.*;
import com.coocare.ai.utils.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    private final SysUserRoleService userRoleService;
    private final SysRoleService roleService;
    private final SysMenuService menuService;
    private final SysDeptService deptService;

    @Override
    public PageUtils queryPage(String searchWord, Boolean status, PageDomain pageDomain) {
        IPage<SysUser> page = this.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()),
                new LambdaQueryWrapper<SysUser>().like(StringUtils.isNoneEmpty(searchWord), SysUser::getUsername, searchWord)
                        .or(StringUtils.isNoneEmpty(searchWord))
                        .like(StringUtils.isNoneEmpty(searchWord), SysUser::getNickname, searchWord));
        page.setRecords(page.getRecords().stream().map(user -> {
            UserDTO userDto = new UserDTO();
            BeanUtils.copyProperties(user, userDto);
            List<String> roleList = roleService.getRoleByUid(user.getUserId()).stream().map(role -> role.getRoleId() + "").collect(Collectors.toList());
            userDto.setRole(String.join(",", roleList));
            return userDto;
        }).collect(Collectors.toList()));
        return new PageUtils(page);
    }

    @Override
    public SysUser queryUserByName(String username) {
        return this.baseMapper.selectOne(new QueryWrapper<SysUser>().eq("username", username));
    }

    @Override
    public SysUser queryUserByEmail(String email) {
        return this.baseMapper.selectOne(new QueryWrapper<SysUser>().eq("email", email));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = CacheConstants.USER_DETAILS, allEntries = true)
    public Long saveUser(UserDTO userDto) {
        String pass = StringUtils.isEmpty(userDto.getPassword()) ? ParamResolver.getStr("USER_DEFAULT_PASSWORD", "12345678") : userDto.getPassword();
        userDto.setPassword(CommonConstants.ENCODER.encode(pass));
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userDto, user);

        // 如果角色为空，赋默认角色
        if (EmptyUtil.isEmpty(userDto.getRole())) {
            // 获取默认角色编码
            String defaultRole = ParamResolver.getStr("USER_DEFAULT_ROLE");
            // 默认角色
            SysRole sysRole = roleService.getOne(Wrappers.<SysRole>lambdaQuery().eq(SysRole::getRoleCode, defaultRole));
            userDto.setRole(sysRole.getRoleId() + "");
        }

        // 插入用户角色关系表
        List<Long> ids = StringUtil.parseLong(userDto.getRole());
        ids.stream().map(roleId -> {
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(user.getUserId());
            userRole.setRoleId(roleId);
            return userRole;
        }).forEach(userRoleService::save);

        save(user);
        return user.getUserId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetPassword(Long userId) {
        SysUser user = this.baseMapper.selectById(userId);
        user.setPassword(CommonConstants.ENCODER.encode("12345678"));
        return updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeUserPass(String oldPass, String newPass) {
        if (!checkPassword(oldPass)) {
            throw new RRException("user.password.error");
        } else if (checkPassword(newPass)) {
            throw new RRException("user.password.check");
        } else {
            return update(Wrappers.lambdaUpdate(SysUser.class).set(SysUser::getPassword, CommonConstants.ENCODER.encode(newPass)).eq(SysUser::getUserId, SecurityUtils.getUser().getId()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = CacheConstants.USER_DETAILS, allEntries = true)
    public Boolean updateUser(UserDTO userDTO) {
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userDTO, user);
        if (StrUtil.isNotBlank(userDTO.getPassword())) {
            user.setPassword(CommonConstants.ENCODER.encode(userDTO.getPassword()));
        }

        if (EmptyUtil.isEmpty(userDTO.getRole())) {
            userRoleService.remove(Wrappers.lambdaQuery(SysUserRole.class).eq(SysUserRole::getUserId, user.getUserId()));
            userRoleService.saveBatch(StringUtil.parseLong(userDTO.getRole()).stream().map(role -> {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(user.getUserId());
                sysUserRole.setRoleId(role);
                return sysUserRole;
            }).collect(Collectors.toList()));
        }
        return updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = CacheConstants.USER_DETAILS, allEntries = true)
    public Boolean change(Long id, Boolean status) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(id);
        sysUser.setEnable(status);
        return this.baseMapper.updateById(sysUser) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Object assignRoles(UserRoleAssignDTO dto) {
        var query = Wrappers.lambdaQuery(SysUserRole.class);
        query.eq(SysUserRole::getUserId, dto.getUserId());
        userRoleService.remove(query);
        var list = dto.getRoleIds().stream().map(roleId -> {
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(dto.getUserId());
            sysUserRole.setRoleId(roleId);
            return sysUserRole;
        }).collect(Collectors.toList());
        return userRoleService.saveBatch(list);
    }

    @Override
    @Cacheable(value = CacheConstants.USER_DETAILS, key = "#userId", unless = "#result == null ")
    public UserInfo userInfo(Long userId) {
        SysUser user = getById(userId);
        Optional.ofNullable(user).orElseThrow(() -> new RRException("user.not.exist"));

        UserInfo userInfo = new UserInfo();
        userInfo.setSysUser(user);
        // 设置角色列表 （ID）
        List<Long> roleIds = roleService.getRoleByUid(userId)
                .stream()
                .map(SysRole::getRoleId)
                .collect(Collectors.toList());
        userInfo.setRoles(ArrayUtil.toArray(roleIds, Long.class));

        // 设置权限列表（menu.permission）
        Set<String> permissions = new HashSet<>();
        roleIds.forEach(roleId -> {
            List<String> permissionList = menuService.findMenuByRoleId(roleId)
                    .stream()
                    .map(SysMenu::getPermission)
                    .filter(StrUtil::isNotEmpty)
                    .toList();
            permissions.addAll(permissionList);
        });
        userInfo.setPermissions(ArrayUtil.toArray(permissions, String.class));
        return userInfo;
    }

    @Override
    public UserInfo loadUserByUsername(String username) {
        SysUser user = queryUserByName(username);
        if (user == null) {
            throw new RRException("user.not.exist");
        }

        UserInfo userInfo = new UserInfo();
        userInfo.setSysUser(user);
        // 设置角色列表 （ID）
        List<Long> roleIds = roleService.getRoleByUid(user.getUserId())
                .stream()
                .map(SysRole::getRoleId)
                .collect(Collectors.toList());
        userInfo.setRoles(ArrayUtil.toArray(roleIds, Long.class));

        // 设置权限列表（permission）
        //Set<String> permissions = menuService.findMenuByRoleId(roleIds)
        //        .stream()
        //        .filter(Objects::nonNull)
        //        .map(SysMenu::getPermission)
        //        .filter(StringUtils::isNotEmpty)
        //        .collect(Collectors.toSet());

        //userInfo.setPermissions(ArrayUtil.toArray(permissions, String.class));
        return userInfo;
    }

    @Override
    public UserInfo loadUserByEmail(String email) {
        SysUser user = queryUserByEmail(email);
        if (user == null) {
            throw new RRException("user.not.exist");
        }

        UserInfo userInfo = new UserInfo();
        userInfo.setSysUser(user);
        // 设置角色列表 （ID）
        List<Long> roleIds = roleService.getRoleByUid(user.getUserId())
                .stream()
                .map(SysRole::getRoleId)
                .collect(Collectors.toList());
        userInfo.setRoles(ArrayUtil.toArray(roleIds, Long.class));

        // 设置权限列表（permission）
        //Set<String> permissions = menuService.findMenuByRoleId(roleIds)
        //        .stream()
        //        .filter(Objects::nonNull)
        //        .map(SysMenu::getPermission)
        //        .filter(StringUtils::isNotEmpty)
        //        .collect(Collectors.toSet());
        //
        //userInfo.setPermissions(ArrayUtil.toArray(permissions, String.class));
        return userInfo;
    }

    @Override
    public List<SysRole> getRolesByUser(Long id) {
        var sysUserRoles = userRoleService.list(Wrappers.lambdaQuery(SysUserRole.class).eq(SysUserRole::getUserId, id));
        if (ObjectUtil.isEmpty(sysUserRoles)) {
            return Collections.emptyList();
        }
        var query = Wrappers.lambdaQuery(SysRole.class);
        query.in(SysRole::getRoleId, sysUserRoles.stream().map(SysUserRole::getRoleId).collect(Collectors.toList()));
        return roleService.list(query);
    }

    @Override
    public Boolean checkPassword(String password) {
        return CommonConstants.ENCODER.encode(password).equals(SecurityUtils.getUser().getPassword());
    }

    @Override
    public List<UserDeptVO> getUsersByDeptId(Long deptId, boolean includeChildren) {
        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getDelFlag, "0");

        if (includeChildren) {
            // 获取所有子部门ID
            List<Long> deptIds = getAllChildDeptIds(deptId);
            deptIds.add(deptId);
            wrapper.in(SysUser::getDeptId, deptIds);
        } else {
            wrapper.eq(SysUser::getDeptId, deptId);
        }

        List<SysUser> users = this.list(wrapper);
        return users.stream().map(this::convertToUserDeptVO).collect(Collectors.toList());
    }

    @Override
    public PageUtils queryDeptUsersPage(Long deptId, String searchWord, boolean includeChildren, PageDomain pageDomain) {
        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getDelFlag, "0");

        if (includeChildren) {
            // 获取所有子部门ID
            List<Long> deptIds = getAllChildDeptIds(deptId);
            deptIds.add(deptId);
            wrapper.in(SysUser::getDeptId, deptIds);
        } else {
            wrapper.eq(SysUser::getDeptId, deptId);
        }

        if (StringUtils.isNotEmpty(searchWord)) {
            wrapper.and(w -> w.like(SysUser::getUsername, searchWord)
                    .or().like(SysUser::getName, searchWord)
                    .or().like(SysUser::getNickname, searchWord)
                    .or().like(SysUser::getEmail, searchWord)
                    .or().like(SysUser::getMobile, searchWord));
        }

        Page<SysUser> page = this.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), wrapper);

        // 转换为UserDeptVO
        List<UserDeptVO> userDeptVOs = page.getRecords().stream()
                .map(this::convertToUserDeptVO)
                .collect(Collectors.toList());

        Page<UserDeptVO> resultPage = new Page<>();
        resultPage.setRecords(userDeptVOs);
        resultPage.setTotal(page.getTotal());
        resultPage.setCurrent(page.getCurrent());
        resultPage.setSize(page.getSize());

        return new PageUtils(resultPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setUserDept(UserDeptDTO userDeptDTO) {
        SysUser user = this.getById(userDeptDTO.getUserId());
        if (user == null) {
            return false;
        }

        // 清除原部门的主管标识
        if (user.getIsDeptManager() != null && user.getIsDeptManager() == 1) {
            user.setIsDeptManager(0);
        }

        // 设置新部门
        user.setDeptId(userDeptDTO.getDeptId());

        // 设置部门主管标识
        if (userDeptDTO.getIsDeptManager() != null && userDeptDTO.getIsDeptManager()) {
            user.setIsDeptManager(1);
            // 同时更新部门表的主管字段
            deptService.setDeptManager(userDeptDTO.getDeptId(), userDeptDTO.getUserId());
        }

        return this.updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSetUserDept(UserDeptDTO userDeptDTO) {
        if (CollUtil.isEmpty(userDeptDTO.getUserIds())) {
            return false;
        }

        List<SysUser> users = this.listByIds(userDeptDTO.getUserIds());
        for (SysUser user : users) {
            // 清除原部门的主管标识
            if (user.getIsDeptManager() != null && user.getIsDeptManager() == 1) {
                user.setIsDeptManager(0);
            }

            // 设置新部门
            user.setDeptId(userDeptDTO.getDeptId());
        }

        return this.updateBatchById(users);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDeptManager(DeptManagerDTO deptManagerDTO) {
        return deptService.setDeptManager(deptManagerDTO.getDeptId(), deptManagerDTO.getUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeDeptManager(DeptManagerDTO deptManagerDTO) {
        return deptService.removeDeptManager(deptManagerDTO.getDeptId());
    }

    @Override
    public List<UserDeptVO> getDeptManagers(Long deptId) {
        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery(SysUser.class)
                .eq(SysUser::getDeptId, deptId)
                .eq(SysUser::getIsDeptManager, 1)
                .eq(SysUser::getDelFlag, "0");

        List<SysUser> managers = this.list(wrapper);
        return managers.stream().map(this::convertToUserDeptVO).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean transferUsersToDept(List<Long> userIds, Long fromDeptId, Long toDeptId) {
        if (CollUtil.isEmpty(userIds)) {
            return false;
        }

        List<SysUser> users = this.listByIds(userIds);
        for (SysUser user : users) {
            // 验证用户是否属于原部门
            if (!fromDeptId.equals(user.getDeptId())) {
                continue;
            }

            // 清除部门主管标识
            if (user.getIsDeptManager() != null && user.getIsDeptManager() == 1) {
                user.setIsDeptManager(0);
            }

            // 转移到新部门
            user.setDeptId(toDeptId);
        }

        return this.updateBatchById(users);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUsersFromDept(List<Long> userIds, Long deptId) {
        if (CollUtil.isEmpty(userIds)) {
            return false;
        }

        List<SysUser> users = this.listByIds(userIds);
        for (SysUser user : users) {
            // 验证用户是否属于该部门
            if (!deptId.equals(user.getDeptId())) {
                continue;
            }

            // 清除部门主管标识
            if (user.getIsDeptManager() != null && user.getIsDeptManager() == 1) {
                user.setIsDeptManager(0);
            }

            // 移除部门关联
            user.setDeptId(null);
        }

        return this.updateBatchById(users);
    }

    /**
     * 获取所有子部门ID
     */
    private List<Long> getAllChildDeptIds(Long parentId) {
        List<Long> result = new ArrayList<>();
        List<SysDept> children = deptService.getChildrenByParentId(parentId);

        for (SysDept child : children) {
            result.add(child.getDeptId());
            // 递归获取子部门的子部门
            result.addAll(getAllChildDeptIds(child.getDeptId()));
        }

        return result;
    }

    /**
     * 转换为UserDeptVO
     */
    private UserDeptVO convertToUserDeptVO(SysUser user) {
        UserDeptVO userDeptVO = new UserDeptVO();
        BeanUtils.copyProperties(user, userDeptVO);

        // 获取部门信息
        if (user.getDeptId() != null) {
            SysDept dept = deptService.getById(user.getDeptId());
            userDeptVO.setDept(dept);
        }

        return userDeptVO;
    }

}
