//package com.coocare.ai.config.auth.support.wechat;
/**
 * 微信登录转换器（示例/占位）
 * 若启用请补全参数校验与 Token 构造
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
//
//import com.coocare.ai.config.auth.reource.util.OAuth2EndpointUtils;
//import com.coocare.ai.config.auth.support.base.OAuth2ResourceOwnerBaseAuthenticationConverter;
//import com.coocare.ai.config.constants.SecurityConstants;
//import jakarta.servlet.http.HttpServletRequest;
//import org.springframework.security.core.Authentication;
//import org.springframework.security.oauth2.core.AuthorizationGrantType;
//import org.springframework.security.oauth2.core.OAuth2ErrorCodes;
//import org.springframework.util.MultiValueMap;
//import org.springframework.util.StringUtils;
//
//import java.util.Map;
//import java.util.Set;
//
///**
// * <AUTHOR>
// * @date 2022-05-31
// *
// * 短信登录转换器
// */
//public class OAuth2ResourceOwnerWechatAuthenticationConverter
//		extends OAuth2ResourceOwnerBaseAuthenticationConverter<OAuth2ResourceOwnerWechatAuthenticationToken> {
//
//	/**
//	 * 是否支持此convert
//	 * @param grantType 授权类型
//	 * @return
//	 */
//	@Override
//	public boolean support(String grantType) {
//		return SecurityConstants.WECHAT.equals(grantType);
//	}
//
//	@Override
//	public OAuth2ResourceOwnerWechatAuthenticationToken buildToken(Authentication clientPrincipal, Set requestedScopes,
//                                                                Map additionalParameters) {
//		return new OAuth2ResourceOwnerWechatAuthenticationToken(new AuthorizationGrantType(SecurityConstants.APP),
//				clientPrincipal, requestedScopes, additionalParameters);
//	}
//
//	/**
//	 * 校验扩展参数 密码模式密码必须不为空
//	 * @param request 参数列表
//	 */
//	@Override
//	public void checkParams(HttpServletRequest request) {
//		MultiValueMap<String, String> parameters = OAuth2EndpointUtils.getParameters(request);
//		// CODE (REQUIRED)
//		String code = parameters.getFirst(SecurityConstants.WECHAT_PARAMETER_NAME);
//		if (!StringUtils.hasText(code) || parameters.get(SecurityConstants.WECHAT_PARAMETER_NAME).size() != 1) {
//			OAuth2EndpointUtils.throwError(OAuth2ErrorCodes.INVALID_REQUEST, SecurityConstants.WECHAT_PARAMETER_NAME,
//					OAuth2EndpointUtils.ACCESS_TOKEN_REQUEST_ERROR_URI);
//		}
//	}
//
//}
