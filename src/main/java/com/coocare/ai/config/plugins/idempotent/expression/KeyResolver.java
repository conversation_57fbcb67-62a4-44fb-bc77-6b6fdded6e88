package com.coocare.ai.config.plugins.idempotent.expression;

import com.coocare.ai.config.plugins.idempotent.annotation.Idempotent;
import org.aspectj.lang.JoinPoint;

/**
 * 幂等 Key 解析 SPI
 * 定义如何从方法/注解上下文解析幂等 Key
 *
 * <AUTHOR>
 * @since 2020-09-25
 */
public interface KeyResolver {

	/**
	 * 解析处理 key
	 * @param idempotent 接口注解标识
	 * @param point 接口切点信息
	 * @return 处理结果
	 */
	String resolver(Idempotent idempotent, JoinPoint point);

}
