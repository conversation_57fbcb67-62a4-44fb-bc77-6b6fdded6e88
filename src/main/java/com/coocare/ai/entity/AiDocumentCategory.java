package com.coocare.ai.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * AI文档分类实体类
 * 支持3层分类结构：
 * 1. 第一层：产品型号（如 HP EliteBook）
 * 2. 第二层：产品系列（如 HP EliteBook 6 G1i 13）
 * 3. 第三层：文档类型（产品资料、产品图库、产品彩页、产品证书、Quick Specs、图说产品）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_document_category")
@Schema(description = "AI文档分类管理")
public class AiDocumentCategory implements Serializable {

    /**
     * 分类层级常量
     */
    public static final int LEVEL_PRODUCT_MODEL = 1;  // 产品型号
    public static final int LEVEL_PRODUCT_SERIES = 2; // 产品系列
    public static final int LEVEL_DOCUMENT_TYPE = 3;  // 文档类型
    /**
     * 状态常量
     */
    public static final int STATUS_DISABLED = 0; // 禁用
    public static final int STATUS_ENABLED = 1;  // 启用
    /**
     * 系统分类常量
     */
    public static final int IS_SYSTEM_NO = 0;  // 非系统分类
    public static final int IS_SYSTEM_YES = 1; // 系统分类
    /**
     * 第三层默认分类名称和编码
     */
    public static final String[] DEFAULT_DOCUMENT_TYPES = {
            "产品资料", "产品图库", "产品彩页", "产品证书", "Quick Specs", "图说产品"
    };
    public static final String[] DEFAULT_DOCUMENT_CODES = {
            "PRODUCT_DATA", "PRODUCT_GALLERY", "PRODUCT_BROCHURE",
            "PRODUCT_CERTIFICATE", "QUICK_SPECS", "PRODUCT_ILLUSTRATION"
    };
    /**
     * 第三层分类编码常量
     */
    public static final String CODE_PRODUCT_DATA = "PRODUCT_DATA";           // 产品资料
    public static final String CODE_PRODUCT_GALLERY = "PRODUCT_GALLERY";     // 产品图库
    public static final String CODE_PRODUCT_BROCHURE = "PRODUCT_BROCHURE";   // 产品彩页
    public static final String CODE_PRODUCT_CERTIFICATE = "PRODUCT_CERTIFICATE"; // 产品证书
    public static final String CODE_QUICK_SPECS = "QUICK_SPECS";             // Quick Specs
    public static final String CODE_PRODUCT_ILLUSTRATION = "PRODUCT_ILLUSTRATION"; // 图说产品

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类ID")
    @TableId(value = "category_id", type = IdType.ASSIGN_ID)
    private Long categoryId;


    @Schema(description = "数据集ID")
    @TableField(value = "dataset_id")
    private String dataSetId;

    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    @TableField(value = "category_name")
    private String categoryName;

    @Schema(description = "分类编码")
    @Size(max = 50, message = "分类编码长度不能超过50个字符")
    @TableField(value = "category_code")
    private String categoryCode;

    @Schema(description = "父级分类ID，NULL表示顶级分类")
    @TableField(value = "parent_id")
    private Long parentId;

    @Schema(description = "分类层级：1-产品型号，2-产品系列，3-文档类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分类层级不能为空")
    @TableField(value = "level")
    private Integer level;

    @Schema(description = "排序号")
    @TableField(value = "sort_order")
    private Integer sortOrder;

    @Schema(description = "分类描述")
    @Size(max = 500, message = "分类描述长度不能超过500个字符")
    @TableField(value = "description")
    private String description;

    @Schema(description = "分类图标")
    @Size(max = 200, message = "分类图标长度不能超过200个字符")
    @TableField(value = "icon")
    private String icon;

    @Schema(description = "状态：0-禁用，1-启用")
    @TableField(value = "status")
    private Boolean status;

    @Schema(description = "是否系统分类：0-否，1-是（第三层自动生成的分类）")
    @TableField(value = "is_system")
    private Boolean isSystem;

    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.UPDATE)
    private String updateBy;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "删除标志：0-正常，1-删除")
    @TableField(value = "del_flag")
    @TableLogic
    private String delFlag;

    // 非数据库字段
    @Schema(description = "子分类列表")
    @TableField(exist = false)
    private List<AiDocumentCategory> children;

    @Schema(description = "父分类名称")
    @TableField(exist = false)
    private String parentName;
}
