package com.coocare.ai;

import com.coocare.ai.config.auth.reource.annotation.EnableResourceServer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 应用启动入口
 * 战AI-企业版后端服务的 Spring Boot 引导类
 *
 * <AUTHOR>
 * @since 2023-05-22
 */

@MapperScan("com.coocare.ai.mapper")
@EnableResourceServer
@SpringBootApplication()
public class ZhanAiEnterpriseApplication {

    public static void main(String[] args) {
        SpringApplication.run(ZhanAiEnterpriseApplication.class, args);
    }
}
