package com.coocare.ai.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.config.constants.UserDetail;
import com.coocare.ai.entity.AiChatStatistics;
import com.coocare.ai.mapper.AiChatStatisticsMapper;
import com.coocare.ai.service.AiChatStatisticsService;
import io.github.guoshiqiufeng.dify.chat.pipeline.ChatMessagePipelineModel;
import io.github.guoshiqiufeng.dify.core.pipeline.PipelineContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * AI对话统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiChatStatisticsServiceImpl extends ServiceImpl<AiChatStatisticsMapper, AiChatStatistics> implements AiChatStatisticsService {

    @Override
    public boolean recordChatStatistics(PipelineContext<ChatMessagePipelineModel> context) {
        try {
            ChatMessagePipelineModel model = context.getModel();

            // 从context中获取信息
            String conversationId = model.getConversationId();
            String messageId = model.getMessageId();

            // 获取token使用信息
            Integer totalTokens = model.getMetadata().getUsage().getTotalTokens();
            Integer inputTokens = model.getMetadata().getUsage().getPromptTokens();
            Integer outputTokens = model.getMetadata().getUsage().getCompletionTokens();

            // 获取对话内容
//            String userInput = extractFromModel(model, "query");
//            String aiResponse = extractFromModel(model, "answer");

            // 获取当前用户信息
//            UserDetail currentUser = SecurityUtils.getUser();
            UserDetail currentUser = null;
            Long userId = currentUser != null ? currentUser.getId() : null;
            String username = currentUser != null ? currentUser.getUsername() : "anonymous";
            String userName = currentUser != null ? currentUser.getName() : "匿名用户";

            return recordChatStatistics(
                    conversationId, messageId, null, null, inputTokens, outputTokens, totalTokens,
                    userId, username
            );

        } catch (Exception e) {
            log.error("记录对话统计信息失败", e);
            return false;
        }
    }

    @Override
    public boolean recordChatStatistics(String conversationId, String messageId, Long agentId, String agentName,
                                        Integer inputTokens, Integer outputTokens, Integer totalTokens,
                                        Long userId, String username) {
        try {
            AiChatStatistics statistics = new AiChatStatistics();
            statistics.setConversationId(conversationId);
            statistics.setMessageId(messageId);
            statistics.setAgentId(agentId);
            statistics.setAgentName(agentName);
            statistics.setInputTokens(inputTokens);
            statistics.setOutputTokens(outputTokens);
            statistics.setTotalTokens(totalTokens);
            statistics.setUserId(userId);
            statistics.setUsername(username);
            statistics.setConversationStartTime(LocalDateTime.now());
            statistics.setConversationEndTime(LocalDateTime.now());

            return save(statistics);
        } catch (Exception e) {
            log.error("保存对话统计信息失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getTokenStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getTokenStatistics(startTime, endTime);
    }

    @Override
    public Map<String, Object> getAgentStatistics(Long agentId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getAgentStatistics(agentId, startTime, endTime);
    }

    @Override
    public Map<String, Object> getUserStatistics(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getUserStatistics(userId, startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getPopularAgents(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        return baseMapper.getPopularAgents(startTime, endTime, limit);
    }

    @Override
    public List<Map<String, Object>> getActiveUsers(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        return baseMapper.getActiveUsers(startTime, endTime, limit);
    }

}
