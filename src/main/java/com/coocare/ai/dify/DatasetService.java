package com.coocare.ai.dify;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocare.ai.config.GlobalConfigManager;
import com.coocare.ai.config.constants.ConfigConstants;
import com.coocare.ai.entity.AiDataset;
import com.coocare.ai.entity.AiDocument;
import com.coocare.ai.event.DocumentUploadEvent;
import com.coocare.ai.service.AiDatasetService;
import com.coocare.ai.service.AiDocumentService;
import io.github.guoshiqiufeng.dify.client.spring6.builder.DifyDatasetBuilder;
import io.github.guoshiqiufeng.dify.core.config.DifyProperties;
import io.github.guoshiqiufeng.dify.dataset.DifyDataset;
import io.github.guoshiqiufeng.dify.dataset.dto.request.DocumentCreateByFileRequest;
import io.github.guoshiqiufeng.dify.dataset.dto.request.DocumentUpdateByFileRequest;
import io.github.guoshiqiufeng.dify.dataset.dto.response.DocumentCreateResponse;
import io.github.guoshiqiufeng.dify.dataset.enums.IndexingTechniqueEnum;
import io.github.guoshiqiufeng.dify.dataset.enums.document.DocFormEnum;
import io.github.guoshiqiufeng.dify.dataset.enums.document.DocTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * @description:
 * @author: Adam
 * @create: 2025-08-14 08:44
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class DatasetService {

    private final AiDocumentService aiDocumentService;
    private final AiDatasetService aiDatasetService;
    private final GlobalConfigManager globalConfigManager;

    /**
     * 添加文件到数据集
     *
     * @param documentUploadEvent
     */
    public void handleFileToDataSet(DocumentUploadEvent documentUploadEvent) {
        try {
            AiDataset dataset = aiDatasetService.getById(documentUploadEvent.getDatasetId());
            String difyServiceUrl = globalConfigManager.getDifyServiceUrl();
            if (StrUtil.isBlank(difyServiceUrl)) {
                log.error("AI引擎的地址未配置，请检查系统配置");
                return;
            }

            DifyDataset difyDataset = DifyDatasetBuilder.create(DifyDatasetBuilder.DifyDatasetClientBuilder
                    .builder()
                    .baseUrl(globalConfigManager.getConfig(ConfigConstants.DIFY_SERVICE_URL))
                    .clientConfig(new DifyProperties.ClientConfig())
                    .apiKey(globalConfigManager.getConfig(ConfigConstants.DATASET_API_KEY))
                    .build());

            switch (documentUploadEvent.getOperationType()) {
                case UPLOAD -> addFile(documentUploadEvent, dataset, difyDataset);
                case DELETE -> removeFileToDataSet(documentUploadEvent, dataset, difyDataset);
                case STATUS_CHANGE -> changeFileStatus(documentUploadEvent, dataset, difyDataset);
                case UPDATE -> updateFileToDataSet(documentUploadEvent, dataset, difyDataset);
            }

            addFile(documentUploadEvent, dataset, difyDataset);
        } catch (Exception e) {
            log.error("添加文件到数据集失败: datasetId={}, filePath={}", documentUploadEvent.getDatasetId(), documentUploadEvent.getDocument().getDocumentId(), e);
        }
    }

    private void addFile(DocumentUploadEvent documentUploadEvent, AiDataset dataset, DifyDataset difyDataset) {
        DocumentCreateByFileRequest documentCreateByFileRequest = new DocumentCreateByFileRequest();
        documentCreateByFileRequest.setDatasetId(dataset.getBackendId());
        documentCreateByFileRequest.setFile(documentUploadEvent.getFile());
        documentCreateByFileRequest.setDocType(DocTypeEnum.others);
        //documentCreateByFileRequest.setDocMetadata(Map.of("key", "file"));
        documentCreateByFileRequest.setIndexingTechnique(IndexingTechniqueEnum.HIGH_QUALITY);
        documentCreateByFileRequest.setDocForm(DocFormEnum.hierarchical_model);
        documentCreateByFileRequest.setDocLanguage("Chinese");
        DocumentCreateResponse documentByFile = difyDataset.createDocumentByFile(documentCreateByFileRequest);
        aiDocumentService.update(Wrappers.lambdaUpdate(AiDocument.class)
                .eq(AiDocument::getDocumentId, documentUploadEvent.getDocument().getDocumentId())
                .set(AiDocument::getBackendId, documentByFile.getDocument().getId()));
    }

    private void removeFileToDataSet(DocumentUploadEvent documentUploadEvent, AiDataset dataset, DifyDataset difyDataset) {
        difyDataset.deleteDocument(dataset.getBackendId(), documentUploadEvent.getDocument().getBackendId());
        aiDocumentService.removeById(documentUploadEvent.getDocument().getDocumentId());
    }

    private void updateFileToDataSet(DocumentUploadEvent documentUploadEvent, AiDataset dataset, DifyDataset difyDataset) {
        DocumentUpdateByFileRequest documentUpdateByFileRequest = new DocumentUpdateByFileRequest();
        documentUpdateByFileRequest.setDatasetId(dataset.getBackendId());
        documentUpdateByFileRequest.setDocumentId(documentUploadEvent.getDocument().getBackendId());
        documentUpdateByFileRequest.setFile(documentUploadEvent.getFile());
        DocumentCreateResponse response = difyDataset.updateDocumentByFile(documentUpdateByFileRequest);
        aiDocumentService.update(Wrappers.lambdaUpdate(AiDocument.class)
                .eq(AiDocument::getDocumentId, documentUploadEvent.getDocument().getDocumentId())
                .set(AiDocument::getBackendId, response.getDocument().getId()));
    }

    public void changeFileStatus(DocumentUploadEvent documentUploadEvent, AiDataset dataset, DifyDataset difyDataset) {
        Set<String> docIds = new HashSet<>();
        docIds.add(documentUploadEvent.getDocument().getBackendId());
        difyDataset.changeDocumentStatus(dataset.getBackendId(), docIds, documentUploadEvent.getAction());
        aiDocumentService.update(Wrappers.lambdaUpdate(AiDocument.class)
                .eq(AiDocument::getDocumentId, documentUploadEvent.getDocument().getDocumentId())
                .set(AiDocument::getStatus, documentUploadEvent.getAction().name()));
    }


}
