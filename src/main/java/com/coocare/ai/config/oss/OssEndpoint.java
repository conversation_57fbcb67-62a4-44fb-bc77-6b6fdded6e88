package com.coocare.ai.config.oss;//package com.coocare.ai.config.oss;
/**
 * OSS 端点（示例/占位）
 * 若启用请取消注释并接入 OssTemplate
 *
 * <AUTHOR>
 * @since 2023-01-01
 */
//
//import com.amazonaws.services.s3.model.S3Object;
//import com.amazonaws.services.s3.model.S3ObjectSummary;
//import lombok.AllArgsConstructor;
//import lombok.Cleanup;
//import lombok.SneakyThrows;
//import org.springframework.http.HttpStatus;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.InputStream;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * aws 对外提供服务端点
// *
// * <AUTHOR>
// * <AUTHOR>
// * <p>
// * oss.info
// */
//@RestController
//@AllArgsConstructor
//@RequestMapping("/oss")
//public class OssEndpoint {
//
//	private final OssTemplate ai;
//
//
//	/**
//	 * Object Endpoints
//	 */
//	@SneakyThrows
//	@PostMapping("/object/{bucketName}")
//	public S3Object createObject(@RequestBody MultipartFile object, @PathVariable String bucketName) {
//		String name = object.getOriginalFilename();
//		@Cleanup
//		InputStream inputStream = object.getInputStream();
//		ai.putObject(bucketName, name, inputStream, object.getSize(), object.getContentType());
//		return ai.getObjectInfo(bucketName, name);
//
//	}
//
//	@SneakyThrows
//	@PostMapping("/object/{bucketName}/{objectName}")
//	public S3Object createObject(@RequestBody MultipartFile object, @PathVariable String bucketName,
//			@PathVariable String objectName) {
//		@Cleanup
//		InputStream inputStream = object.getInputStream();
//		ai.putObject(bucketName, objectName, inputStream, object.getSize(), object.getContentType());
//		return ai.getObjectInfo(bucketName, objectName);
//
//	}
//
//	@SneakyThrows
//	@GetMapping("/object/{bucketName}")
//	public List<S3ObjectSummary> filterObject(@PathVariable String bucketName, @RequestParam String objectName) {
//		return ai.getAllObjectsByPrefix(bucketName, objectName, true);
//	}
//
//	@SneakyThrows
//	@GetMapping("/object/{bucketName}/{expires}")
//	public Map<String, Object> getObject(@PathVariable String bucketName, @RequestParam String objectName,
//			@PathVariable Integer expires) {
//		Map<String, Object> responseBody = new HashMap<>(8);
//		// Put Object info
//		responseBody.put("bucket", bucketName);
//		responseBody.put("object", objectName);
//		responseBody.put("url", ai.getObjectURL(bucketName, objectName, expires));
//		responseBody.put("expires", expires);
//		return responseBody;
//	}
//
//	@SneakyThrows
//	@ResponseStatus(HttpStatus.ACCEPTED)
//	@DeleteMapping("/object/{bucketName}")
//	public void deleteObject(@PathVariable String bucketName, @RequestParam String objectName) {
//		ai.removeObject(bucketName, objectName);
//	}
//
//	@SneakyThrows
//	@GetMapping("/preUpload/{bucketName}")
//	public String preUpload(@PathVariable String bucketName, @RequestParam String objectName) {
//		return ai.preUploadUrl(bucketName, objectName);
//	}
//
//}
