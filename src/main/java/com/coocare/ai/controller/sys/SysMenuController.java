package com.coocare.ai.controller.sys;

import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.config.plugins.logging.annotation.Logging;
import com.coocare.ai.entity.sys.SysMenu;
import com.coocare.ai.service.SysMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 菜单管理控制器
 * 提供用户菜单、菜单树、角色菜单、CRUD 等接口
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Tag(name = "菜单管理")
@RestController
@RequestMapping("/sys/menu")
@RequiredArgsConstructor
public class SysMenuController {

    private final SysMenuService sysMenuService;

    /**
     * 返回当前用户的树形菜单集合
     * @param type 类型
     * @param parentId 父节点ID
     * @return 当前用户的树形菜单
     */
    @Operation(summary = "获取当前用户菜单", description = "根据用户权限获取可访问的菜单树形结构", operationId = "sysMenu_userMenu")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping
    public AjaxResult<?> getUserMenu(
            @Parameter(description = "菜单类型，如：menu、button等") String type,
            @Parameter(description = "父节点ID，为空时获取所有菜单") Long parentId) {
        // 获取符合条件的菜单
        Set<SysMenu> all = new HashSet<>();
//        StpUtil.getRoleList().forEach(roleId -> all.addAll(sysMenuService.findMenuByRoleId(Long.valueOf(roleId))));
        return AjaxResult.ok(sysMenuService.filterMenu(all, type, parentId));
    }

    /**
     * 返回树形菜单集合
     * @param parentId 父节点ID
     * @param menuName 菜单名称
     * @return 树形菜单
     */
    @Operation(summary = "获取菜单树", description = "获取系统所有菜单的树形结构，支持按名称搜索", operationId = "sysMenu_tree")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping(value = "/tree")
    public AjaxResult<?> getTree(
            @Parameter(description = "父节点ID，为空时获取所有菜单") Long parentId,
            @Parameter(description = "菜单名称，支持模糊搜索") String menuName,
            @Parameter(description = "菜单类型") String type) {
        return AjaxResult.ok(sysMenuService.treeMenu(parentId, menuName, type));
    }

    /**
     * 返回角色的菜单集合
     * @param roleId 角色ID
     * @return 属性集合
     */
    @Operation(summary = "获取角色菜单", description = "根据角色ID获取该角色拥有的菜单ID列表", operationId = "sysMenu_roleMenu")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "角色不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/tree/{roleId}")
    public AjaxResult<?> getRoleTree(
            @Parameter(description = "角色ID", required = true) @PathVariable Long roleId) {
        return AjaxResult.ok(sysMenuService.findMenuByRoleId(roleId).stream().map(SysMenu::getMenuId).collect(Collectors.toList()));
    }

    /**
     * 通过ID查询菜单的详细信息
     * @param id 菜单ID
     * @return 菜单详细信息
     */
    @Operation(summary = "根据ID查询菜单", description = "根据菜单ID获取菜单的详细信息", operationId = "sysMenu_getById")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "菜单不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}")
    public AjaxResult<?> getMenuInfoById(
            @Parameter(description = "菜单ID", required = true) @PathVariable Long id) {
        return AjaxResult.ok(sysMenuService.getById(id));
    }

    /**
     * 新增菜单
     * @param sysMenu 菜单信息
     * @return success/false
     */
    @Operation(summary = "新增菜单", description = "创建新的菜单项，支持菜单和按钮类型", operationId = "sysMenu_create")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "新增成功"),
        @ApiResponse(responseCode = "400", description = "参数校验失败"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @Logging("新增菜单")
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('sys_menu_add')")
    public AjaxResult<?> save(
            @Parameter(description = "菜单信息", required = true) @Valid @RequestBody SysMenu sysMenu) {
        sysMenuService.save(sysMenu);
        return AjaxResult.ok(sysMenu);
    }

    /**
     * 删除菜单
     * @param id 菜单ID
     * @return success/false
     */
    @Operation(summary = "删除菜单", description = "根据菜单ID删除菜单，会级联删除子菜单", operationId = "sysMenu_delete")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "400", description = "菜单存在子菜单，无法删除"),
        @ApiResponse(responseCode = "404", description = "菜单不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @Logging("删除菜单")
    @DeleteMapping("/{id}")
//    @PreAuthorize("@pms.hasPermission('sys_menu_del')")
    public AjaxResult<?> removeById(
            @Parameter(description = "菜单ID", required = true) @PathVariable Long id) {
        return sysMenuService.removeMenuById(id);
    }

    /**
     * 更新菜单
     * @param sysMenu 菜单信息
     * @return 更新结果
     */
    @Operation(summary = "更新菜单", description = "根据菜单ID更新菜单信息", operationId = "sysMenu_update")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "400", description = "参数校验失败"),
        @ApiResponse(responseCode = "404", description = "菜单不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @Logging("更新菜单")
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('sys_menu_edit')")
    public AjaxResult<?> update(
            @Parameter(description = "菜单信息", required = true) @Valid @RequestBody SysMenu sysMenu) {
        return AjaxResult.ok(sysMenuService.updateMenuById(sysMenu));
    }
}
