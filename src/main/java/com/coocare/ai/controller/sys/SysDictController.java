package com.coocare.ai.controller.sys;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocare.ai.config.domain.AjaxResult;
import com.coocare.ai.entity.sys.SysDict;
import com.coocare.ai.entity.sys.SysDictItem;
import com.coocare.ai.service.SysDictItemService;
import com.coocare.ai.service.SysDictService;
import com.coocare.ai.utils.PageDomain;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 字典表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */

@Tag(name = "字典管理")
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/sys/dict")
public class SysDictController {

    private final SysDictService dictService;
    private final SysDictItemService dictItemService;

    /**
     * 分页查询字典列表
     * @param dict 字典查询条件
     * @param pageDomain 分页参数
     * @return 字典分页列表
     */
    @Operation(summary = "分页查询字典列表", description = "根据条件分页查询系统字典信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/page")
    public AjaxResult<?> queryDictPage(
            @Parameter(description = "字典查询条件") SysDict dict,
            @Parameter(description = "分页参数") PageDomain pageDomain) {
        return AjaxResult.ok(dictService.queryDictPage(dict, pageDomain));
    }


    /**
     * 分页查询字典项列表
     * @param dictId 字典ID
     * @param description 字典项描述
     * @param pageDomain 分页参数
     * @return 字典项分页列表
     */
    @Operation(summary = "分页查询字典项列表", description = "根据字典ID分页查询字典项信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "字典不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("{dictId}/item/page")
    public AjaxResult<?> queryDictItemPage(
            @Parameter(description = "字典ID", required = true) @PathVariable Long dictId,
            @Parameter(description = "字典项描述，支持模糊搜索") String description,
            @Parameter(description = "分页参数") PageDomain pageDomain) {
        return AjaxResult.ok(dictItemService.queryDictItemPage(dictId, description, pageDomain));
    }

    @Operation(summary = "根据字典项目子项列表")
    @GetMapping("{dictType}/item")
    public AjaxResult<?> queryDictItemByType(@PathVariable String dictType) {
        return AjaxResult.ok(dictItemService.list(new LambdaQueryWrapper<SysDictItem>().eq(SysDictItem::getDictType, dictType)));
    }

    @Operation(summary = "保存字典项目")
    @PostMapping("/item")
    @CacheEvict(value = "dict_details", allEntries = true)
    public AjaxResult<?> editItem(@RequestBody SysDictItem dictItem) {
        return AjaxResult.ok(dictItemService.saveOrUpdate(dictItem));
    }

    @Operation(summary = "删除字典项目")
    @DeleteMapping("/item/{id}")
    @CacheEvict(value = "dict_details", allEntries = true)
    public AjaxResult<?> delItem(@PathVariable("id") Long id) {
        return AjaxResult.ok(dictItemService.removeById(id));
    }

    @Operation(summary = "保存字典")
    @PostMapping
    public AjaxResult<?> saveDict(@RequestBody SysDict dict) {
        return AjaxResult.ok(dictService.saveOrUpdate(dict));
    }

    @Operation(summary = "删除字典")
    @DeleteMapping("/{id}")
    public AjaxResult<?> delDict(@PathVariable("id") Long id) {
        return AjaxResult.ok(dictService.removeById(id));
    }
}
