server:
  port: 8070

# 前端登录验证码
security:
  encodeKey: 'coocareencodekey'

spring:
  application:
    name: @artifactId@
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
    banner-mode: off
    allow-bean-definition-overriding: true
    allow-circular-references: true
  messages:
    basename: i18n/messages
  cache:
    type: redis
  data:
    redis:
      host: ${REDIS_HOST:***********}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:18}
      password: ${REDIS_PASSWORD:123456}
      timeout: 5000
  datasource:
    url: jdbc:mysql://${MYSQL_HOST:***********}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:zhan_ai_enterprise}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:root}
  mail:
    host: ${MAIL_HOST:smtp.qq.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

# mybatis-plus 配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: auto
      where-strategy: not_empty
      insert-strategy: not_empty
      update-strategy: not_null
  configuration:
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

mybatis-plus-join:
  banner: false
  sub-table-logic: true
  ms-cache: true
  table-alias: t
  logic-del-type: where

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  default-flat-param-object: true
knife4j:
  enable: false
  setting:
    enable-swagger-models: true
    enable-dynamic-parameter: false
    footer-custom-content: "<strong>Copyright ©️ 2024 Coocare.Inc. All Rights Reversed</strong>"
    enable-footer-custom: true
    enable-footer: true
    enable-document-manage: true
  # production: true  # 生产环境屏蔽swagger

# oss配置 支持其他云存储
oss:
  enable: true
  accessKey: OzISMUOidAONjgbsH6zo
  secretKey: lF8ogzlqgiXg25EWiqGGEhT7UCTw2VCBmzsBMZxO
  endpoint: http://10.10.4.22:9000
  bucketName: dify
  custom-domain: http://10.10.4.22:9000 # 自定义域名
  path-style-access: false

logging:
  level:
    org:
      springframework:
        web:
          filter:
            CommonsRequestLoggingFilter: DEBUG
