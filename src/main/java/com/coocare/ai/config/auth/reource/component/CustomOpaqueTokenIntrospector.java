package com.coocare.ai.config.auth.reource.component;

import com.coocare.ai.config.auth.reource.service.CustomUserDetailsService;
import com.coocare.ai.config.constants.SecurityConstants;
import com.coocare.ai.config.constants.UserDetail;
import com.coocare.ai.utils.SpringContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.OAuth2AuthenticatedPrincipal;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.security.oauth2.server.resource.InvalidBearerTokenException;
import org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector;

import java.security.Principal;
import java.util.Comparator;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 自定义 Opaque Token 内省器
 * 从本地 OAuth2AuthorizationService 读取令牌并恢复用户上下文
 *
 * <AUTHOR>
 * @since 2022-06-02
 */

@Slf4j
@RequiredArgsConstructor
public class CustomOpaqueTokenIntrospector implements OpaqueTokenIntrospector {

    private final OAuth2AuthorizationService authorizationService;

    @Override
    public OAuth2AuthenticatedPrincipal introspect(String token) {
        OAuth2Authorization oldAuthorization = authorizationService.findByToken(token, OAuth2TokenType.ACCESS_TOKEN);
        if (Objects.isNull(oldAuthorization)) {
            throw new InvalidBearerTokenException(token);
        }

        if (SecurityConstants.FROM.equals(oldAuthorization.getRegisteredClientId())) {
            return (UserDetail) ((UsernamePasswordAuthenticationToken) Objects.requireNonNull(oldAuthorization).getAttributes().get(Principal.class.getName())).getPrincipal();
        }

        // 客户端模式默认返回
        if (AuthorizationGrantType.CLIENT_CREDENTIALS.equals(oldAuthorization.getAuthorizationGrantType())) {
            return new ClientCredentialsOAuth2AuthenticatedPrincipal(
                    Objects.requireNonNull(oldAuthorization.getAccessToken().getClaims()),
                    AuthorityUtils.NO_AUTHORITIES, oldAuthorization.getPrincipalName());
        }

        Map<String, CustomUserDetailsService> userDetailsServiceMap = SpringContextHolder
                .getBeansOfType(CustomUserDetailsService.class);

        Optional<CustomUserDetailsService> optional = userDetailsServiceMap.values()
                .stream()
                .filter(service -> service.support(Objects.requireNonNull(oldAuthorization).getRegisteredClientId(), oldAuthorization.getAuthorizationGrantType().getValue()))
                .max(Comparator.comparingInt(Ordered::getOrder));

        UserDetails userDetails = null;
        try {
            Object principal = Objects.requireNonNull(oldAuthorization).getAttributes().get(Principal.class.getName());
            UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = (UsernamePasswordAuthenticationToken) principal;
            Object tokenPrincipal = usernamePasswordAuthenticationToken.getPrincipal();
            userDetails = optional.get().loadUserByUser((UserDetail) tokenPrincipal);
        } catch (UsernameNotFoundException notFoundException) {
            log.warn("用户不存在 {}", notFoundException.getLocalizedMessage());
            throw notFoundException;
        } catch (DisabledException disabledException) {
            log.error("用户禁用");
            throw disabledException;
        } catch (Exception ex) {
            log.error("资源服务器 introspect Token error {}", ex.getLocalizedMessage());
            throw ex;
        }

        // 注入客户端信息，方便上下文中获取
        UserDetail userDetail = (UserDetail) userDetails;
        Objects.requireNonNull(userDetail)
                .getAttributes()
                .put(SecurityConstants.CLIENT_ID, oldAuthorization.getRegisteredClientId());
        return userDetail;
    }
}
