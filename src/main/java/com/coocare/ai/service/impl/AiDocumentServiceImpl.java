package com.coocare.ai.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocare.ai.config.oss.OssProperties;
import com.coocare.ai.config.oss.OssTemplate;
import com.coocare.ai.entity.AiDocument;
import com.coocare.ai.entity.AiDocumentCategory;
import com.coocare.ai.entity.dto.AiDocumentQueryDTO;
import com.coocare.ai.entity.dto.AiDocumentUploadDTO;
import com.coocare.ai.entity.vo.AiDocumentGroupVO;
import com.coocare.ai.entity.vo.AiDocumentVO;
import com.coocare.ai.event.DocumentUploadEvent;
import com.coocare.ai.mapper.AiDocumentMapper;
import com.coocare.ai.service.AiDocumentCategoryService;
import com.coocare.ai.service.AiDocumentService;
import com.coocare.ai.utils.PageDomain;
import com.coocare.ai.utils.PageUtils;
import io.github.guoshiqiufeng.dify.dataset.enums.document.DocActionEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * AI文档 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiDocumentServiceImpl extends ServiceImpl<AiDocumentMapper, AiDocument>
        implements AiDocumentService {

    // 支持的文件类型
    private static final Set<String> ALLOWED_FILE_TYPES = Set.of(
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
            "txt", "md", "zip", "rar", "7z"
    );
    private final AiDocumentCategoryService categoryService;
    private final ApplicationEventPublisher eventPublisher;
    private final OssProperties ossProperties;
    private final OssTemplate ossTemplate;

    @Override
    public PageUtils pageInfo(PageDomain pageDomain, AiDocumentQueryDTO queryDTO) {
        LambdaQueryWrapper<AiDocument> wrapper = Wrappers.lambdaQuery(AiDocument.class)
                .like(StrUtil.isNotBlank(queryDTO.getDocumentName()), AiDocument::getDocumentName, queryDTO.getDocumentName())
                .eq(queryDTO.getCategoryId() != null, AiDocument::getCategoryId, queryDTO.getCategoryId())
                .eq(queryDTO.getProductSeriesId() != null, AiDocument::getProductSeriesId, queryDTO.getProductSeriesId())
                .eq(queryDTO.getProductModelId() != null, AiDocument::getProductModelId, queryDTO.getProductModelId())
                .eq(StrUtil.isNotBlank(queryDTO.getCategoryCode()), AiDocument::getCategoryCode, queryDTO.getCategoryCode())
                .eq(StrUtil.isNotBlank(queryDTO.getFileType()), AiDocument::getFileType, queryDTO.getFileType())
                .eq(queryDTO.getStatus() != null, AiDocument::getStatus, queryDTO.getStatus())
                .eq(queryDTO.getIndexingStatus() != null, AiDocument::getIndexingStatus, queryDTO.getIndexingStatus())
                .eq(StrUtil.isNotBlank(queryDTO.getUploadBy()), AiDocument::getUploadBy, queryDTO.getUploadBy())
                .eq(AiDocument::getDelFlag, "0")
                .orderByDesc(AiDocument::getCreateTime);

        Page<AiDocument> page = this.page(new Page<>(pageDomain.getPageNo(), pageDomain.getPageSize()), wrapper);

        // 转换为VO
        List<AiDocumentVO> voList = page.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        PageUtils pageUtils = new PageUtils(page);
        pageUtils.setList(voList);
        return pageUtils;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AiDocumentVO uploadDocument(MultipartFile file, AiDocumentUploadDTO uploadDTO) {
        // 验证文件
        validateFile(file);

        // 验证分类
        AiDocumentCategory category = categoryService.getById(uploadDTO.getCategoryId());
        if (category == null || AiDocumentCategory.LEVEL_DOCUMENT_TYPE != category.getLevel()) {
            throw new IllegalArgumentException("分类不存在或不是第三层分类");
        }

        // 获取分类层级信息
        AiDocumentCategory productSeries = categoryService.getById(category.getParentId());
        AiDocumentCategory productModel = categoryService.getById(productSeries.getParentId());

        // 检查文档名称是否重复
        if (checkDocumentNameExists(uploadDTO.getDocumentName(), uploadDTO.getCategoryId(), null)) {
            throw new IllegalArgumentException("文档名称在该分类下已存在");
        }

        try {
            // 保存文件
            String filePath = saveFile(file);

            // 创建文档记录
            AiDocument document = new AiDocument();
            BeanUtil.copyProperties(uploadDTO, document);
            document.setOriginalName(file.getOriginalFilename());
            document.setFilePath(filePath);
            document.setFileUrl(filePath);
            document.setFileSize(file.getSize());
            document.setFileType(getFileExtension(file.getOriginalFilename()));
            document.setMimeType(file.getContentType());
            document.setProductSeriesId(productSeries.getCategoryId());
            document.setProductModelId(productModel.getCategoryId());
            document.setCategoryCode(category.getCategoryCode());
            document.setDownloadCount(0);
            document.setViewCount(0);
            document.setStatus("enable");
            document.setDelFlag("0");
            document.setCreateTime(LocalDateTime.now());

            // 设置默认值
            document.setIndexingStatus("waiting");
            if (document.getVersion() == null) {
                document.setVersion("1.0");
            }
            if (document.getSortOrder() == null) {
                document.setSortOrder(0);
            }

            this.save(document);

            AiDocumentVO documentVO = convertToVO(document);

            // 发布文档上传成功事件
            try {
                DocumentUploadEvent event = new DocumentUploadEvent(this, documentVO,
                        DocumentUploadEvent.OperationType.UPLOAD, uploadDTO.getDateSetId(), document.getUploadBy(), null, null, null, file);
                eventPublisher.publishEvent(event);
                log.info("发布文档上传成功事件: {}", document.getDocumentName());
            } catch (Exception e) {
                log.error("发布文档上传事件失败", e);
            }

            return documentVO;

        } catch (Exception e) {
            log.error("文件保存失败", e);
            throw new RuntimeException("文件保存失败: " + e.getMessage());
        }
    }

    @Override
    public List<AiDocumentGroupVO> getDocumentsByProductSeriesGrouped(Long productSeriesId) {
        // 获取该产品系列下的所有文档
        List<AiDocument> documents = baseMapper.selectDocumentsByProductSeriesId(productSeriesId);

        // 按分类编码分组
        Map<String, List<AiDocument>> groupedDocuments = documents.stream()
                .collect(Collectors.groupingBy(AiDocument::getCategoryCode));

        // 获取所有第三层分类
        List<AiDocumentCategory> categories = categoryService.getChildrenByParentId(productSeriesId);

        // 构建分组结果
        List<AiDocumentGroupVO> result = new ArrayList<>();
        for (AiDocumentCategory category : categories) {
            AiDocumentGroupVO groupVO = new AiDocumentGroupVO();
            groupVO.setCategoryCode(category.getCategoryCode());
            groupVO.setCategoryName(category.getCategoryName());
            groupVO.setCategoryId(category.getCategoryId());
            groupVO.setCategoryDescription(category.getDescription());
            groupVO.setCategoryIcon(category.getIcon());
            groupVO.setSortOrder(category.getSortOrder());

            // 获取该分类下的文档
            List<AiDocument> categoryDocuments = groupedDocuments.getOrDefault(category.getCategoryCode(), new ArrayList<>());
            List<AiDocumentVO> documentVOs = categoryDocuments.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            groupVO.setDocuments(documentVOs);
            result.add(groupVO);
        }

        // 按排序号排序
        result.sort(Comparator.comparing(AiDocumentGroupVO::getSortOrder));

        return result;
    }

    @Override
    public List<AiDocumentVO> getDocumentsByProductSeriesAndCategory(Long productSeriesId, String categoryCode) {
        List<AiDocument> documents = baseMapper.selectDocumentsByProductSeriesAndCategory(productSeriesId, categoryCode);
        return documents.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<AiDocumentVO> getDocumentsByCategoryId(Long categoryId) {
        List<AiDocument> documents = baseMapper.selectDocumentsByCategoryId(categoryId);
        return documents.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<AiDocumentVO> getDocumentsByProductModelId(Long productModelId) {
        List<AiDocument> documents = baseMapper.selectDocumentsByProductModelId(productModelId);
        return documents.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public AiDocumentVO getDocumentDetail(Long documentId) {
        AiDocument document = baseMapper.selectDocumentDetailById(documentId);
        if (document == null) {
            return null;
        }
        return convertToVO(document);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDocument(Long documentId, AiDocumentUploadDTO uploadDTO) {
        AiDocument existing = this.getById(documentId);
        if (existing == null) {
            throw new IllegalArgumentException("文档不存在");
        }

        // 检查文档名称是否重复（排除自己）
        if (StrUtil.isNotBlank(uploadDTO.getDocumentName()) &&
                checkDocumentNameExists(uploadDTO.getDocumentName(), existing.getCategoryId(), documentId)) {
            throw new IllegalArgumentException("文档名称已存在");
        }

        AiDocument document = new AiDocument();
        BeanUtil.copyProperties(uploadDTO, document);
        document.setDocumentId(documentId);
        document.setUpdateTime(LocalDateTime.now());

        return this.updateById(document);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDocument(Long documentId, Long datasetId) {
        // 先获取文档信息用于事件发布
        AiDocumentVO documentVO = getDocumentDetail(documentId);

        AiDocument document = new AiDocument();
        document.setDocumentId(documentId);
        document.setDelFlag("1");
        document.setUpdateTime(LocalDateTime.now());

        boolean result = this.updateById(document);

        // 发布文档删除事件
        if (result && documentVO != null) {
            try {
                DocumentUploadEvent event = new DocumentUploadEvent(this, documentVO,
                        DocumentUploadEvent.OperationType.DELETE, datasetId, "getCurrentUser()", null, null, null, null);
                eventPublisher.publishEvent(event);
                log.info("发布文档删除成功事件: {}", documentVO.getDocumentName());
            } catch (Exception e) {
                log.error("发布文档删除事件失败", e);
            }
        } else if (!result) {
            log.error("发布文档删除失败事件失败");
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(List<Long> documentIds, Long datasetId) {
        if (documentIds == null || documentIds.isEmpty()) {
            return true;
        }

        // 先获取文档信息用于事件发布
        List<AiDocumentVO> documentVOs = documentIds.stream()
                .map(this::getDocumentDetail)
                .filter(Objects::nonNull)
                .toList();

        List<AiDocument> documents = documentIds.stream()
                .map(id -> {
                    AiDocument document = new AiDocument();
                    document.setDocumentId(id);
                    document.setDelFlag("1");
                    document.setUpdateTime(LocalDateTime.now());
                    return document;
                })
                .collect(Collectors.toList());

        boolean result = this.updateBatchById(documents);

        // 发布批量删除事件
        if (result) {
            for (AiDocumentVO documentVO : documentVOs) {
                try {
                    DocumentUploadEvent event = new DocumentUploadEvent(this, documentVO,
                            DocumentUploadEvent.OperationType.DELETE, datasetId, "getCurrentUser()", null, null, null, null);
                    eventPublisher.publishEvent(event);
                } catch (Exception e) {
                    log.error("发布文档删除事件失败: {}", documentVO.getDocumentName(), e);
                }
            }
            log.info("发布批量删除事件完成，共 {} 个文档", documentVOs.size());
        }

        return result;
    }

    @Override
    public boolean updateStatus(Long documentId, Long datasetId, DocActionEnum status) {
        // 先获取文档信息用于事件发布
        AiDocumentVO documentVO = getDocumentDetail(documentId);
        String oldStatus = documentVO != null ? documentVO.getStatus() : null;

        AiDocument document = new AiDocument();
        document.setDocumentId(documentId);
        document.setStatus(status.name());
        document.setUpdateTime(LocalDateTime.now());

        boolean result = this.updateById(document);

        // 发布状态更改事件
        if (result && documentVO != null) {
            try {
                String statusChange = String.format("状态从[%s]更改为[%s]",
                        getStatusName(oldStatus), getStatusName(status.name()));
                DocumentUploadEvent event = new DocumentUploadEvent(this, documentVO,
                        DocumentUploadEvent.OperationType.STATUS_CHANGE, datasetId, "getCurrentUser()", null, null, status, null);
                eventPublisher.publishEvent(event);
                log.info("发布文档状态更改成功事件: {} - {}", documentVO.getDocumentName(), statusChange);
            } catch (Exception e) {
                log.error("发布文档状态更改事件失败", e);
            }
        }
        return result;
    }

    @Override
    public boolean batchUpdateStatus(List<Long> documentIds, Long datasetId, DocActionEnum status) {
        if (documentIds == null || documentIds.isEmpty()) {
            return true;
        }

        // 先获取文档信息用于事件发布
        List<AiDocumentVO> documentVOs = documentIds.stream()
                .map(this::getDocumentDetail)
                .filter(Objects::nonNull)
                .toList();

        boolean result = baseMapper.batchUpdateStatus(documentIds, status.name()) > 0;

        // 发布批量状态更改事件
        if (result) {
            for (AiDocumentVO documentVO : documentVOs) {
                try {
                    String statusChange = String.format("状态批量更改为[%s]", getStatusName(status.name()));
                    DocumentUploadEvent event = new DocumentUploadEvent(this, documentVO,
                            DocumentUploadEvent.OperationType.STATUS_CHANGE, datasetId, "getCurrentUser()", statusChange, null, status, null);
                    eventPublisher.publishEvent(event);
                } catch (Exception e) {
                    log.error("发布文档状态更改事件失败: {}", documentVO.getDocumentName(), e);
                }
            }
            log.info("发布批量状态更改事件完成，共 {} 个文档", documentVOs.size());
        }

        return result;
    }

    @Override
    public boolean incrementDownloadCount(Long documentId) {
        return baseMapper.incrementDownloadCount(documentId) > 0;
    }

    @Override
    public boolean incrementViewCount(Long documentId) {
        return baseMapper.incrementViewCount(documentId) > 0;
    }

    @Override
    public boolean checkDocumentNameExists(String documentName, Long categoryId, Long documentId) {
        LambdaQueryWrapper<AiDocument> wrapper = Wrappers.lambdaQuery(AiDocument.class)
                .eq(AiDocument::getDocumentName, documentName)
                .eq(AiDocument::getCategoryId, categoryId)
                .ne(documentId != null, AiDocument::getDocumentId, documentId)
                .eq(AiDocument::getDelFlag, "0");

        return this.count(wrapper) > 0;
    }

    @Override
    public int getDocumentCountByCategoryId(Long categoryId) {
        return baseMapper.countDocumentsByCategoryId(categoryId);
    }

    @Override
    public int getDocumentCountByProductSeriesId(Long productSeriesId) {
        return baseMapper.countDocumentsByProductSeriesId(productSeriesId);
    }

    @Override
    public AiDocumentVO downloadDocument(Long documentId) {
        AiDocumentVO document = getDocumentDetail(documentId);
        if (document != null) {
            // 增加下载次数
            incrementDownloadCount(documentId);
        }
        return document;
    }

    @Override
    public List<AiDocumentGroupVO> getDocumentGroupStats(Long productSeriesId) {
        List<AiDocument> stats = baseMapper.selectDocumentGroupStatsByProductSeriesId(productSeriesId);

        return stats.stream()
                .map(stat -> {
                    AiDocumentGroupVO groupVO = new AiDocumentGroupVO();
                    groupVO.setCategoryCode(stat.getCategoryCode());
                    groupVO.setCategoryName(stat.getCategoryName());
                    groupVO.setDocumentCount(stat.getDownloadCount()); // 这里复用了字段
                    groupVO.setTotalFileSize(stat.getFileSize());
                    return groupVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 验证上传文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        String fileExtension = getFileExtension(originalFilename);
        if (!ALLOWED_FILE_TYPES.contains(fileExtension.toLowerCase())) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileExtension);
        }

        // 检查文件大小（限制为100MB）
        if (file.getSize() > 100 * 1024 * 1024) {
            throw new IllegalArgumentException("文件大小不能超过100MB");
        }
    }

    /**
     * 保存文件到磁盘
     */
    private String saveFile(MultipartFile file) throws Exception {
        String fileName = IdUtil.fastSimpleUUID() + file.getOriginalFilename();
        ossTemplate.putObject(ossProperties.getBucketName(), fileName, file.getInputStream());
        return ossProperties.getCustomDomain() + "/" + fileName;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (StrUtil.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 转换为VO对象
     */
    private AiDocumentVO convertToVO(AiDocument document) {
        AiDocumentVO vo = BeanUtil.copyProperties(document, AiDocumentVO.class);
        vo.setFileSizeFormatted(document.getFileSizeFormatted());
        vo.setStatusName(document.getStatusName());
        vo.setIndexingStatusName(document.getIndexingStatus());
        return vo;
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(String status) {
        if (status == null) {
            return "未知";
        }
        return switch (status) {
            case "disable" -> "禁用";
            case "enable" -> "启用";
            case "archive" -> "归档文档";
            case "un_archive" -> "取消归档";
            default -> "未知";
        };
    }
}
