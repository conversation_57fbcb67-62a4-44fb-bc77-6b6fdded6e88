package com.coocare.ai.entity;

/**
 * 生成AI对话统计表建表语句
 *
 * <AUTHOR>
 * @since 2025-09-03
 */
public class GenerateAiChatStatisticsTable {

    public static void main(String[] args) {
        String sql = generateCreateTableSql();
        System.out.println(sql);
        
        // 也可以写入文件
        try {
            java.nio.file.Files.write(
                java.nio.file.Paths.get("db/ai_chat_statistics.sql"), 
                sql.getBytes()
            );
            System.out.println("\n建表语句已保存到 db/ai_chat_statistics.sql");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String generateCreateTableSql() {
        StringBuilder sql = new StringBuilder();
        
        sql.append("-- AI对话统计表\n");
        sql.append("DROP TABLE IF EXISTS ai_chat_statistics;\n");
        sql.append("CREATE TABLE ai_chat_statistics (\n");
        
        // 主键
        sql.append("    statistics_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计记录ID',\n");
        
        // 对话相关字段
        sql.append("    conversation_id VARCHAR(255) COMMENT '对话ID',\n");
        sql.append("    message_id VARCHAR(255) COMMENT '消息ID',\n");
        
        // 智能体相关字段
        sql.append("    agent_id BIGINT COMMENT '智能体ID',\n");
        sql.append("    agent_name VARCHAR(255) COMMENT '智能体名称',\n");
        
        // Token相关字段
        sql.append("    token_count INT COMMENT '使用的token数量',\n");
        sql.append("    input_tokens INT COMMENT '输入token数量',\n");
        sql.append("    output_tokens INT COMMENT '输出token数量',\n");
        sql.append("    total_tokens INT COMMENT '总token数量',\n");
        
        // 用户相关字段
        sql.append("    user_id BIGINT COMMENT '使用人ID',\n");
        sql.append("    username VARCHAR(100) COMMENT '使用人用户名',\n");
        sql.append("    user_name VARCHAR(100) COMMENT '使用人姓名',\n");
        
        // 时间相关字段
        sql.append("    conversation_start_time DATETIME COMMENT '对话开始时间',\n");
        sql.append("    conversation_end_time DATETIME COMMENT '对话结束时间',\n");
        sql.append("    response_time BIGINT COMMENT '响应时间（毫秒）',\n");
        
        // 内容相关字段
        sql.append("    user_input TEXT COMMENT '用户输入内容',\n");
        sql.append("    ai_response TEXT COMMENT 'AI回复内容',\n");
        
        // 状态相关字段
        sql.append("    event_type VARCHAR(50) COMMENT '事件类型',\n");
        sql.append("    status VARCHAR(20) COMMENT '状态：SUCCESS-成功，ERROR-错误',\n");
        sql.append("    error_message TEXT COMMENT '错误信息',\n");
        
        // 客户端相关字段
        sql.append("    client_ip VARCHAR(50) COMMENT '客户端IP地址',\n");
        sql.append("    user_agent VARCHAR(500) COMMENT '用户代理信息',\n");
        
        // 审计字段
        sql.append("    create_by VARCHAR(100) COMMENT '创建人',\n");
        sql.append("    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n");
        sql.append("    update_by VARCHAR(100) COMMENT '更新人',\n");
        sql.append("    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n");
        sql.append("    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志：0-正常，1-删除'\n");
        
        sql.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI对话统计表';\n\n");
        
        // 添加索引
        sql.append("-- 添加索引\n");
        sql.append("CREATE INDEX idx_conversation_id ON ai_chat_statistics(conversation_id);\n");
        sql.append("CREATE INDEX idx_agent_id ON ai_chat_statistics(agent_id);\n");
        sql.append("CREATE INDEX idx_user_id ON ai_chat_statistics(user_id);\n");
        sql.append("CREATE INDEX idx_create_time ON ai_chat_statistics(create_time);\n");
        sql.append("CREATE INDEX idx_event_type ON ai_chat_statistics(event_type);\n");
        sql.append("CREATE INDEX idx_status ON ai_chat_statistics(status);\n");
        sql.append("CREATE INDEX idx_del_flag ON ai_chat_statistics(del_flag);\n");
        
        return sql.toString();
    }
}
