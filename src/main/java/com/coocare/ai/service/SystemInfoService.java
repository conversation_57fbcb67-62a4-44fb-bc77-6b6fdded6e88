package com.coocare.ai.service;

import com.coocare.ai.entity.sys.dto.SystemInfoDTO;

/**
 * <p>
 * 系统信息服务接口
 * 用于获取服务器的硬件和系统信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface SystemInfoService {

    /**
     * 获取完整的系统信息
     * @return 系统信息
     */
    SystemInfoDTO getSystemInfo();

    /**
     * 获取CPU信息
     * @return CPU信息
     */
    SystemInfoDTO.CpuInfo getCpuInfo();

    /**
     * 获取内存信息
     * @return 内存信息
     */
    SystemInfoDTO.MemoryInfo getMemoryInfo();

    /**
     * 获取磁盘信息
     * @return 磁盘信息列表
     */
    java.util.List<SystemInfoDTO.DiskInfo> getDiskInfo();

    /**
     * 获取网络信息
     * @return 网络信息列表
     */
    java.util.List<SystemInfoDTO.NetworkInfo> getNetworkInfo();

    /**
     * 获取系统基本信息
     * @return 系统基本信息
     */
    SystemInfoDTO.SystemBasicInfo getSystemBasicInfo();

    /**
     * 获取实时网络速度
     * @param interfaceName 网络接口名称，为空则获取所有接口
     * @return 网络速度信息
     */
    java.util.List<SystemInfoDTO.NetworkInfo> getNetworkSpeed(String interfaceName);

    /**
     * 格式化字节大小
     * @param bytes 字节数
     * @return 格式化后的字符串
     */
    String formatBytes(long bytes);

    /**
     * 格式化速度
     * @param bytesPerSecond 每秒字节数
     * @return 格式化后的速度字符串
     */
    String formatSpeed(long bytesPerSecond);

    /**
     * 关闭系统
     * @return 操作结果
     */
    boolean shutdownSystem();

    /**
     * 重启系统
     * @return 操作结果
     */
    boolean restartSystem();
}