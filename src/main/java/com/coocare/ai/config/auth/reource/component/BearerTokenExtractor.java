package com.coocare.ai.config.auth.reource.component;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.security.oauth2.server.resource.BearerTokenError;
import org.springframework.security.oauth2.server.resource.BearerTokenErrors;
import org.springframework.security.oauth2.server.resource.web.BearerTokenResolver;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.util.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
/**
 * Bearer Token 提取器
 * 支持白名单接口在传入错误 Token 时放行、其余情况抛出标准异常
 *
 * <AUTHOR>
 * @since 2020-05-15
 */

public class BearerTokenExtractor implements BearerTokenResolver {

    private static final Pattern authorizationPattern = Pattern.compile("^Bearer (?<token>[a-zA-Z0-9-:._~+/]+=*)$",
            Pattern.CASE_INSENSITIVE);
    private final PathMatcher pathMatcher = new AntPathMatcher();
    private final PermitAllUrlProperties urlProperties;
    private final OAuth2AuthorizationService authorizationService;
    private boolean allowFormEncodedBodyParameter = false;
    private boolean allowUriQueryParameter = true;
    private String bearerTokenHeaderName = HttpHeaders.AUTHORIZATION;

    public BearerTokenExtractor(PermitAllUrlProperties urlProperties, OAuth2AuthorizationService authorizationService) {
        this.urlProperties = urlProperties;
        this.authorizationService = authorizationService;
    }

    private static String resolveFromRequestParameters(HttpServletRequest request) {
        String[] values = request.getParameterValues("access_token");
        if (values == null || values.length == 0) {
            return null;
        }
        if (values.length == 1) {
            return values[0];
        }
        BearerTokenError error = BearerTokenErrors.invalidRequest("Found multiple bearer tokens in the request");
        throw new OAuth2AuthenticationException(error);
    }

    @Override
    public String resolve(HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String relativePath = requestUri.substring(request.getContextPath().length());

        boolean match = urlProperties.getIgnoreUrls().stream().anyMatch(url -> pathMatcher.match(url, relativePath));

        final String parameterToken;
        try {
            final String authorizationHeaderToken = resolveFromAuthorizationHeader(request);
            parameterToken = isParameterTokenSupportedForRequest(request)
                    ? resolveFromRequestParameters(request) : null;
            if (authorizationHeaderToken != null) {
                // 白名单，如果有token，校验token有效性，找不到直接返回null
                if (match) {
                    OAuth2Authorization authorization = authorizationService.findByToken(authorizationHeaderToken, OAuth2TokenType.ACCESS_TOKEN);
                    if (null == authorization) {
                        return null;
                    }
                }
                if (parameterToken != null) {
                    final BearerTokenError error = BearerTokenErrors
                            .invalidRequest("Found multiple bearer tokens in the request");
                    throw new OAuth2AuthenticationException(error);
                }
                return authorizationHeaderToken;
            }
            if (parameterToken != null && isParameterTokenEnabledForRequest(request)) {
                return parameterToken;
            }
        } catch (OAuth2AuthenticationException e) {
            if (match) {
                return null;
            }
            throw e;
        }
        return null;
    }

    private String resolveFromAuthorizationHeader(HttpServletRequest request) {
        String authorization = request.getHeader(this.bearerTokenHeaderName);
        if (!StringUtils.startsWithIgnoreCase(authorization, "bearer")) {
            return null;
        }
        Matcher matcher = authorizationPattern.matcher(authorization);
        if (!matcher.matches()) {
            BearerTokenError error = BearerTokenErrors.invalidToken("Bearer token is malformed");
            throw new OAuth2AuthenticationException(error);
        }
        return matcher.group("token");
    }

    private boolean isParameterTokenSupportedForRequest(final HttpServletRequest request) {
        return (("POST".equals(request.getMethod())
                && MediaType.APPLICATION_FORM_URLENCODED_VALUE.equals(request.getContentType()))
                || "GET".equals(request.getMethod()));
    }

    private boolean isParameterTokenEnabledForRequest(final HttpServletRequest request) {
        return ((this.allowFormEncodedBodyParameter && "POST".equals(request.getMethod())
                && MediaType.APPLICATION_FORM_URLENCODED_VALUE.equals(request.getContentType()))
                || (this.allowUriQueryParameter && "GET".equals(request.getMethod())));
    }

}
