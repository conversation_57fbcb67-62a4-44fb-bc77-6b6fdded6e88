package com.coocare.ai.config.auth.support.email;

import cn.hutool.core.util.StrUtil;
import com.coocare.ai.config.auth.support.base.OAuth2ResourceOwnerBaseAuthenticationProvider;
import com.coocare.ai.config.constants.SecurityConstants;
import com.coocare.ai.config.exception.RRException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2ErrorCodes;
import org.springframework.security.oauth2.core.OAuth2Token;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenGenerator;

import java.util.Map;

/**
 * 邮箱验证码登录认证提供者
 *
 * <AUTHOR>
 */
public class OAuth2ResourceOwnerEmailAuthenticationProvider
        extends OAuth2ResourceOwnerBaseAuthenticationProvider<OAuth2ResourceOwnerEmailAuthenticationToken> {

    private static final Logger LOGGER = LogManager.getLogger(OAuth2ResourceOwnerEmailAuthenticationProvider.class);

    private final RedisTemplate<String, Object> redisTemplate;

    public OAuth2ResourceOwnerEmailAuthenticationProvider(AuthenticationManager authenticationManager,
                                                          OAuth2AuthorizationService authorizationService,
                                                          OAuth2TokenGenerator<? extends OAuth2Token> tokenGenerator,
                                                          RedisTemplate<String, Object> redisTemplate) {
        super(authenticationManager, authorizationService, tokenGenerator);
        this.redisTemplate = redisTemplate;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        boolean supports = OAuth2ResourceOwnerEmailAuthenticationToken.class.isAssignableFrom(authentication);
        LOGGER.debug("supports authentication=" + authentication + " returning " + supports);
        return supports;
    }

    @Override
    public void checkClient(RegisteredClient registeredClient) {
        assert registeredClient != null;
        if (!registeredClient.getAuthorizationGrantTypes()
                .contains(new AuthorizationGrantType(SecurityConstants.GRANT_TYPE_EMAIL_CODE))) {
            throw new OAuth2AuthenticationException(OAuth2ErrorCodes.UNAUTHORIZED_CLIENT);
        }
    }

    @Override
    public UsernamePasswordAuthenticationToken buildToken(Map<String, Object> reqParameters) {
        String email = (String) reqParameters.get(SecurityConstants.OAUTH_PARAMETER_NAME_EMAIL);
        String emailCode = (String) reqParameters.get(SecurityConstants.OAUTH_PARAMETER_NAME_EMAIL_CAPTCHA);

        // 验证邮箱验证码
        validateEmailCode(email, emailCode);

        // 构建认证token，使用邮箱作为用户名，验证码作为密码
        return new UsernamePasswordAuthenticationToken(email + "@EMAIL", null);
    }

    /**
     * 验证邮箱验证码
     *
     * @param email 邮箱
     * @param emailCode 验证码
     */
    private void validateEmailCode(String email, String emailCode) {
        if (StrUtil.isBlank(email) || StrUtil.isBlank(emailCode)) {
            throw new RRException("邮箱和验证码不能为空");
        }

        String cacheKey = SecurityConstants.EMAIL_CAPTCHA_PREFIX + email;
        String cachedCode = (String) redisTemplate.opsForValue().get(cacheKey);

        if (cachedCode == null) {
            throw new RRException("验证码已过期或不存在");
        }

        if (!emailCode.equals(cachedCode)) {
            throw new RRException("验证码错误");
        }

        // 验证成功后删除验证码
        redisTemplate.delete(cacheKey);
    }
}
